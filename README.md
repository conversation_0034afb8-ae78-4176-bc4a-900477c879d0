# TheInfini AI Backend

A robust, secure, and scalable backend for an AI chat application built with Node.js, TypeScript, and Langchain.

## Features

### 🔐 Authentication & Security
- **Multi-factor Authentication**: Login with email/mobile + password or OTP
- **JWT Token-based Authentication**: Secure session management
- **Password Encryption**: Bcrypt-based password hashing
- **CSRF Protection**: Request signing and CSRF token validation
- **Rate Limiting**: Configurable rate limits per user/IP
- **Input Sanitization**: XSS and injection attack prevention

### 💬 Chat System
- **User Chat**: Persistent chat history for authenticated users
- **Guest Chat**: Limited chat sessions (5 interactions) for non-authenticated users
- **LLM Integration**: Configurable LLM models via Langchain
- **Chat Management**: Create, delete, search, and export chats
- **Session Management**: Seamless guest-to-user chat conversion

### 🤖 LLM Integration
- **Multiple Providers**: OpenAI and Anthropic support
- **Model Factory**: Easy switching between different LLM models
- **Configurable Parameters**: Temperature, max tokens, custom prompts
- **Error Handling**: Robust LLM service error management

### 🗄️ Database Architecture
- **Dual Database Setup**: Separate databases for users and chats
- **Sequelize ORM**: Type-safe database operations with MySQL
- **Model Relationships**: Proper foreign key constraints and associations
- **Data Validation**: Comprehensive input validation and sanitization

### 📊 Monitoring & Logging
- **Winston Logging**: Structured logging with multiple transports
- **Error Tracking**: Comprehensive error handling and reporting
- **Performance Monitoring**: Request logging and timing
- **Health Checks**: System health and status endpoints

## Architecture

```
src/
├── config/          # Configuration files (database, JWT, logger)
├── controllers/     # Request handlers and business logic coordination
├── middleware/      # Authentication, validation, security, error handling
├── models/          # Database models and schemas
├── routes/          # API route definitions
├── services/        # Business logic and external service integrations
├── types/           # TypeScript type definitions
└── utils/           # Utility functions and constants
```

## Quick Start

### Prerequisites
- Node.js 18+ and npm
- MySQL 8.0+

### Installation

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd theinfini_ai_backend
npm install
```

2. **Environment setup**:
```bash
cp .env.example .env
# Edit .env with your configuration (especially API keys)
```

3. **Database setup**:
Run the provided SQL script as MySQL root user:
```bash
mysql -u root -p < setup-database.sql
```

Or manually create the databases:
```sql
CREATE DATABASE infini_ai_users;
CREATE DATABASE infini_ai_user_chat_recs;

-- Create users (adjust privileges as needed)
CREATE USER 'inf_ai_user'@'localhost' IDENTIFIED BY 'inf_ai_user';
CREATE USER 'inf_ai_chat_recs'@'localhost' IDENTIFIED BY 'inf_ai_chat_recs';

GRANT ALL PRIVILEGES ON infini_ai_users.* TO 'inf_ai_user'@'localhost';
GRANT ALL PRIVILEGES ON infini_ai_user_chat_recs.* TO 'inf_ai_chat_recs'@'localhost';
FLUSH PRIVILEGES;
```

4. **Start the application**:
```bash
# Development (with hot reload)
npm run dev

# Production build and start
npm run prod

# Just build
npm run build

# Start built application
npm start
```

The server will start on port 5529 by default. You can access:
- API Health Check: http://localhost:5529/api/health
- API Documentation: http://localhost:5529/api/version

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login with password or request OTP
- `POST /api/auth/verify-otp` - Verify OTP and login
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/change-password` - Change password

### Chat
- `POST /api/chat/message` - Send chat message (auth optional)
- `GET /api/chat/chats` - Get user chats (auth required)
- `GET /api/chat/chats/:id/messages` - Get chat messages
- `DELETE /api/chat/chats/:id` - Delete chat
- `GET /api/chat/models` - Get available LLM models

### User Management
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `GET /api/user/stats` - Get user statistics
- `GET /api/user/export` - Export user data

## Configuration

### Environment Variables

```bash
# Server
PORT=5529
NODE_ENV=development

# JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# Databases
USER_DB_HOST=localhost
USER_DB_NAME=infini_ai_users
USER_DB_USERNAME=inf_ai_user
USER_DB_PASSWORD=inf_ai_user

CHAT_DB_HOST=localhost
CHAT_DB_NAME=infini_ai_user_chat_recs
CHAT_DB_USERNAME=inf_ai_chat_recs
CHAT_DB_PASSWORD=inf_ai_chat_recs

# LLM APIs
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEFAULT_LLM_MODEL=gpt-3.5-turbo

# Security
CSRF_SECRET=your_csrf_secret_here
GUEST_CHAT_LIMIT=5
```

## Development

### Scripts
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Start production server
- `npm run prod` - Build and start production server

### Code Structure
- **Models**: Sequelize models with TypeScript classes
- **Controllers**: Request handling and response formatting
- **Services**: Business logic and external API integration
- **Middleware**: Authentication, validation, security
- **Routes**: API endpoint definitions

## Security Features

- **HTTPS Enforcement**: Security headers and HTTPS redirection
- **Input Validation**: Joi-based request validation
- **SQL Injection Prevention**: Sequelize ORM with parameterized queries
- **XSS Protection**: Input sanitization and CSP headers
- **Rate Limiting**: Configurable rate limits per endpoint
- **CSRF Protection**: Token-based CSRF protection
- **Password Security**: Bcrypt hashing with salt rounds

## Production Deployment

1. **Environment Setup**:
   - Set `NODE_ENV=production`
   - Configure production database credentials
   - Set secure JWT secrets
   - Configure LLM API keys

2. **Database Migration**:
   - Run database synchronization
   - Set up database backups
   - Configure connection pooling

3. **Security Hardening**:
   - Enable HTTPS
   - Configure firewall rules
   - Set up monitoring and alerting
   - Regular security updates

## License

This project is licensed under the MIT License.
