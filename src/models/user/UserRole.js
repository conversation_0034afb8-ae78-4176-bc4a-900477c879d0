import { DataTypes, Model, Op } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { User } from './User.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * UserRole Model
 * Manages user role assignments and role-based access control
 */
class UserRole extends Model {
  /**
   * Check if role assignment is active
   * @returns {boolean} True if role assignment is active
   */
  isActive() {
    return this.isActive === true;
  }

  /**
   * Deactivate role assignment
   * @returns {Promise<void>}
   */
  async deactivate() {
    this.isActive = false;
    await this.save();
  }

  /**
   * Activate role assignment
   * @returns {Promise<void>}
   */
  async activate() {
    this.isActive = true;
    await this.save();
  }

  /**
   * Create a new user role assignment
   * @param {Object} data - Role assignment data
   * @param {string} data.userId - User ID
   * @param {string} data.roleId - Role ID
   * @param {string} data.assignedBy - ID of user who assigned the role
   * @param {Date} [data.assignedAt] - Assignment date (optional, defaults to now)
   * @param {boolean} [data.isActive=true] - Whether assignment is active
   * @returns {Promise<UserRole>} Created user role assignment
   */
  static async createUserRole(data) {
    return UserRole.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      roleId: data.roleId,
      assignedBy: data.assignedBy,
      assignedAt: data.assignedAt || new Date(),
      isActive: data.isActive !== undefined ? data.isActive : true,
    });
  }

  /**
   * Get active roles for a user
   * @param {string} userId - User ID
   * @returns {Promise<UserRole[]>} Array of active user roles
   */
  static async getActiveUserRoles(userId) {
    return UserRole.findAll({
      where: {
        userId,
        isActive: true,
      },
      order: [['assignedAt', 'DESC']],
    });
  }

  /**
   * Get all roles for a user (active and inactive)
   * @param {string} userId - User ID
   * @returns {Promise<UserRole[]>} Array of all user roles
   */
  static async getAllUserRoles(userId) {
    return UserRole.findAll({
      where: {
        userId,
      },
      order: [['assignedAt', 'DESC']],
    });
  }

  /**
   * Check if user has a specific role
   * @param {string} userId - User ID
   * @param {string} roleId - Role ID
   * @returns {Promise<boolean>} True if user has the role
   */
  static async userHasRole(userId, roleId) {
    const userRole = await UserRole.findOne({
      where: {
        userId,
        roleId,
        isActive: true,
      },
    });
    return !!userRole;
  }

  /**
   * Remove role from user (deactivate)
   * @param {string} userId - User ID
   * @param {string} roleId - Role ID
   * @returns {Promise<boolean>} True if role was removed
   */
  static async removeUserRole(userId, roleId) {
    const userRole = await UserRole.findOne({
      where: {
        userId,
        roleId,
        isActive: true,
      },
    });

    if (userRole) {
      await userRole.deactivate();
      return true;
    }
    return false;
  }

  /**
   * Get users with a specific role
   * @param {string} roleId - Role ID
   * @param {boolean} [activeOnly=true] - Whether to include only active assignments
   * @returns {Promise<UserRole[]>} Array of user role assignments
   */
  static async getUsersWithRole(roleId, activeOnly = true) {
    const whereClause = { roleId };
    if (activeOnly) {
      whereClause.isActive = true;
    }

    return UserRole.findAll({
      where: whereClause,
      order: [['assignedAt', 'DESC']],
    });
  }

  /**
   * Get role assignment history for a user
   * @param {string} userId - User ID
   * @param {number} [limit=50] - Maximum number of records to return
   * @returns {Promise<UserRole[]>} Array of role assignments
   */
  static async getRoleHistory(userId, limit = 50) {
    return UserRole.findAll({
      where: { userId },
      order: [['assignedAt', 'DESC']],
      limit,
    });
  }
}

UserRole.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      // Note: Foreign key reference will be set up in associations
    },
    roleId: {
      type: DataTypes.UUID,
      allowNull: false,
      // Note: Foreign key reference will be set up in associations
    },
    assignedBy: {
      type: DataTypes.UUID,
      allowNull: false,
      // Note: Foreign key reference will be set up in associations
    },
    assignedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserRole',
    tableName: 'user_roles',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['role_id'],
      },
      {
        fields: ['assigned_by'],
      },
      {
        fields: ['assigned_at'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
      {
        fields: ['user_id', 'role_id'],
      },
      {
        fields: ['user_id', 'is_active'],
      },
    ],
  }
);

export { UserRole };
