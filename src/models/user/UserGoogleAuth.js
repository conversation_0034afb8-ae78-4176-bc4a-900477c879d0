import { DataTypes, Model } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * UserGoogleAuth Model
 * Stores Google OAuth authentication tokens and user consent information
 */
class UserGoogleAuth extends Model {
  /**
   * Check if the access token is expired
   * @returns {boolean} True if token is expired
   */
  isAccessTokenExpired() {
    if (!this.accessTokenExpiresAt) return true;
    return new Date() >= this.accessTokenExpiresAt;
  }

  /**
   * Check if the refresh token is expired
   * @returns {boolean} True if refresh token is expired
   */
  isRefreshTokenExpired() {
    if (!this.refreshTokenExpiresAt) return false; // Refresh tokens may not have expiry
    return new Date() >= this.refreshTokenExpiresAt;
  }

  /**
   * Check if user has granted specific scope
   * @param {string} scope - OAuth scope to check
   * @returns {boolean} True if scope is granted
   */
  hasScope(scope) {
    if (!this.grantedScopes) return false;
    const scopes = this.grantedScopes.split(' ');
    return scopes.includes(scope);
  }

  /**
   * Check if user has granted all required scopes
   * @param {string[]} requiredScopes - Array of required scopes
   * @returns {boolean} True if all scopes are granted
   */
  hasAllScopes(requiredScopes) {
    return requiredScopes.every(scope => this.hasScope(scope));
  }

  /**
   * Convert to JSON (excludes sensitive tokens)
   * @returns {Object} Safe user data without tokens
   */
  toJSON() {
    const values = { ...this.get() };
    delete values.accessToken;
    delete values.refreshToken;
    return values;
  }

  /**
   * Convert to JSON with tokens (for internal use only)
   * @returns {Object} Complete user data with tokens
   */
  toJSONWithTokens() {
    return { ...this.get() };
  }

  /**
   * Find Google auth by user ID
   * @param {string} userId - User ID
   * @returns {Promise<UserGoogleAuth|null>} Google auth record
   */
  static async findByUserId(userId) {
    return this.findOne({
      where: { userId }
    });
  }

  /**
   * Create or update Google auth for user
   * @param {Object} authData - Google auth data
   * @param {string} authData.userId - User ID
   * @param {string} authData.accessToken - Access token
   * @param {string} authData.refreshToken - Refresh token
   * @param {Date} authData.accessTokenExpiresAt - Access token expiry
   * @param {Date} [authData.refreshTokenExpiresAt] - Refresh token expiry
   * @param {string} authData.grantedScopes - Granted scopes
   * @param {Object} [authData.userInfo] - Google user info
   * @returns {Promise<UserGoogleAuth>} Created or updated auth record
   */
  static async createOrUpdate(authData) {
    const {
      userId,
      accessToken,
      refreshToken,
      accessTokenExpiresAt,
      refreshTokenExpiresAt,
      grantedScopes,
      userInfo
    } = authData;

    const existingAuth = await this.findByUserId(userId);

    if (existingAuth) {
      // Update existing record
      await existingAuth.update({
        accessToken,
        refreshToken: refreshToken || existingAuth.refreshToken, // Keep existing if not provided
        accessTokenExpiresAt,
        refreshTokenExpiresAt: refreshTokenExpiresAt || existingAuth.refreshTokenExpiresAt,
        grantedScopes,
        googleUserInfo: userInfo || existingAuth.googleUserInfo,
        lastAuthenticatedAt: new Date(),
        isActive: true
      });
      return existingAuth;
    } else {
      // Create new record
      return this.create({
        id: EncryptionUtil.generateUUID(),
        userId,
        accessToken,
        refreshToken,
        accessTokenExpiresAt,
        refreshTokenExpiresAt,
        grantedScopes,
        googleUserInfo: userInfo,
        lastAuthenticatedAt: new Date(),
        isActive: true
      });
    }
  }

  /**
   * Revoke Google authentication for user
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} True if revoked successfully
   */
  static async revokeAuth(userId) {
    const auth = await this.findByUserId(userId);
    if (!auth) return false;

    await auth.update({
      accessToken: null,
      refreshToken: null,
      accessTokenExpiresAt: null,
      refreshTokenExpiresAt: null,
      grantedScopes: null,
      isActive: false,
      revokedAt: new Date()
    });

    return true;
  }
}

UserGoogleAuth.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    accessToken: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Google OAuth access token'
    },
    refreshToken: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Google OAuth refresh token'
    },
    accessTokenExpiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Access token expiration timestamp'
    },
    refreshTokenExpiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Refresh token expiration timestamp (if applicable)'
    },
    grantedScopes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Space-separated list of granted OAuth scopes'
    },
    googleUserInfo: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Google user profile information'
    },
    lastAuthenticatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Last successful authentication timestamp'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether the authentication is active'
    },
    revokedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Timestamp when authentication was revoked'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserGoogleAuth',
    tableName: 'user_google_auth',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['last_authenticated_at'],
      },
    ],
  }
);

export { UserGoogleAuth };
