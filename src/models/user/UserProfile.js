import { DataTypes, Model  } from 'sequelize';
import { userDatabase  } from '../../config/database.js';
import { User } from './User.js';
import { EncryptionUtil  } from '../../utils/encryption.js';

/**
 * UserProfile Model
 * Manages user profile information and subscription plans
 */
class UserProfile extends Model {
  /**
   * Get user's full name
   * @returns {string} Full name or 'User' if not available
   */
  getFullName() {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    return this.firstName || this.lastName || 'User';
  }

  /**
   * Check if user has premium plan
   * @returns {boolean} True if user has premium plan (Creator or Pro)
   */
  isPremium() {
    return this.plan === 'CREATOR' || this.plan === 'PRO';
  }

  /**
   * Check if user has pro plan
   * @returns {boolean} True if user has pro plan
   */
  isPro() {
    return this.plan === 'PRO';
  }

  /**
   * Check if user has free plan
   * @returns {boolean} True if user has free plan
   */
  isFree() {
    return this.plan === 'EXPLORER';
  }

  /**
   * Update user profile
   * @param {Object} data - Profile update data
   * @param {string} [data.firstName] - First name (optional)
   * @param {string} [data.lastName] - Last name (optional)
   * @param {string} [data.profilePicture] - Profile picture URL (optional)
   * @param {'FREE'|'PREMIUM'|'ENTERPRISE'} [data.plan] - User plan (optional)
   * @returns {Promise<void>}
   */
  async updateProfile(data) {
    if (data.firstName !== undefined) this.firstName = data.firstName;
    if (data.lastName !== undefined) this.lastName = data.lastName;
    if (data.profilePicture !== undefined) this.profilePicture = data.profilePicture;
    if (data.plan !== undefined) this.plan = data.plan;
    await this.save();
  }

  /**
   * Create user profile
   * @param {Object} data - Profile data
   * @param {string} data.userId - User ID
   * @param {string} [data.firstName] - First name (optional)
   * @param {string} [data.lastName] - Last name (optional)
   * @param {string} [data.profilePicture] - Profile picture URL (optional)
   * @param {'EXPLORER'|'CREATOR'|'PRO'} [data.plan='EXPLORER'] - User plan
   * @returns {Promise<UserProfile>} Created profile
   */
  static async createUserProfile(data) {

    return UserProfile.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      firstName: data.firstName,
      lastName: data.lastName,
      profilePicture: data.profilePicture,
      plan: data.plan || 'EXPLORER',
    });
  }

  /**
   * Find profile by user ID
   * @param {string} userId - User ID
   * @returns {Promise<UserProfile|null>} Profile or null
   */
  static async findByUserId(userId) {
    return UserProfile.findOne({
      where: { userId },
    });
  }

  /**
   * Find or create profile for user
   * @param {string} userId - User ID
   * @param {Object} [defaults] - Default values if creating
   * @param {string} [defaults.firstName] - First name (optional)
   * @param {string} [defaults.lastName] - Last name (optional)
   * @param {string} [defaults.profilePicture] - Profile picture URL (optional)
   * @param {'EXPLORER'|'CREATOR'|'PRO'} [defaults.plan='EXPLORER'] - User plan
   * @returns {Promise<[UserProfile, boolean]>} Tuple of [profile, wasCreated]
   */
  static async findOrCreateByUserId(userId, defaults = {}) {

    const [userProfile, created] = await UserProfile.findOrCreate({
      where: { userId },
      defaults: {
        id: EncryptionUtil.generateUUID(),
        userId,
        firstName: defaults.firstName,
        lastName: defaults.lastName,
        profilePicture: defaults.profilePicture,
        plan: defaults.plan || 'EXPLORER',
      },
    });
    return [userProfile, created];
  }
}

UserProfile.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      // Note: Foreign key reference will be set up in associations
      unique: true, // One profile per user
    },
    firstName: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    lastName: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    profilePicture: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    plan: {
      type: DataTypes.ENUM('EXPLORER', 'CREATOR', 'PRO'),
      allowNull: false,
      defaultValue: 'EXPLORER',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserProfile',
    tableName: 'user_profiles',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id'],
      },
      {
        fields: ['plan'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

// Define associations
User.hasOne(UserProfile, { foreignKey: 'user_id', as: 'profile' });
UserProfile.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export { UserProfile  };
