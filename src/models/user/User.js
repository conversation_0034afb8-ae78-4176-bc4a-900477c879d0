import { DataTypes, Model, Op  } from 'sequelize';
import { userDatabase  } from '../../config/database.js';

import { EncryptionUtil  } from '../../utils/encryption.js';
import { VALIDATION_RULES  } from '../../utils/constants.js';

/**
 * User Model
 * Represents a user in the system with authentication and profile information
 */
class User extends Model {
  /**
   * Set user password (hashed)
   * @param {string} password - Plain text password
   * @returns {Promise<void>}
   */
  async setPassword(password) {
    this.password = await EncryptionUtil.hashPassword(password);
  }

  /**
   * Validate user password
   * @param {string} password - Plain text password to validate
   * @returns {Promise<boolean>} True if password is valid
   */
  async validatePassword(password) {
    if (!this.password) return false;
    return EncryptionUtil.comparePassword(password, this.password);
  }

  /**
   * Mark user as verified
   * @returns {Promise<void>}
   */
  async markAsVerified() {
    this.isVerified = true;
    await this.save();
  }

  /**
   * Convert to JSON (excludes password)
   * @returns {Object} User data without password
   */
  toJSON() {
    const values = { ...this.get() };
    delete values.password; // Never return password in JSON
    return values;
  }

  /**
   * Find user by email or mobile number
   * @param {string} identifier - Email or mobile number
   * @returns {Promise<User|null>} User instance or null
   */
  static async findByEmailOrMobile(identifier) {

    const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(identifier);
    const isMobile = VALIDATION_RULES.MOBILE_REGEX.test(identifier);

    if (!isEmail && !isMobile) {
      return null;
    }

    return User.findOne({
      where: isEmail ? { email: identifier } : { mobile: identifier },
    });
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @param {string} [userData.email] - User email (optional)
   * @param {string} [userData.mobile] - User mobile (optional)
   * @param {string} [userData.password] - User password (optional)
   * @param {boolean} [userData.isVerified=false] - Whether user is verified
   * @returns {Promise<User>} Created user instance
   */
  static async createUser(userData) {

    const user = new User({
      id: EncryptionUtil.generateUUID(),
      email: userData.email,
      mobile: userData.mobile,
      isActive: true,
      isVerified: userData.isVerified ?? false,
    });

    if (userData.password) {
      await user.setPassword(userData.password);
    }

    return user.save();
  }
}

User.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true,
      validate: {
        isEmail: true,
      },
    },
    mobile: {
      type: DataTypes.STRING(20),
      allowNull: true,
      unique: true,
      validate: {
        is: /^[+]?[1-9][\d]{7,14}$/,
      },
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    isVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['email'],
      },
      {
        unique: true,
        fields: ['mobile'],
      },
    ],
    validate: {
      emailOrMobileRequired() {
        if (!this.email && !this.mobile) {
          throw new Error('Either email or mobile number is required');
        }
      },
    },
  }
);

export { User  };
