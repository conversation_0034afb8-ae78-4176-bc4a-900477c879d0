import { DataTypes, Model, Op } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * Role Model
 * Manages system roles and their permissions
 */
class Role extends Model {
  /**
   * Check if role is active
   * @returns {boolean} True if role is active
   */
  isActive() {
    return this.isActive === true;
  }

  /**
   * Deactivate role
   * @returns {Promise<void>}
   */
  async deactivate() {
    this.isActive = false;
    await this.save();
  }

  /**
   * Activate role
   * @returns {Promise<void>}
   */
  async activate() {
    this.isActive = true;
    await this.save();
  }

  /**
   * Get permissions as array
   * @returns {Array} Array of permissions
   */
  getPermissions() {
    if (!this.permissions) return [];
    return Array.isArray(this.permissions) ? this.permissions : [];
  }

  /**
   * Check if role has specific permission
   * @param {string} permission - Permission to check
   * @returns {boolean} True if role has the permission
   */
  hasPermission(permission) {
    const permissions = this.getPermissions();
    return permissions.includes(permission);
  }

  /**
   * Add permission to role
   * @param {string} permission - Permission to add
   * @returns {Promise<void>}
   */
  async addPermission(permission) {
    const permissions = this.getPermissions();
    if (!permissions.includes(permission)) {
      permissions.push(permission);
      this.permissions = permissions;
      await this.save();
    }
  }

  /**
   * Remove permission from role
   * @param {string} permission - Permission to remove
   * @returns {Promise<void>}
   */
  async removePermission(permission) {
    const permissions = this.getPermissions();
    const index = permissions.indexOf(permission);
    if (index > -1) {
      permissions.splice(index, 1);
      this.permissions = permissions;
      await this.save();
    }
  }

  /**
   * Set permissions for role
   * @param {Array} permissions - Array of permissions
   * @returns {Promise<void>}
   */
  async setPermissions(permissions) {
    this.permissions = Array.isArray(permissions) ? permissions : [];
    await this.save();
  }

  /**
   * Create a new role
   * @param {Object} data - Role data
   * @param {string} data.name - Role name (unique)
   * @param {string} [data.description] - Role description (optional)
   * @param {Array} [data.permissions=[]] - Array of permissions (optional)
   * @param {boolean} [data.isActive=true] - Whether role is active
   * @returns {Promise<Role>} Created role
   */
  static async createRole(data) {
    return Role.create({
      id: EncryptionUtil.generateUUID(),
      name: data.name,
      description: data.description,
      permissions: data.permissions || [],
      isActive: data.isActive !== undefined ? data.isActive : true,
    });
  }

  /**
   * Find role by name
   * @param {string} name - Role name
   * @returns {Promise<Role|null>} Role instance or null
   */
  static async findByName(name) {
    return Role.findOne({
      where: { name },
    });
  }

  /**
   * Get all active roles
   * @returns {Promise<Role[]>} Array of active roles
   */
  static async getActiveRoles() {
    return Role.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']],
    });
  }

  /**
   * Get all roles (active and inactive)
   * @returns {Promise<Role[]>} Array of all roles
   */
  static async getAllRoles() {
    return Role.findAll({
      order: [['name', 'ASC']],
    });
  }

  /**
   * Find roles with specific permission
   * @param {string} permission - Permission to search for
   * @param {boolean} [activeOnly=true] - Whether to include only active roles
   * @returns {Promise<Role[]>} Array of roles with the permission
   */
  static async findRolesWithPermission(permission, activeOnly = true) {
    const whereClause = {
      permissions: {
        [Op.like]: `%"${permission}"%`,
      },
    };

    if (activeOnly) {
      whereClause.isActive = true;
    }

    return Role.findAll({
      where: whereClause,
      order: [['name', 'ASC']],
    });
  }

  /**
   * Get role statistics
   * @returns {Promise<Object>} Role statistics
   */
  static async getRoleStats() {
    const [totalRoles, activeRoles] = await Promise.all([
      Role.count(),
      Role.count({ where: { isActive: true } }),
    ]);

    return {
      totalRoles,
      activeRoles,
      inactiveRoles: totalRoles - activeRoles,
    };
  }

  /**
   * Search roles by name or description
   * @param {string} searchTerm - Search term
   * @param {boolean} [activeOnly=true] - Whether to include only active roles
   * @returns {Promise<Role[]>} Array of matching roles
   */
  static async searchRoles(searchTerm, activeOnly = true) {
    const whereClause = {
      [Op.or]: [
        { name: { [Op.like]: `%${searchTerm}%` } },
        { description: { [Op.like]: `%${searchTerm}%` } },
      ],
    };

    if (activeOnly) {
      whereClause.isActive = true;
    }

    return Role.findAll({
      where: whereClause,
      order: [['name', 'ASC']],
    });
  }
}

Role.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [1, 50],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    permissions: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'Role',
    tableName: 'roles',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['name'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

export { Role };
