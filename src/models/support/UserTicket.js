import { DataTypes, Model } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { User } from '../user/User.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * UserTicket Model
 * Represents customer support tickets submitted by users
 */
class UserTicket extends Model {
  /**
   * Create a new support ticket
   * @param {Object} data - Ticket data
   * @param {string} data.userId - User ID
   * @param {string} data.subject - Ticket subject
   * @param {string} data.description - Ticket description
   * @param {string} [data.attachmentUrl] - Optional attachment URL
   * @param {string} [data.attachmentName] - Optional attachment original name
   * @param {string} [data.attachmentType] - Optional attachment MIME type
   * @param {number} [data.attachmentSize] - Optional attachment size in bytes
   * @param {string} [data.attachmentSecureId] - Optional attachment secure ID
   * @param {string} [data.attachmentS3Url] - Optional S3 URL
   * @param {string} [data.attachmentS3Key] - Optional S3 key
   * @param {string} [data.attachmentStorageType] - Optional storage type
   * @param {string} [data.techDetails] - Optional technical details
   * @param {'LOW'|'MEDIUM'|'HIGH'|'URGENT'} data.priority - Ticket priority
   * @returns {Promise<UserTicket>} Created ticket
   */
  static async createTicket(data) {
    return UserTicket.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      subject: data.subject,
      description: data.description,
      attachmentUrl: data.attachmentUrl,
      attachmentName: data.attachmentName,
      attachmentType: data.attachmentType,
      attachmentSize: data.attachmentSize,
      attachmentSecureId: data.attachmentSecureId,
      attachmentS3Url: data.attachmentS3Url,
      attachmentS3Key: data.attachmentS3Key,
      attachmentStorageType: data.attachmentStorageType,
      techDetails: data.techDetails,
      priority: data.priority,
      status: 'OPEN',
    });
  }

  /**
   * Format ticket data for API response, hiding sensitive attachment information
   * @param {Object} ticketData - Raw ticket data
   * @returns {Object} Formatted ticket data
   */
  static formatForResponse(ticketData) {
    // Convert to plain object if it's a Sequelize instance
    const ticket = ticketData.toJSON ? ticketData.toJSON() : { ...ticketData };

    // If there's an attachment, replace sensitive S3 info with secure file access
    if (ticket.attachmentSecureId) {
      // Remove ALL sensitive information
      delete ticket.attachmentUrl;
      delete ticket.attachmentS3Url;
      delete ticket.attachmentS3Key;

      // Add secure file access information with direct API path
      ticket.attachment = {
        fileId: ticket.attachmentSecureId,
        name: ticket.attachmentName,
        type: ticket.attachmentType,
        size: ticket.attachmentSize,
        url: `/files/${ticket.attachmentSecureId}` // Direct API path
      };

      // Remove individual attachment fields
      delete ticket.attachmentSecureId;
      delete ticket.attachmentName;
      delete ticket.attachmentType;
      delete ticket.attachmentSize;
      delete ticket.attachmentStorageType;
    } else if (ticket.attachmentName) {
      // Handle legacy tickets without secure ID
      delete ticket.attachmentUrl;
      delete ticket.attachmentS3Url;
      delete ticket.attachmentS3Key;

      ticket.attachment = {
        name: ticket.attachmentName,
        type: ticket.attachmentType,
        size: ticket.attachmentSize,
        url: null // No secure access available for legacy files
      };

      // Remove individual attachment fields
      delete ticket.attachmentName;
      delete ticket.attachmentType;
      delete ticket.attachmentSize;
      delete ticket.attachmentStorageType;
    }

    return ticket;
  }

  /**
   * Find tickets by user ID
   * @param {string} userId - User ID
   * @param {Object} [options] - Query options
   * @param {number} [options.limit=10] - Limit number of results
   * @param {number} [options.offset=0] - Offset for pagination
   * @param {string} [options.status] - Filter by status
   * @param {string} [options.priority] - Filter by priority
   * @returns {Promise<{tickets: UserTicket[], total: number}>} Tickets and total count
   */
  static async findByUserId(userId, options = {}) {
    const {
      limit = 10,
      offset = 0,
      status,
      priority
    } = options;

    const whereClause = { userId };
    
    if (status) {
      whereClause.status = status;
    }
    
    if (priority) {
      whereClause.priority = priority;
    }

    const { count, rows } = await UserTicket.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'mobile'],
        }
      ]
    });

    return {
      tickets: rows,
      total: count
    };
  }

  /**
   * Find ticket by ID and user ID
   * @param {string} ticketId - Ticket ID
   * @param {string} userId - User ID
   * @returns {Promise<UserTicket|null>} Ticket or null
   */
  static async findByIdAndUserId(ticketId, userId) {
    return UserTicket.findOne({
      where: {
        id: ticketId,
        userId
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'mobile'],
        }
      ]
    });
  }

  /**
   * Update ticket status
   * @param {string} ticketId - Ticket ID
   * @param {'OPEN'|'IN_PROGRESS'|'RESOLVED'|'CLOSED'} status - New status
   * @returns {Promise<[number, UserTicket[]]>} Update result
   */
  static async updateStatus(ticketId, status) {
    return UserTicket.update(
      { status },
      {
        where: { id: ticketId },
        returning: true
      }
    );
  }

  /**
   * Get ticket statistics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Ticket statistics
   */
  static async getTicketStats(userId) {
    const totalTickets = await UserTicket.count({
      where: { userId }
    });

    const openTickets = await UserTicket.count({
      where: { userId, status: 'OPEN' }
    });

    const resolvedTickets = await UserTicket.count({
      where: { userId, status: 'RESOLVED' }
    });

    const urgentTickets = await UserTicket.count({
      where: { userId, priority: 'URGENT' }
    });

    return {
      total: totalTickets,
      open: openTickets,
      resolved: resolvedTickets,
      urgent: urgentTickets
    };
  }

  /**
   * Get all tickets (admin function)
   * @param {Object} [options] - Query options
   * @param {number} [options.limit=20] - Limit number of results
   * @param {number} [options.offset=0] - Offset for pagination
   * @param {string} [options.status] - Filter by status
   * @param {string} [options.priority] - Filter by priority
   * @returns {Promise<{tickets: UserTicket[], total: number}>} Tickets and total count
   */
  static async getAllTickets(options = {}) {
    const {
      limit = 20,
      offset = 0,
      status,
      priority
    } = options;

    const whereClause = {};
    
    if (status) {
      whereClause.status = status;
    }
    
    if (priority) {
      whereClause.priority = priority;
    }

    const { count, rows } = await UserTicket.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'mobile'],
        }
      ]
    });

    return {
      tickets: rows,
      total: count
    };
  }
}

UserTicket.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      // Foreign key reference will be set up in associations
    },
    subject: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 5000]
      }
    },
    attachmentUrl: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    attachmentName: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    attachmentType: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    attachmentSize: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    attachmentSecureId: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    attachmentS3Url: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    attachmentS3Key: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    attachmentStorageType: {
      type: DataTypes.ENUM('local', 's3'),
      allowNull: true,
    },
    techDetails: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    priority: {
      type: DataTypes.ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT'),
      allowNull: false,
      defaultValue: 'MEDIUM',
    },
    status: {
      type: DataTypes.ENUM('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'),
      allowNull: false,
      defaultValue: 'OPEN',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserTicket',
    tableName: 'user_tickets',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['priority'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
      {
        fields: ['user_id', 'status'],
      },
      {
        fields: ['user_id', 'priority'],
      },
      {
        fields: ['attachment_secure_id'],
      },
      {
        fields: ['attachment_s3_key'],
      },
      {
        fields: ['attachment_storage_type'],
      },
    ],
  }
);

// Define associations
User.hasMany(UserTicket, { foreignKey: 'user_id', as: 'tickets' });
UserTicket.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export { UserTicket };
