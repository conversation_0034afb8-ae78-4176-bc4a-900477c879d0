import { DataTypes, Model } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * CreditReset Model
 * Tracks credit reset cycles for different subscription plans
 */
class CreditReset extends Model {
  /**
   * Check if reset is due
   * @returns {boolean} True if reset is due
   */
  isResetDue() {
    return new Date() >= new Date(this.nextResetDate);
  }

  /**
   * Calculate next reset date based on cycle type
   * @param {string} cycleType - Reset cycle type (WEEKLY, MONTHLY)
   * @param {Date} fromDate - Date to calculate from
   * @returns {Date} Next reset date
   */
  static calculateNextResetDate(cycleType, fromDate = new Date()) {
    const date = new Date(fromDate);
    
    switch (cycleType) {
      case 'WEEKLY':
        date.setDate(date.getDate() + 7);
        break;
      case 'MONTHLY':
        date.setMonth(date.getMonth() + 1);
        break;
      default:
        throw new Error(`Unsupported cycle type: ${cycleType}`);
    }
    
    return date;
  }

  /**
   * Update next reset date
   * @returns {Promise<void>}
   */
  async updateNextResetDate() {
    this.lastResetDate = new Date();
    this.nextResetDate = CreditReset.calculateNextResetDate(this.resetCycle, this.lastResetDate);
    this.resetCount += 1;
    await this.save();
  }

  /**
   * Create credit reset record
   * @param {Object} resetData - Reset data
   * @returns {Promise<CreditReset>} Created reset record
   */
  static async createCreditReset(resetData) {
    const nextResetDate = this.calculateNextResetDate(
      resetData.resetCycle,
      resetData.lastResetDate || new Date()
    );

    return CreditReset.create({
      id: EncryptionUtil.generateUUID(),
      nextResetDate,
      ...resetData,
    });
  }

  /**
   * Find credit reset by user ID
   * @param {string} userId - User ID
   * @returns {Promise<CreditReset|null>} Credit reset record or null
   */
  static async findByUserId(userId) {
    return CreditReset.findOne({
      where: { userId },
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * Find all users due for credit reset
   * @param {string} resetCycle - Reset cycle type
   * @returns {Promise<Array>} Users due for reset
   */
  static async findUsersForReset(resetCycle) {
    return CreditReset.findAll({
      where: {
        resetCycle,
        nextResetDate: {
          [DataTypes.Op?.lte || '$lte']: new Date(),
        },
      },
    });
  }

  /**
   * Find or create credit reset for user
   * @param {string} userId - User ID
   * @param {string} resetCycle - Reset cycle type
   * @param {number} creditAmount - Credit amount to reset to
   * @returns {Promise<[CreditReset, boolean]>} Tuple of [resetRecord, wasCreated]
   */
  static async findOrCreateByUserId(userId, resetCycle, creditAmount) {
    const [creditReset, created] = await CreditReset.findOrCreate({
      where: { userId },
      defaults: {
        id: EncryptionUtil.generateUUID(),
        userId,
        resetCycle,
        creditAmount,
        lastResetDate: new Date(),
        nextResetDate: this.calculateNextResetDate(resetCycle),
        resetCount: 0,
      },
    });
    return [creditReset, created];
  }
}

CreditReset.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      unique: true, // One reset record per user
    },
    resetCycle: {
      type: DataTypes.ENUM('WEEKLY', 'MONTHLY'),
      allowNull: false,
    },
    creditAmount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Amount of credits to reset to',
    },
    lastResetDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    nextResetDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    resetCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Number of times credits have been reset',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'CreditReset',
    tableName: 'credit_resets',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id'],
      },
      {
        fields: ['reset_cycle'],
      },
      {
        fields: ['next_reset_date'],
      },
      {
        fields: ['is_active'],
      },
    ],
  }
);

export { CreditReset };
