import { DataTypes, Model } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * PaymentTransaction Model
 * Records all payment transactions for subscriptions and one-time purchases
 */
class PaymentTransaction extends Model {
  /**
   * Check if transaction is successful
   * @returns {boolean} True if transaction is successful
   */
  isSuccessful() {
    return this.status === 'SUCCESS';
  }

  /**
   * Check if transaction is pending
   * @returns {boolean} True if transaction is pending
   */
  isPending() {
    return this.status === 'PENDING';
  }

  /**
   * Check if transaction failed
   * @returns {boolean} True if transaction failed
   */
  isFailed() {
    return this.status === 'FAILED';
  }

  /**
   * Mark transaction as successful
   * @param {string} razorpayPaymentId - Razorpay payment ID
   * @param {Object} paymentDetails - Additional payment details
   * @returns {Promise<void>}
   */
  async markAsSuccessful(razorpayPaymentId, paymentDetails = {}) {
    this.status = 'SUCCESS';
    this.razorpayPaymentId = razorpayPaymentId;
    this.paidAt = new Date();
    this.paymentDetails = {
      ...this.paymentDetails,
      ...paymentDetails,
    };
    await this.save();
  }

  /**
   * Mark transaction as failed
   * @param {string} failureReason - Failure reason
   * @param {Object} errorDetails - Error details
   * @returns {Promise<void>}
   */
  async markAsFailed(failureReason, errorDetails = {}) {
    this.status = 'FAILED';
    this.failureReason = failureReason;
    this.paymentDetails = {
      ...this.paymentDetails,
      error: errorDetails,
    };
    await this.save();
  }

  /**
   * Create payment transaction
   * @param {Object} transactionData - Transaction data
   * @returns {Promise<PaymentTransaction>} Created transaction
   */
  static async createTransaction(transactionData) {
    return PaymentTransaction.create({
      id: EncryptionUtil.generateUUID(),
      ...transactionData,
    });
  }

  /**
   * Find transaction by Razorpay order ID
   * @param {string} razorpayOrderId - Razorpay order ID
   * @returns {Promise<PaymentTransaction|null>} Transaction or null
   */
  static async findByRazorpayOrderId(razorpayOrderId) {
    return PaymentTransaction.findOne({
      where: { razorpayOrderId },
    });
  }

  /**
   * Find transaction by Razorpay payment ID
   * @param {string} razorpayPaymentId - Razorpay payment ID
   * @returns {Promise<PaymentTransaction|null>} Transaction or null
   */
  static async findByRazorpayPaymentId(razorpayPaymentId) {
    return PaymentTransaction.findOne({
      where: { razorpayPaymentId },
    });
  }

  /**
   * Get user's payment history
   * @param {string} userId - User ID
   * @param {number} limit - Limit results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} Payment transactions
   */
  static async getUserPaymentHistory(userId, limit = 10, offset = 0) {
    return PaymentTransaction.findAll({
      where: { userId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });
  }
}

PaymentTransaction.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    subscriptionId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'user_subscriptions',
        key: 'id',
      },
    },
    planId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'subscription_plans',
        key: 'id',
      },
    },
    transactionType: {
      type: DataTypes.ENUM('SUBSCRIPTION', 'ADDON', 'RENEWAL', 'UPGRADE', 'DOWNGRADE'),
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING(3),
      allowNull: false,
      defaultValue: 'INR',
    },
    status: {
      type: DataTypes.ENUM('PENDING', 'SUCCESS', 'FAILED', 'REFUNDED'),
      allowNull: false,
      defaultValue: 'PENDING',
    },
    razorpayOrderId: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true,
    },
    razorpayPaymentId: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true,
    },
    razorpaySignature: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    paymentMethod: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    paidAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    failureReason: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    paymentDetails: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional payment details from Razorpay',
    },
    invoiceId: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: 'Reference to generated invoice',
    },
    invoiceNumber: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Invoice number for reference',
    },
    invoiceUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: 'URL to download invoice',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'PaymentTransaction',
    tableName: 'payment_transactions',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['subscription_id'],
      },
      {
        fields: ['plan_id'],
      },
      {
        unique: true,
        fields: ['razorpay_order_id'],
      },
      {
        unique: true,
        fields: ['razorpay_payment_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['transaction_type'],
      },
      {
        fields: ['created_at'],
      },
    ],
  }
);

export { PaymentTransaction };
