import { Model, DataTypes, Op } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * UserAddon Model
 * Tracks user addon purchases and their remaining limits
 */
class UserAddon extends Model {
  /**
   * Check if addon has remaining credits
   * @returns {boolean} True if addon has credits remaining
   */
  hasRemainingCredits() {
    return this.remainingCredits > 0;
  }

  /**
   * Check if addon has remaining projects
   * @returns {boolean} True if addon has projects remaining
   */
  hasRemainingProjects() {
    return this.remainingProjects > 0;
  }

  /**
   * Check if addon has remaining files
   * @returns {boolean} True if addon has files remaining
   */
  hasRemainingFiles() {
    return this.remainingFilesPerDay > 0;
  }

  /**
   * Consume credits from addon
   * @param {number} amount - Amount to consume
   * @returns {Promise<boolean>} True if successful
   */
  async consumeCredits(amount) {
    if (this.remainingCredits < amount) {
      return false;
    }
    this.remainingCredits -= amount;
    await this.save();
    return true;
  }

  /**
   * Consume projects from addon
   * @param {number} amount - Amount to consume
   * @returns {Promise<boolean>} True if successful
   */
  async consumeProjects(amount = 1) {
    if (this.remainingProjects < amount) {
      return false;
    }
    this.remainingProjects -= amount;
    await this.save();
    return true;
  }

  /**
   * Consume files from addon
   * @param {number} amount - Amount to consume
   * @returns {Promise<boolean>} True if successful
   */
  async consumeFiles(amount = 1) {
    if (this.remainingFilesPerDay < amount) {
      return false;
    }
    this.remainingFilesPerDay -= amount;
    await this.save();
    return true;
  }

  /**
   * Create user addon record
   * @param {Object} data - Addon data
   * @param {string} data.userId - User ID
   * @param {string} data.planId - Addon plan ID
   * @param {string} data.transactionId - Payment transaction ID
   * @param {number} data.totalCredits - Total credits from addon
   * @param {number} data.totalProjects - Total projects from addon
   * @param {number} data.totalFilesPerDay - Total files per day from addon
   * @returns {Promise<UserAddon>} Created addon record
   */
  static async createUserAddon(data) {
    return UserAddon.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      planId: data.planId,
      transactionId: data.transactionId,
      totalCredits: data.totalCredits,
      remainingCredits: data.totalCredits,
      totalProjects: data.totalProjects,
      remainingProjects: data.totalProjects,
      totalFilesPerDay: data.totalFilesPerDay,
      remainingFilesPerDay: data.totalFilesPerDay,
      isActive: true,
    });
  }

  /**
   * Find active addons for user
   * @param {string} userId - User ID
   * @returns {Promise<UserAddon[]>} Array of active addon records
   */
  static async findActiveByUserId(userId) {
    return UserAddon.findAll({
      where: { 
        userId,
        isActive: true,
        [Op.or]: [
          { remainingCredits: { [Op.gt]: 0 } },
          { remainingProjects: { [Op.gt]: 0 } },
          { remainingFilesPerDay: { [Op.gt]: 0 } }
        ]
      },
      order: [['createdAt', 'ASC']], // FIFO consumption
    });
  }

  /**
   * Get addon summary for user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Addon summary
   */
  static async getAddonSummary(userId) {
    const addons = await this.findActiveByUserId(userId);
    
    const summary = {
      totalAddons: addons.length,
      totalRemainingCredits: 0,
      totalRemainingProjects: 0,
      totalRemainingFilesPerDay: 0,
      addons: addons.map(addon => ({
        id: addon.id,
        planId: addon.planId,
        totalCredits: addon.totalCredits,
        remainingCredits: addon.remainingCredits,
        totalProjects: addon.totalProjects,
        remainingProjects: addon.remainingProjects,
        totalFilesPerDay: addon.totalFilesPerDay,
        remainingFilesPerDay: addon.remainingFilesPerDay,
        purchaseDate: addon.createdAt,
      }))
    };

    // Calculate totals
    addons.forEach(addon => {
      summary.totalRemainingCredits += addon.remainingCredits;
      summary.totalRemainingProjects += addon.remainingProjects;
      summary.totalRemainingFilesPerDay += addon.remainingFilesPerDay;
    });

    return summary;
  }

  /**
   * Find addon by transaction ID
   * @param {string} transactionId - Transaction ID
   * @returns {Promise<UserAddon|null>} Addon record or null
   */
  static async findByTransactionId(transactionId) {
    return UserAddon.findOne({
      where: { transactionId },
    });
  }
}

UserAddon.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    planId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    transactionId: {
      type: DataTypes.UUID,
      allowNull: false,
      unique: true,
    },
    totalCredits: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    remainingCredits: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    totalProjects: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    remainingProjects: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    totalFilesPerDay: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    remainingFilesPerDay: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserAddon',
    tableName: 'user_addons',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        unique: true,
        fields: ['transaction_id'],
      },
      {
        fields: ['plan_id'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['remaining_credits'],
      },
      {
        fields: ['remaining_projects'],
      },
      {
        fields: ['remaining_files_per_day'],
      },
      {
        fields: ['created_at'],
      },
    ],
  }
);

export { UserAddon };
