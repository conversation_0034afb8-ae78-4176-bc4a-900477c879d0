import { DataTypes, Model } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * UserSubscription Model
 * Manages user's active subscription and billing information
 */
class UserSubscription extends Model {
  /**
   * Check if subscription is active
   * @returns {boolean} True if subscription is active
   */
  isActive() {
    return this.status === 'ACTIVE' && 
           (!this.endDate || new Date() <= new Date(this.endDate));
  }

  /**
   * Check if subscription is in grace period
   * @returns {boolean} True if in grace period
   */
  isInGracePeriod() {
    if (!this.gracePeriodEnd) return false;
    return new Date() <= new Date(this.gracePeriodEnd);
  }

  /**
   * Check if subscription needs renewal
   * @returns {boolean} True if needs renewal
   */
  needsRenewal() {
    if (!this.nextBillingDate) return false;
    return new Date() >= new Date(this.nextBillingDate);
  }

  /**
   * Get days until next billing
   * @returns {number} Days until next billing
   */
  getDaysUntilBilling() {
    if (!this.nextBillingDate) return null;
    const now = new Date();
    const billingDate = new Date(this.nextBillingDate);
    const diffTime = billingDate - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Activate subscription
   * @param {Date} startDate - Subscription start date
   * @param {Date} endDate - Subscription end date
   * @param {Date} nextBillingDate - Next billing date
   * @returns {Promise<void>}
   */
  async activate(startDate, endDate, nextBillingDate) {
    this.status = 'ACTIVE';
    this.startDate = startDate;
    this.endDate = endDate;
    this.nextBillingDate = nextBillingDate;
    this.gracePeriodEnd = null;
    await this.save();
  }

  /**
   * Cancel subscription
   * @param {string} reason - Cancellation reason
   * @returns {Promise<void>}
   */
  async cancel(reason = null) {
    this.status = 'CANCELLED';
    this.cancelledAt = new Date();
    this.cancellationReason = reason;
    await this.save();
  }

  /**
   * Suspend subscription with grace period
   * @param {Date} gracePeriodEnd - Grace period end date
   * @returns {Promise<void>}
   */
  async suspend(gracePeriodEnd) {
    this.status = 'SUSPENDED';
    this.gracePeriodEnd = gracePeriodEnd;
    await this.save();
  }

  /**
   * Create user subscription
   * @param {Object} subscriptionData - Subscription data
   * @returns {Promise<UserSubscription>} Created subscription
   */
  static async createSubscription(subscriptionData) {
    return UserSubscription.create({
      id: EncryptionUtil.generateUUID(),
      ...subscriptionData,
    });
  }

  /**
   * Find active subscription by user ID
   * @param {string} userId - User ID
   * @returns {Promise<UserSubscription|null>} Active subscription or null
   */
  static async findActiveByUserId(userId) {
    return UserSubscription.findOne({
      where: {
        userId,
        status: ['ACTIVE', 'SUSPENDED'],
      },
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * Find subscription by Razorpay subscription ID
   * @param {string} razorpaySubscriptionId - Razorpay subscription ID
   * @returns {Promise<UserSubscription|null>} Subscription or null
   */
  static async findByRazorpayId(razorpaySubscriptionId) {
    return UserSubscription.findOne({
      where: { razorpaySubscriptionId },
    });
  }
}

UserSubscription.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    planId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'subscription_plans',
        key: 'id',
      },
    },
    razorpaySubscriptionId: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true,
    },
    razorpayCustomerId: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('ACTIVE', 'SUSPENDED', 'CANCELLED', 'EXPIRED'),
      allowNull: false,
      defaultValue: 'ACTIVE',
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    nextBillingDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    gracePeriodEnd: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    cancelledAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    cancellationReason: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional subscription metadata',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserSubscription',
    tableName: 'user_subscriptions',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['plan_id'],
      },
      {
        unique: true,
        fields: ['razorpay_subscription_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['start_date'],
      },
      {
        fields: ['end_date'],
      },
      {
        fields: ['next_billing_date'],
      },
    ],
  }
);

export { UserSubscription };
