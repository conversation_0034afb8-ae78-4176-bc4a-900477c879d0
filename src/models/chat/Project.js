import { DataTypes, Model  } from 'sequelize';
import { chatDatabase  } from '../../config/database.js';
import { EncryptionUtil  } from '../../utils/encryption.js';

/**
 * Project Model
 * Represents a project for organizing chat threads with specific rules and context
 */
class Project extends Model {
  /**
   * Create a new project
   * @param {Object} data - Project data
   * @param {string} data.userId - User ID
   * @param {string} data.name - Project name
   * @param {string} data.description - Project description
   * @param {string} data.rules - Project rules
   * @returns {Promise<Project>} Created project instance
   */
  static async createProject(data) {

    return Project.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      name: data.name,
      description: data.description,
      rules: data.rules,
    });
  }

  /**
   * Find user projects
   * @param {string} userId - User ID
   * @param {number} [limit=6] - Limit number of results
   * @param {number} [offset=0] - Offset for pagination
   * @returns {Promise<Project[]>} Array of project instances
   */
  static async findUserProjects(userId, limit = 6, offset = 0) {
    return Project.findAll({
      where: { userId },
      order: [['updatedAt', 'DESC']],
      limit,
      offset,
    });
  }

  /**
   * Find project by ID and user
   * @param {string} id - Project ID
   * @param {string} userId - User ID
   * @returns {Promise<Project|null>} Project instance or null
   */
  static async findByIdAndUser(id, userId) {
    return Project.findOne({
      where: { id, userId },
    });
  }

  /**
   * Count total projects for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Total project count
   */
  static async countUserProjects(userId) {
    return Project.count({
      where: { userId },
    });
  }

  /**
   * Update project details
   * @param {Object} data - Update data
   * @param {string} [data.name] - New project name (optional)
   * @param {string} [data.description] - New project description (optional)
   * @param {string} [data.rules] - New project rules (optional)
   * @returns {Promise<void>}
   */
  async updateProject(data) {
    if (data.name !== undefined) this.name = data.name;
    if (data.description !== undefined) this.description = data.description;
    if (data.rules !== undefined) this.rules = data.rules;
    await this.save();
  }
}

Project.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    rules: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'Project',
    tableName: 'projects',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

export { Project  };
