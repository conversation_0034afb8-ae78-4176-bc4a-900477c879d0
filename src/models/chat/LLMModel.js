import { DataTypes, Model } from 'sequelize';
import { chatDatabase } from '../../config/database.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * LLMModel Model
 * Represents available LLM models with their capabilities and configurations
 */
class LLMModel extends Model {
  /**
   * Create a new LLM model entry
   * @param {Object} data - Model data
   * @param {string} data.provider - Provider name (OPENAI, ANTHROPIC, GOOGLE, DEEPSEEK, META)
   * @param {string} data.modelName - Model name/identifier
   * @param {string} data.modelId - API model ID
   * @param {string} data.displayName - Human-readable display name
   * @param {string} data.description - Model description
   * @param {Object} data.capabilities - Model capabilities object
   * @param {string} [data.logoUrl] - Logo URL
   * @param {string} [data.logoAttachmentSecureId] - Logo attachment secure ID
   * @param {string} [data.apiKey] - API key for this model
   * @param {Object} [data.pricing] - Pricing information
   * @param {number} [data.contextWindow] - Context window size
   * @param {number} [data.maxOutputTokens] - Maximum output tokens
   * @param {boolean} [data.supportsVision] - Vision support
   * @param {boolean} [data.supportsAudio] - Audio support
   * @param {boolean} [data.supportsCodeExecution] - Code execution support
   * @param {boolean} [data.isActive] - Whether model is active
   * @returns {Promise<LLMModel>} Created model instance
   */
  static async createModel(data) {
    return LLMModel.create({
      id: EncryptionUtil.generateUUID(),
      provider: data.provider,
      modelName: data.modelName,
      modelId: data.modelId,
      displayName: data.displayName,
      description: data.description,
      capabilities: JSON.stringify(data.capabilities),
      logoUrl: data.logoUrl,
      logoAttachmentSecureId: data.logoAttachmentSecureId,
      apiKey: data.apiKey,
      pricing: data.pricing ? JSON.stringify(data.pricing) : null,
      contextWindow: data.contextWindow,
      maxOutputTokens: data.maxOutputTokens,
      supportsVision: data.supportsVision || false,
      supportsAudio: data.supportsAudio || false,
      supportsCodeExecution: data.supportsCodeExecution || false,
      isActive: data.isActive !== undefined ? data.isActive : true,
    });
  }

  /**
   * Get all active models
   * @returns {Promise<LLMModel[]>} Array of active model instances
   */
  static async getActiveModels() {
    return LLMModel.findAll({
      where: {
        isActive: true,
      },
      order: [['provider', 'ASC'], ['displayName', 'ASC']],
    });
  }

  /**
   * Get models by provider
   * @param {string} provider - Provider name
   * @returns {Promise<LLMModel[]>} Array of model instances
   */
  static async getModelsByProvider(provider) {
    return LLMModel.findAll({
      where: {
        provider: provider.toUpperCase(),
        isActive: true,
      },
      order: [['displayName', 'ASC']],
    });
  }

  /**
   * Get model by model ID
   * @param {string} modelId - Model ID
   * @returns {Promise<LLMModel|null>} Model instance or null
   */
  static async getModelById(modelId) {
    return LLMModel.findOne({
      where: {
        modelId,
        isActive: true,
      },
    });
  }

  /**
   * Get models with specific capabilities
   * @param {Object} capabilities - Required capabilities
   * @param {boolean} [capabilities.vision] - Vision support required
   * @param {boolean} [capabilities.audio] - Audio support required
   * @param {boolean} [capabilities.codeExecution] - Code execution support required
   * @returns {Promise<LLMModel[]>} Array of matching model instances
   */
  static async getModelsByCapabilities(capabilities) {
    const where = {
      isActive: true,
    };

    if (capabilities.vision) {
      where.supportsVision = true;
    }
    if (capabilities.audio) {
      where.supportsAudio = true;
    }
    if (capabilities.codeExecution) {
      where.supportsCodeExecution = true;
    }

    return LLMModel.findAll({
      where,
      order: [['provider', 'ASC'], ['displayName', 'ASC']],
    });
  }

  /**
   * Update model status
   * @param {string} modelId - Model ID
   * @param {boolean} isActive - Active status
   * @returns {Promise<boolean>} Success status
   */
  static async updateModelStatus(modelId, isActive) {
    const [updatedRows] = await LLMModel.update(
      { isActive },
      {
        where: { modelId },
      }
    );
    return updatedRows > 0;
  }

  /**
   * Get parsed capabilities
   * @returns {Object} Parsed capabilities object
   */
  getParsedCapabilities() {
    try {
      return JSON.parse(this.capabilities);
    } catch (error) {
      return {};
    }
  }

  /**
   * Get parsed pricing
   * @returns {Object|null} Parsed pricing object or null
   */
  getParsedPricing() {
    try {
      return this.pricing ? JSON.parse(this.pricing) : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if model supports multimodal input
   * @returns {boolean} True if supports vision or audio
   */
  isMultimodal() {
    return this.supportsVision || this.supportsAudio;
  }

  /**
   * Get model summary for API responses
   * @returns {Object} Model summary object
   */
  getSummary() {
    return {
      id: this.id,
      provider: this.provider,
      modelName: this.modelName,
      modelId: this.modelId,
      displayName: this.displayName,
      description: this.description,
      capabilities: this.getParsedCapabilities(),
      logoUrl: this.logoUrl,
      logoAttachmentSecureId: this.logoAttachmentSecureId,
      pricing: this.getParsedPricing(),
      contextWindow: this.contextWindow,
      maxOutputTokens: this.maxOutputTokens,
      supportsVision: this.supportsVision,
      supportsAudio: this.supportsAudio,
      supportsCodeExecution: this.supportsCodeExecution,
      isMultimodal: this.isMultimodal(),
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}

LLMModel.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    provider: {
      type: DataTypes.ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE', 'DEEPSEEK', 'META'),
      allowNull: false,
    },
    modelName: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    modelId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
    },
    displayName: {
      type: DataTypes.STRING(150),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    capabilities: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    logoUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    logoAttachmentSecureId: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    apiKey: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    pricing: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    contextWindow: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    maxOutputTokens: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    supportsVision: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    supportsAudio: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    supportsCodeExecution: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'LLMModel',
    tableName: 'llm_models',
    timestamps: true,
    indexes: [
      {
        fields: ['provider'],
      },
      {
        fields: ['model_id'],
        unique: true,
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['supports_vision'],
      },
      {
        fields: ['supports_audio'],
      },
      {
        fields: ['supports_code_execution'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

export { LLMModel };
