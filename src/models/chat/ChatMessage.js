import { DataTypes, Model, Op  } from 'sequelize';
import { chatDatabase  } from '../../config/database.js';
import { Chat } from './Chat.js';
import { ChatThread } from './ChatThread.js';
import { EncryptionUtil  } from '../../utils/encryption.js';

/**
 * ChatMessage Model
 * Represents a message and response in a chat conversation
 */
class ChatMessage extends Model {
  /**
   * Create a new chat message
   * @param {Object} data - Message data
   * @returns {Promise<ChatMessage>} Created message instance
   */
  static async createMessage(data) {

    return ChatMessage.create({
      id: EncryptionUtil.generateUUID(),
      chatId: data.chatId,
      message: data.message,
      response: data.response,
      llmModel: data.llmModel,
      isUserMessage: data.isUserMessage ?? true,
      parentMessageId: data.parentMessageId,
      version: data.version ?? 1,
      isRegenerated: data.isRegenerated ?? false,
      attachmentPath: data.attachmentPath,
      attachmentName: data.attachmentName,
      attachmentType: data.attachmentType,
      attachmentSize: data.attachmentSize,
      attachmentS3Url: data.attachmentS3Url,
      attachmentS3Key: data.attachmentS3Key,
      attachmentStorageType: data.attachmentStorageType,
      attachmentSecureId: data.attachmentSecureId,
    });
  }

  /**
   * Find messages for a chat
   * @param {string} chatId - Chat ID
   * @param {number} [limit=50] - Limit number of results
   * @param {number} [offset=0] - Offset for pagination
   * @returns {Promise<ChatMessage[]>} Array of message instances
   */
  static async findChatMessages(chatId, limit = 50, offset = 0) {
    return ChatMessage.findAll({
      where: { chatId },
      order: [['createdAt', 'ASC']],
      limit,
      offset,
    });
  }

  /**
   * Find latest message for a chat
   * @param {string} chatId - Chat ID
   * @returns {Promise<ChatMessage|null>} Latest message instance or null
   */
  static async findLatestMessage(chatId) {
    return ChatMessage.findOne({
      where: { chatId },
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * Count messages for a chat
   * @param {string} chatId - Chat ID
   * @returns {Promise<number>} Number of messages
   */
  static async countChatMessages(chatId) {
    return ChatMessage.count({
      where: { chatId },
    });
  }

  /**
   * Delete all messages for a chat
   * @param {string} chatId - Chat ID
   * @returns {Promise<number>} Number of deleted messages
   */
  static async deleteChatMessages(chatId) {
    return ChatMessage.destroy({
      where: { chatId },
    });
  }

  /**
   * Find message by ID
   * @param {string} messageId - Message ID
   * @returns {Promise<ChatMessage|null>} Message instance or null
   */
  static async findById(messageId) {
    return ChatMessage.findByPk(messageId);
  }

  /**
   * Find all versions of a message
   * @param {string} parentMessageId - Parent message ID
   * @returns {Promise<ChatMessage[]>} Array of message versions
   */
  static async findMessageVersions(parentMessageId) {
    return ChatMessage.findAll({
      where: {
        [Op.or]: [
          { id: parentMessageId },
          { parentMessageId: parentMessageId }
        ]
      },
      order: [['version', 'ASC'], ['createdAt', 'ASC']],
    });
  }

  /**
   * Get latest version number for a message
   * @param {string} parentMessageId - Parent message ID
   * @returns {Promise<number>} Latest version number
   */
  static async getLatestVersion(parentMessageId) {
    const latestMessage = await ChatMessage.findOne({
      where: {
        [Op.or]: [
          { id: parentMessageId },
          { parentMessageId: parentMessageId }
        ]
      },
      order: [['version', 'DESC']],
    });

    return latestMessage ? latestMessage.version : 0;
  }

  /**
   * Create a regenerated response
   * @param {Object} data - Regeneration data
   * @returns {Promise<ChatMessage>} Created regenerated message
   */
  static async createRegeneratedResponse(data) {
    const originalMessage = await ChatMessage.findById(data.originalMessageId);
    if (!originalMessage) {
      throw new Error('Original message not found');
    }

    const nextVersion = await ChatMessage.getLatestVersion(data.originalMessageId) + 1;

    return ChatMessage.create({
      id: EncryptionUtil.generateUUID(),
      chatId: originalMessage.chatId,
      message: originalMessage.message,
      response: data.newResponse,
      llmModel: data.llmModel,
      isUserMessage: originalMessage.isUserMessage,
      parentMessageId: data.originalMessageId,
      version: nextVersion,
      isRegenerated: true,
    });
  }

  /**
   * Find messages by date range
   * @param {string} chatId - Chat ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<ChatMessage[]>} Array of messages in date range
   */
  static async findMessagesByDateRange(chatId, startDate, endDate) {
    return ChatMessage.findAll({
      where: {
        chatId,
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      order: [['createdAt', 'ASC']],
    });
  }

  /**
   * Count daily file uploads for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Number of files uploaded today
   */
  static async countDailyFileUploads(userId) {
    const { Chat, ChatThread } = await import('./index.js');

    // Get start and end of today
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    return ChatMessage.count({
      where: {
        createdAt: {
          [Op.between]: [startOfDay, endOfDay],
        },
        attachmentName: {
          [Op.not]: null, // Only count messages with attachments
        },
      },
      include: [
        {
          model: Chat,
          as: 'chat',
          where: { userId },
          attributes: [],
          required: false,
        },
        {
          model: ChatThread,
          as: 'thread',
          where: { userId },
          attributes: [],
          required: false,
        },
      ],
      where: {
        [Op.or]: [
          { '$chat.user_id$': userId },
          { '$thread.user_id$': userId },
        ],
      },
    });
  }

  /**
   * Transform chat message for secure output (hide sensitive S3 information)
   * @param {ChatMessage} message - Chat message instance
   * @returns {Object} Transformed message object
   */
  static transformForSecureOutput(message) {
    const messageData = message.toJSON();

    // If there's an attachment, replace sensitive S3 info with secure file access
    if (messageData.attachmentSecureId) {
      // Remove ALL sensitive information
      delete messageData.attachmentS3Url;
      delete messageData.attachmentS3Key;
      delete messageData.attachmentPath;

      // Add secure file access information with direct API path
      messageData.attachment = {
        fileId: messageData.attachmentSecureId,
        name: messageData.attachmentName,
        type: messageData.attachmentType,
        size: messageData.attachmentSize,
        storageType: messageData.attachmentStorageType,
        url: `/files/${messageData.attachmentSecureId}` // Direct API path
      };

      // Remove individual attachment fields
      delete messageData.attachmentSecureId;
      delete messageData.attachmentName;
      delete messageData.attachmentType;
      delete messageData.attachmentSize;
      delete messageData.attachmentStorageType;
    } else if (messageData.attachmentName) {
      // Handle legacy messages without secure ID
      delete messageData.attachmentS3Url;
      delete messageData.attachmentS3Key;
      delete messageData.attachmentPath;

      messageData.attachment = {
        name: messageData.attachmentName,
        type: messageData.attachmentType,
        size: messageData.attachmentSize,
        storageType: messageData.attachmentStorageType,
        url: null // No secure access available for legacy files
      };

      delete messageData.attachmentName;
      delete messageData.attachmentType;
      delete messageData.attachmentSize;
      delete messageData.attachmentStorageType;
    }

    return messageData;
  }
}

ChatMessage.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    chatId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    response: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    llmModel: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    isUserMessage: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    parentMessageId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    isRegenerated: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    attachmentPath: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    attachmentName: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    attachmentType: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    attachmentSize: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    attachmentS3Url: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    attachmentS3Key: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    attachmentStorageType: {
      type: DataTypes.ENUM('local', 's3'),
      allowNull: true,
    },
    attachmentSecureId: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'ChatMessage',
    tableName: 'chat_messages',
    timestamps: false,
    indexes: [
      {
        fields: ['chat_id'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['llm_model'],
      },
      {
        fields: ['is_user_message'],
      },
      {
        fields: ['parent_message_id'],
      },
      {
        fields: ['version'],
      },
      {
        fields: ['is_regenerated'],
      },
      {
        fields: ['attachment_path'],
      },
      {
        fields: ['attachment_type'],
      },
      {
        fields: ['attachment_s3_url'],
      },
      {
        fields: ['attachment_s3_key'],
      },
      {
        fields: ['attachment_storage_type'],
      },
      {
        fields: ['attachment_secure_id'],
      },
    ],
  }
);

// Define logical associations without foreign key constraints
// This prevents conflicts since ChatMessage can reference both Chat and ChatThread
Chat.hasMany(ChatMessage, {
  foreignKey: 'chat_id',
  as: 'messages',
  constraints: false // Disable foreign key constraint
});
ChatMessage.belongsTo(Chat, {
  foreignKey: 'chat_id',
  as: 'chat',
  constraints: false // Disable foreign key constraint
});

// Also support ChatThread associations
ChatThread.hasMany(ChatMessage, {
  foreignKey: 'chat_id',
  as: 'messages',
  constraints: false // Disable foreign key constraint
});
ChatMessage.belongsTo(ChatThread, {
  foreignKey: 'chat_id',
  as: 'thread',
  constraints: false // Disable foreign key constraint
});

export { ChatMessage  };
