import { DataTypes, Model  } from 'sequelize';
import { chatDatabase  } from '../../config/database.js';

import { EncryptionUtil  } from '../../utils/encryption.js';

/**
 * Chat Model
 * Represents a chat session between user and AI
 * 
 * @typedef {Object} ChatAttributes
 * @property {string} id - Chat ID
 * @property {string} [userId] - User ID (optional for guest chats)
 * @property {string} sessionId - Session ID
 * @property {string} [title] - Chat title (optional)
 * @property {boolean} isGuest - Whether chat is from guest
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Update timestamp
 */
class Chat extends Model {
  /**
   * Create a new chat
   * @param {Object} data - Chat data
   * @param {string} [data.userId] - User ID (optional)
   * @param {string} data.sessionId - Session ID
   * @param {string} [data.title] - Chat title (optional)
   * @param {boolean} data.isGuest - Whether chat is from guest
   * @returns {Promise<Chat>} Created chat instance
   */
  static async createChat(data) {

    return Chat.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      sessionId: data.sessionId,
      title: data.title,
      isGuest: data.isGuest,
    });
  }

  /**
   * Find chat by session ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<Chat|null>} Chat instance or null
   */
  static async findBySessionId(sessionId) {
    return Chat.findOne({
      where: { sessionId },
    });
  }

  /**
   * Find user chats
   * @param {string} userId - User ID
   * @param {number} [limit=20] - Limit number of results
   * @param {number} [offset=0] - Offset for pagination
   * @returns {Promise<Chat[]>} Array of chat instances
   */
  static async findUserChats(userId, limit = 20, offset = 0) {
    return Chat.findAll({
      where: {
        userId,
        isGuest: false,
      },
      order: [['updatedAt', 'DESC']],
      limit,
      offset,
    });
  }

  /**
   * Find guest chats by session ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<Chat[]>} Array of chat instances
   */
  static async findGuestChats(sessionId) {
    return Chat.findAll({
      where: {
        sessionId,
        isGuest: true,
      },
      order: [['createdAt', 'ASC']],
    });
  }

  /**
   * Find guest chat by session ID
   * @param {string} sessionId - Session ID
   * @param {string} [guestId] - Guest ID (optional, for compatibility)
   * @returns {Promise<Chat|null>} Chat instance or null
   */
  static async findGuestBySessionId(sessionId, guestId = null) {
    return Chat.findOne({
      where: {
        sessionId,
        isGuest: true,
      },
      order: [['createdAt', 'DESC']], // Get the most recent guest chat for this session
    });
  }

  /**
   * Update chat title
   * @param {string} title - New title
   * @returns {Promise<void>}
   */
  async updateTitle(title) {
    this.title = title;
    await this.save();
  }

  /**
   * Convert guest chat to user chat
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async convertToUserChat(userId) {
    this.userId = userId;
    this.isGuest = false;
    await this.save();
  }
}

Chat.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    sessionId: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    isGuest: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'Chat',
    tableName: 'chats',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['session_id'],
      },
      {
        fields: ['is_guest'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

export { Chat  };
