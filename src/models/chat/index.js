import { Chat  } from './Chat.js';
import { ChatMessage  } from './ChatMessage.js';
import { ChatThread  } from './ChatThread.js';
import { Project  } from './Project.js';
import { GuestSession  } from './GuestSession.js';
import { LLMModel  } from './LLMModel.js';

// Define associations between Project and ChatThread after both models are loaded
// Project has many ChatThreads
Project.hasMany(ChatThread, {
  foreignKey: 'project_id',
  as: 'threads',
  constraints: false // Disable foreign key constraint for flexibility
});

// ChatThread belongs to Project
ChatThread.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project',
  constraints: false // Disable foreign key constraint for flexibility
});

export { Chat,
  ChatMessage,
  ChatThread,
  Project,
  GuestSession,
  LLMModel,
 };
