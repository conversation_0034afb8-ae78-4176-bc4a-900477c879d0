import { LLMService } from '../services/LLMService.js';
import { LLMModelDataService } from '../services/LLMModelDataService.js';
import { HTTP_STATUS, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * LLM Model Controller
 * Handles LLM model-related API endpoints
 */
export class LLMModelController {
  /**
   * Get all available LLM models
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getAvailableModels(req, res) {
    try {
      const models = await LLMService.getAvailableModels();
      
      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DATA_RETRIEVED,
        data: {
          models,
          total: models.length,
        },
      });
    } catch (error) {
      logger.error('Error fetching available models:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
        error: error.message,
      });
    }
  }

  /**
   * Get models by provider
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getModelsByProvider(req, res) {
    try {
      const { provider } = req.params;
      
      if (!provider) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          success: false,
          message: 'Provider parameter is required',
        });
      }

      const models = await LLMService.getModelsByProvider(provider);
      
      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DATA_RETRIEVED,
        data: {
          provider: provider.toUpperCase(),
          models,
          total: models.length,
        },
      });
    } catch (error) {
      logger.error(`Error fetching models for provider ${req.params.provider}:`, error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
        error: error.message,
      });
    }
  }

  /**
   * Get models by capabilities
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getModelsByCapabilities(req, res) {
    try {
      const { vision, audio, codeExecution } = req.query;
      
      const capabilities = {};
      if (vision === 'true') capabilities.vision = true;
      if (audio === 'true') capabilities.audio = true;
      if (codeExecution === 'true') capabilities.codeExecution = true;

      const models = await LLMService.getModelsByCapabilities(capabilities);
      
      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DATA_RETRIEVED,
        data: {
          capabilities,
          models,
          total: models.length,
        },
      });
    } catch (error) {
      logger.error('Error fetching models by capabilities:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
        error: error.message,
      });
    }
  }

  /**
   * Get model information by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getModelInfo(req, res) {
    try {
      const { modelId } = req.params;
      
      if (!modelId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          success: false,
          message: 'Model ID parameter is required',
        });
      }

      const model = await LLMService.getModelInfo(modelId);
      
      if (!model) {
        return res.status(HTTP_STATUS.NOT_FOUND).json({
          success: false,
          message: 'Model not found',
        });
      }

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DATA_RETRIEVED,
        data: {
          model,
        },
      });
    } catch (error) {
      logger.error(`Error fetching model info for ${req.params.modelId}:`, error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
        error: error.message,
      });
    }
  }

  /**
   * Get available providers
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getProviders(req, res) {
    try {
      const providers = [
        {
          name: 'OPENAI',
          displayName: 'OpenAI',
          description: 'Advanced language models including GPT-4o and GPT-3.5',
          logoUrl: '/assets/logos/openai-logo.png',
          website: 'https://openai.com',
        },
        {
          name: 'ANTHROPIC',
          displayName: 'Anthropic',
          description: 'Claude models with advanced reasoning and safety features',
          logoUrl: '/assets/logos/anthropic-logo.png',
          website: 'https://anthropic.com',
        },
        {
          name: 'GOOGLE',
          displayName: 'Google',
          description: 'Gemini models with multimodal capabilities',
          logoUrl: '/assets/logos/google-logo.png',
          website: 'https://ai.google.dev',
        },
        {
          name: 'DEEPSEEK',
          displayName: 'DeepSeek',
          description: 'Advanced reasoning models with competitive performance',
          logoUrl: '/assets/logos/deepseek-logo.png',
          website: 'https://deepseek.com',
        },
        {
          name: 'META',
          displayName: 'Meta',
          description: 'Open-source Llama models for various applications',
          logoUrl: '/assets/logos/meta-logo.png',
          website: 'https://llama.meta.com',
        },
      ];

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DATA_RETRIEVED,
        data: {
          providers,
          total: providers.length,
        },
      });
    } catch (error) {
      logger.error('Error fetching providers:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
        error: error.message,
      });
    }
  }

  /**
   * Initialize LLM models database (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async initializeModels(req, res) {
    try {
      // This should be protected by admin middleware
      await LLMModelDataService.initializeModels();
      
      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'LLM models database initialized successfully',
      });
    } catch (error) {
      logger.error('Error initializing models database:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
        error: error.message,
      });
    }
  }

  /**
   * Get model capabilities summary
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getCapabilitiesSummary(req, res) {
    try {
      const models = await LLMService.getAvailableModels();
      
      const summary = {
        totalModels: models.length,
        providers: {},
        capabilities: {
          vision: models.filter(m => m.supportsVision).length,
          audio: models.filter(m => m.supportsAudio).length,
          codeExecution: models.filter(m => m.supportsCodeExecution).length,
          multimodal: models.filter(m => m.isMultimodal).length,
        },
      };

      // Group by provider
      models.forEach(model => {
        if (!summary.providers[model.provider]) {
          summary.providers[model.provider] = {
            count: 0,
            models: [],
          };
        }
        summary.providers[model.provider].count++;
        summary.providers[model.provider].models.push({
          modelId: model.modelId,
          displayName: model.displayName,
          capabilities: model.capabilities,
        });
      });

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DATA_RETRIEVED,
        data: summary,
      });
    } catch (error) {
      logger.error('Error fetching capabilities summary:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
        error: error.message,
      });
    }
  }
}
