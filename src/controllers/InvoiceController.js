import { Op } from 'sequelize';
import { asyncHand<PERSON> } from '../middleware/errorHandler.js';
import { ResponseUtil } from '../utils/response.js';
import { InvoiceService } from '../services/InvoiceService.js';
import { PaymentTransaction } from '../models/subscription/PaymentTransaction.js';
import { User } from '../models/user/User.js';
import { UserProfile } from '../models/user/UserProfile.js';
import logger from '../config/logger.js';

/**
 * Invoice Controller
 * Handles invoice generation and download operations
 */
class InvoiceController {
  /**
   * Download invoice by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static downloadInvoice = asyncHandler(async (req, res) => {
    const { invoiceId } = req.params;
    const userId = req.user?.userId;

    if (!invoiceId) {
      ResponseUtil.badRequest(res, 'Invoice ID is required');
      return;
    }

    try {
      // Find transaction with this invoice ID
      const transaction = await PaymentTransaction.findOne({
        where: { invoiceId },
        include: [{
          model: User,
          as: 'user',
          attributes: ['id', 'email'],
          include: [{
            model: UserProfile,
            as: 'profile',
            attributes: ['firstName', 'lastName']
          }]
        }]
      });

      if (!transaction) {
        ResponseUtil.notFound(res, 'Invoice not found');
        return;
      }

      // Check if user has access to this invoice (if authenticated)
      if (userId && transaction.userId !== userId) {
        ResponseUtil.forbidden(res, 'Access denied to this invoice');
        return;
      }

      // Get invoice PDF
      const invoiceBuffer = await InvoiceService.getInvoiceById(invoiceId);

      // Set response headers
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="invoice-${transaction.invoiceNumber || invoiceId}.pdf"`);
      res.setHeader('Cache-Control', 'private, max-age=3600'); // Cache for 1 hour
      res.setHeader('Content-Length', invoiceBuffer.length);

      // CORS headers for file download
      res.setHeader('Access-Control-Allow-Credentials', 'true');
      res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      // Send PDF
      res.send(invoiceBuffer);

      logger.info(`Invoice downloaded: ${invoiceId}`, {
        userId: transaction.userId,
        transactionId: transaction.id,
      });

    } catch (error) {
      logger.error('Error downloading invoice:', error);
      ResponseUtil.error(res, 'Failed to download invoice');
    }
  });

  /**
   * Get user's invoices list
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getUserInvoices = asyncHandler(async (req, res) => {
    const userId = req.user.userId;
    const { page = 1, limit = 10 } = req.query;

    try {
      const offset = (parseInt(page) - 1) * parseInt(limit);

      const { rows: transactions, count } = await PaymentTransaction.findAndCountAll({
        where: {
          userId,
          status: 'SUCCESS',
          invoiceId: { [Op.ne]: null }
        },
        attributes: [
          'id',
          'transactionType',
          'amount',
          'currency',
          'invoiceId',
          'invoiceNumber',
          'invoiceUrl',
          'paidAt',
          'createdAt'
        ],
        order: [['paidAt', 'DESC']],
        limit: parseInt(limit),
        offset,
      });

      const invoices = transactions.map(transaction => ({
        id: transaction.invoiceId,
        invoiceNumber: transaction.invoiceNumber,
        type: transaction.transactionType.toLowerCase(),
        amount: parseFloat(transaction.amount),
        currency: transaction.currency,
        paidAt: transaction.paidAt,
        downloadUrl: transaction.invoiceUrl,
        createdAt: transaction.createdAt,
      }));

      ResponseUtil.success(res, 'Invoices retrieved successfully', {
        invoices,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / parseInt(limit)),
        },
      });

    } catch (error) {
      logger.error('Error retrieving user invoices:', error);
      ResponseUtil.error(res, 'Failed to retrieve invoices');
    }
  });

  /**
   * Handle CORS preflight for invoice downloads
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static handleCorsOptions = (req, res) => {
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
    res.status(200).end();
  };
}

export { InvoiceController };
