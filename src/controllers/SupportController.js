import { SupportService } from '../services/SupportService.js';
import { ResponseUtil } from '../utils/response.js';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { FileProcessingService } from '../services/FileProcessingService.js';
import logger from '../config/logger.js';

/**
 * Support Controller
 * Handles customer support ticket operations
 */
class SupportController {
  /**
   * Create a new support ticket with optional file attachment
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static createTicket = asyncHandler(async (req, res) => {
    const ticketData = req.body;
    const userId = req.user.userId;
    const file = req.file;

    // Validate ticket data
    const validation = SupportService.validateTicketData(ticketData);
    if (!validation.isValid) {
      ResponseUtil.validationError(res, 'Invalid ticket data', validation.errors);
      return;
    }

    try {
      let attachmentUrl = null;
      let attachmentInfo = null;
      let processedFile = null;

      // Process file attachment if provided
      if (file) {
        try {
          const fileAttachment = {
            buffer: file.buffer,
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size
          };

          processedFile = await FileProcessingService.processFile(fileAttachment, 'support-ticket-attachments');
          attachmentUrl = processedFile.s3Url || processedFile.filePath;
          attachmentInfo = {
            originalName: processedFile.metadata.originalName,
            mimeType: processedFile.metadata.mimeType,
            size: processedFile.metadata.size,
            storageType: processedFile.metadata.storageType,
            secureFileId: processedFile.metadata.secureFileId
          };

          logger.info(`File attachment processed for support ticket: ${file.originalname} (${file.mimetype})`);
        } catch (fileError) {
          logger.error('Error processing file attachment:', fileError);
          ResponseUtil.error(res, 'Failed to process file attachment. Please try again.');
          return;
        }
      }

      // Add attachment data to ticket data
      const ticketDataWithAttachment = {
        ...ticketData,
        attachmentUrl,
        attachmentName: attachmentInfo?.originalName,
        attachmentType: attachmentInfo?.mimeType,
        attachmentSize: attachmentInfo?.size,
        attachmentSecureId: attachmentInfo?.secureFileId,
        attachmentS3Url: processedFile?.s3Url,
        attachmentS3Key: processedFile?.s3Key,
        attachmentStorageType: attachmentInfo?.storageType
      };

      const result = await SupportService.createTicket(userId, ticketDataWithAttachment);

      // Add attachment info to response if file was uploaded
      if (attachmentInfo) {
        result.ticket.attachmentInfo = attachmentInfo;
      }

      ResponseUtil.success(
        res,
        'Support ticket created successfully. A confirmation email has been sent to your registered email address.',
        result,
        201
      );
    } catch (error) {
      logger.error('Error creating support ticket:', error);

      if (error.message === ERROR_MESSAGES.USER_NOT_FOUND) {
        ResponseUtil.notFound(res, ERROR_MESSAGES.USER_NOT_FOUND);
      } else {
        ResponseUtil.error(res, 'Failed to create support ticket. Please try again.');
      }
    }
  });

  /**
   * Get user's support tickets
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getUserTickets = asyncHandler(async (req, res) => {
    const userId = req.user.userId;
    const {
      page = 1,
      limit = 10,
      status,
      priority
    } = req.query;

    try {
      const result = await SupportService.getUserTickets(userId, {
        page: parseInt(page),
        limit: parseInt(limit),
        status,
        priority
      });

      ResponseUtil.success(res, 'User tickets retrieved successfully', result);
    } catch (error) {
      logger.error('Error fetching user tickets:', error);
      ResponseUtil.error(res, 'Failed to retrieve tickets. Please try again.');
    }
  });

  /**
   * Get a specific ticket by ID
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getTicketById = asyncHandler(async (req, res) => {
    const { ticketId } = req.params;
    const userId = req.user.userId;

    try {
      const ticket = await SupportService.getTicketById(ticketId, userId);
      
      if (!ticket) {
        ResponseUtil.notFound(res, 'Ticket not found or you do not have permission to view it');
        return;
      }

      ResponseUtil.success(res, 'Ticket retrieved successfully', { ticket });
    } catch (error) {
      logger.error('Error fetching ticket by ID:', error);
      ResponseUtil.error(res, 'Failed to retrieve ticket. Please try again.');
    }
  });

  /**
   * Get user's ticket statistics
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getUserTicketStats = asyncHandler(async (req, res) => {
    const userId = req.user.userId;

    try {
      const stats = await SupportService.getUserTicketStats(userId);
      ResponseUtil.success(res, 'Ticket statistics retrieved successfully', { stats });
    } catch (error) {
      logger.error('Error fetching user ticket stats:', error);
      ResponseUtil.error(res, 'Failed to retrieve ticket statistics. Please try again.');
    }
  });

  /**
   * Get available priority options
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getPriorityOptions = asyncHandler(async (req, res) => {
    const priorities = [
      { value: 'LOW', label: 'Low', description: 'General questions or minor issues' },
      { value: 'MEDIUM', label: 'Medium', description: 'Standard support requests' },
      { value: 'HIGH', label: 'High', description: 'Important issues affecting functionality' },
      { value: 'URGENT', label: 'Urgent', description: 'Critical issues requiring immediate attention' }
    ];

    ResponseUtil.success(res, 'Priority options retrieved successfully', { priorities });
  });

  /**
   * Get available status options
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getStatusOptions = asyncHandler(async (req, res) => {
    const statuses = [
      { value: 'OPEN', label: 'Open', description: 'Ticket is newly created and awaiting review' },
      { value: 'IN_PROGRESS', label: 'In Progress', description: 'Ticket is being actively worked on' },
      { value: 'RESOLVED', label: 'Resolved', description: 'Issue has been resolved' },
      { value: 'CLOSED', label: 'Closed', description: 'Ticket has been closed' }
    ];

    ResponseUtil.success(res, 'Status options retrieved successfully', { statuses });
  });

  /**
   * Get support information and guidelines
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getSupportInfo = asyncHandler(async (req, res) => {
    const supportInfo = {
      guidelines: {
        title: 'Support Guidelines',
        items: [
          'Provide a clear and descriptive subject line',
          'Include detailed description of the issue or question',
          'Attach relevant files or screenshots if applicable',
          'Include technical details for technical issues',
          'Select appropriate priority level'
        ]
      },
      responseTime: {
        title: 'Expected Response Times',
        times: [
          { priority: 'URGENT', time: '4 hours', description: 'Critical issues' },
          { priority: 'HIGH', time: '12 hours', description: 'Important issues' },
          { priority: 'MEDIUM', time: '24 hours', description: 'Standard requests' },
          { priority: 'LOW', time: '48 hours', description: 'General questions' }
        ]
      },
      contact: {
        email: '<EMAIL>',
        businessHours: 'Monday - Friday, 9:00 AM - 6:00 PM (UTC)',
        emergencyContact: 'For critical issues outside business hours, mark as URGENT priority'
      },
      tips: [
        'Search our FAQ section before creating a ticket',
        'One issue per ticket for faster resolution',
        'Keep ticket ID for future reference',
        'Update ticket if you have additional information'
      ]
    };

    ResponseUtil.success(res, 'Support information retrieved successfully', supportInfo);
  });

  /**
   * Validate ticket creation data
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static validateTicketData = asyncHandler(async (req, res) => {
    const ticketData = req.body;
    
    const validation = SupportService.validateTicketData(ticketData);
    
    if (validation.isValid) {
      ResponseUtil.success(res, 'Ticket data is valid', { valid: true });
    } else {
      ResponseUtil.success(res, 'Ticket data validation failed', {
        valid: false,
        errors: validation.errors
      });
    }
  });
}

export { SupportController };
