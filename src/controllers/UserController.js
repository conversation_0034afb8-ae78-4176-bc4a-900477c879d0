import { ResponseUtil  } from '../utils/response.js';
import { SUCCESS_MESSAGES, ERROR_MESSAGES  } from '../utils/constants.js';
import { asyncHandler  } from '../middleware/errorHandler.js';
import { UserProfileService  } from '../services/UserProfileService.js';
import { CreditService  } from '../services/CreditService.js';
import { AuthService  } from '../services/AuthService.js';
import logger from '../config/logger.js';

import { User  } from '../models/user/index.js';
import { Chat, ChatMessage  } from '../models/chat/index.js';
import { UserOTP, UserCredit, UserCreditTransaction, UserProfile  } from '../models/user/index.js';

/**
 * User Controller
 * Handles user profile management, preferences, and account operations
 */
class UserController {
  /**
   * Get user profile with credits and profile information
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getProfile = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const user = await User.findByPk(req.user.userId);

    if (!user) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.USER_NOT_FOUND);
      return;
    }

    // Get profile and credit information
    const { profile, credits } = await UserProfileService.getUserProfileWithCredits(req.user.userId);
    const creditSummary = await CreditService.getCreditSummary(req.user.userId);

    ResponseUtil.success(res, 'Profile retrieved successfully', {
      user: user.toJSON(),
      profile,
      credits: {
        current: credits,
        ...creditSummary,
      },
    });
  });

  /**
   * Update user profile
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static updateProfile = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { firstName, lastName, email, mobile, profilePicture } = req.body;

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.USER_NOT_FOUND);
      return;
    }

    // Check if email/mobile is already taken by another user
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser && existingUser.id !== user.id) {
        ResponseUtil.conflict(res, 'Email is already taken');
        return;
      }
    }

    if (mobile && mobile !== user.mobile) {
      const existingUser = await User.findOne({ where: { mobile } });
      if (existingUser && existingUser.id !== user.id) {
        ResponseUtil.conflict(res, 'Mobile number is already taken');
        return;
      }
    }

    // Update user basic info (email, mobile)
    if (email !== undefined) user.email = email;
    if (mobile !== undefined) user.mobile = mobile;
    await user.save();

    // Update user profile details (firstName, lastName, profilePicture)
    let updatedProfile = null;
    if (firstName !== undefined || lastName !== undefined || profilePicture !== undefined) {
      const profileData = {};
      if (firstName !== undefined) profileData.firstName = firstName;
      if (lastName !== undefined) profileData.lastName = lastName;
      if (profilePicture !== undefined) profileData.profilePicture = profilePicture;

      updatedProfile = await UserProfileService.updateUserProfile(req.user.userId, profileData);
    }

    // Get the complete user data with profile
    const { profile } = await UserProfileService.getUserProfileWithCredits(req.user.userId);

    ResponseUtil.success(res, 'Profile updated successfully', { profile });
  });

  /**
   * Deactivate user account
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static deactivateAccount = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const user = await User.findByPk(req.user.userId);

    if (!user) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.USER_NOT_FOUND);
      return;
    }

    user.isActive = false;
    await user.save();

    logger.info(`User account deactivated: ${user.id}`);
    ResponseUtil.success(res, 'Account deactivated successfully');
  });

  /**
   * Delete user account and all associated data
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static deleteAccount = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { password } = req.body;

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.USER_NOT_FOUND);
      return;
    }

    // Verify password if user has one
    if (user.password) {
      if (!password) {
        ResponseUtil.validationError(res, 'Password is required to delete account');
        return;
      }

      const isValidPassword = await user.validatePassword(password);
      if (!isValidPassword) {
        ResponseUtil.unauthorized(res, 'Invalid password');
        return;
      }
    }

    // Delete user data

    // Delete user chats and messages
    const userChats = await Chat.findAll({ where: { userId: user.id } });
    for (const chat of userChats) {
      await ChatMessage.destroy({ where: { chatId: chat.id } });
      await chat.destroy();
    }

    // Delete user OTPs
    await UserOTP.destroy({ where: { userId: user.id } });

    // Delete user credit transactions
    await UserCreditTransaction.destroy({ where: { userId: user.id } });

    // Delete user credits
    await UserCredit.destroy({ where: { userId: user.id } });

    // Delete user profile
    await UserProfile.destroy({ where: { userId: user.id } });

    // Delete user
    await user.destroy();

    logger.info(`User account deleted: ${req.user.userId}`);
    ResponseUtil.success(res, 'Account deleted successfully');
  });

  /**
   * Get user statistics
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getUserStats = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }


    const stats = {
      totalChats: await Chat.count({ where: { userId: req.user.userId, isGuest: false } }),
      totalMessages: await ChatMessage.count({
        include: [{
          model: Chat,
          as: 'chat',
          where: { userId: req.user.userId, isGuest: false }
        }]
      }),
      accountCreated: req.user.iat ? new Date(req.user.iat * 1000) : null,
    };

    ResponseUtil.success(res, 'User statistics retrieved successfully', stats);
  });

  /**
   * Get user activity log
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getActivityLog = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;


    // Get recent chats and messages as activity
    const recentChats = await Chat.findAll({
      where: { userId: req.user.userId, isGuest: false },
      order: [['updatedAt', 'DESC']],
      limit: limit / 2,
      offset: offset / 2,
    });

    const recentMessages = await ChatMessage.findAll({
      include: [{
        model: Chat,
        as: 'chat',
        where: { userId: req.user.userId, isGuest: false }
      }],
      order: [['createdAt', 'DESC']],
      limit: limit / 2,
      offset: offset / 2,
    });

    const activity = [
      ...recentChats.map(chat => ({
        type: 'chat_created',
        id: chat.id,
        title: chat.title,
        timestamp: chat.createdAt,
      })),
      ...recentMessages.map(message => ({
        type: 'message_sent',
        id: message.id,
        chatId: message.chatId,
        message: message.message.substring(0, 100) + (message.message.length > 100 ? '...' : ''),
        timestamp: message.createdAt,
      }))
    ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    ResponseUtil.success(res, 'Activity log retrieved successfully', {
      activity: activity.slice(0, limit),
      limit,
      offset
    });
  });

  /**
   * Update user preferences
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static updatePreferences = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { defaultLLMModel, theme, language } = req.body;

    // In a real application, you would have a UserPreferences model
    // For now, we'll just return success
    const preferences = {
      defaultLLMModel: defaultLLMModel || 'gpt-3.5-turbo',
      theme: theme || 'light',
      language: language || 'en',
      updatedAt: new Date().toISOString()
    };

    ResponseUtil.success(res, 'Preferences updated successfully', preferences);
  });

  static getPreferences = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    // In a real application, you would fetch from UserPreferences model
    const preferences = {
      defaultLLMModel: 'gpt-3.5-turbo',
      theme: 'light',
      language: 'en',
      notifications: {
        email: true,
        push: false
      }
    };

    ResponseUtil.success(res, 'Preferences retrieved successfully', preferences);
  });

  /**
   * Export user data
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static exportUserData = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }


    const user = await User.findByPk(req.user.userId);
    if (!user) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.USER_NOT_FOUND);
      return;
    }

    // Get all user data
    const chats = await Chat.findAll({
      where: { userId: req.user.userId, isGuest: false },
      include: [{
        model: ChatMessage,
        as: 'messages'
      }]
    });

    const exportData = {
      user: user.toJSON(),
      chats: chats.map(chat => ({
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt,
        updatedAt: chat.updatedAt,
        messages: (chat.messages || []).map((msg) => ({
          id: msg.id,
          message: msg.message,
          response: msg.response,
          llmModel: msg.llmModel,
          createdAt: msg.createdAt
        }))
      })),
      exportedAt: new Date().toISOString()
    };

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="user-data-${req.user.userId}.json"`);
    res.send(JSON.stringify(exportData, null, 2));
  });

  /**
   * Update user profile details (firstName, lastName, profilePicture)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static updateProfileDetails = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { firstName, lastName, profilePicture } = req.body;

    const updatedProfile = await UserProfileService.updateUserProfile(req.user.userId, {
      firstName,
      lastName,
      profilePicture,
    });

    ResponseUtil.success(res, 'Profile details updated successfully', { profile: updatedProfile });
  });

  /**
   * Change user password
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static changePassword = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      ResponseUtil.validationError(res, 'Current password and new password are required');
      return;
    }

    await AuthService.changePassword(req.user.userId, currentPassword, newPassword);

    ResponseUtil.success(res, 'Password changed successfully');
  });

  /**
   * Get credit transactions
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getCreditTransactions = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const { transactions, total } = await CreditService.getCreditTransactions(
        req.user.userId,
        limit,
        offset
    );

    ResponseUtil.success(res, 'Credit transactions retrieved successfully', {
      transactions,
      total,
      limit,
      offset,
    });
  });

  /**
   * Get credit summary
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getCreditSummary = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const creditSummary = await CreditService.getCreditSummary(req.user.userId);

    ResponseUtil.success(res, 'Credit summary retrieved successfully', creditSummary);
  });

  /**
   * Update user plan
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static updatePlan = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { plan } = req.body;

    if (!plan || !['FREE', 'PREMIUM', 'ENTERPRISE'].includes(plan)) {
      ResponseUtil.validationError(res, 'Valid plan is required (FREE, PREMIUM, ENTERPRISE)');
      return;
    }

    const updatedProfile = await UserProfileService.updateUserPlan(req.user.userId, plan);

    ResponseUtil.success(res, 'Plan updated successfully', { profile: updatedProfile });
  });
}

  /**
   * Get user preferences
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */

export { UserController  };
