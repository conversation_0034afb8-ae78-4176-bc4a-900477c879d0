import { AuthService  } from '../services/AuthService.js';
import { ResponseUtil  } from '../utils/response.js';
import { SUCCESS_MESSAGES, ERROR_MESSAGES  } from '../utils/constants.js';
import { async<PERSON>and<PERSON>  } from '../middleware/errorHandler.js';
import logger from '../config/logger.js';

import { OTPService  } from '../services/OTPService.js';
import { User, UserOTP  } from '../models/user/index.js';

/**
 * Authentication Controller
 * Handles user authentication, registration, and account management
 */
class AuthController {
  /**
   * Login with email/mobile and password or request OTP
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static login = asyncHandler(async (req, res) => {
    try {
      const loginData = req.body;

      if (loginData.requestOtp) {
        // Request OTP
        const result = await AuthService.requestOTP(loginData.identifier);
        ResponseUtil.success(res, SUCCESS_MESSAGES.OTP_SENT, result);
      } else {
        // Login with password
        if (!loginData.password) {
          ResponseUtil.validationError(res, 'Password is required for login');
          return;
        }

        const result = await AuthService.loginWithPassword(loginData);
        ResponseUtil.success(res, SUCCESS_MESSAGES.LOGIN_SUCCESS, result);
      }
    } catch (error) {
      logger.error('Login error:', error);

      // Handle specific error cases
      if (error.message === ERROR_MESSAGES.USER_NOT_FOUND) {
        ResponseUtil.badRequest(res, 'Invalid email');
        return;
      }

      if (error.message === ERROR_MESSAGES.INVALID_CREDENTIALS) {
        ResponseUtil.badRequest(res, 'Invalid password');
        return;
      }

      if (error.message.includes('not verified')) {
        ResponseUtil.badRequest(res, 'Account not verified. Please verify your account first.');
        return;
      }

      if (error.message.includes('deactivated')) {
        ResponseUtil.badRequest(res, 'Account is deactivated. Please contact support.');
        return;
      }

      // Generic error for other cases
      ResponseUtil.error(res, 'Login failed. Please try again.', error.message);
    }
  });

  /**
   * Request OTP for login
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static requestOTP = asyncHandler(async (req, res) => {
    const { identifier } = req.body;

    const result = await AuthService.requestOTP(identifier);
    ResponseUtil.success(res, SUCCESS_MESSAGES.OTP_SENT, result);
  });

  /**
   * Verify OTP and login
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static verifyOTP = asyncHandler(async (req, res) => {
    const otpData = req.body;

    const result = await AuthService.verifyOTPAndLogin(otpData);
    ResponseUtil.success(res, SUCCESS_MESSAGES.OTP_VERIFIED, result);
  });

  /**
   * Register new user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static register = asyncHandler(async (req, res) => {
    try {
      const userData = req.body;

      const result = await AuthService.register(userData);
      ResponseUtil.success(res, 'Registration initiated. Please verify your account with the OTP sent to your email/mobile.', result, 201);
    } catch (error) {
      logger.error('Registration error:', error);

      // Handle specific error cases
      if (error.message.includes('already exists and is verified')) {
        ResponseUtil.conflict(res, 'User with this email already exists');
        return;
      }

      if (error.message.includes('Invalid email format')) {
        ResponseUtil.validationError(res, 'Please provide a valid email address');
        return;
      }

      if (error.message.includes('Invalid mobile format')) {
        ResponseUtil.validationError(res, 'Please provide a valid mobile number');
        return;
      }

      if (error.message.includes('Password must be at least')) {
        ResponseUtil.validationError(res, error.message);
        return;
      }

      if (error.message.includes('Either email or mobile is required')) {
        ResponseUtil.validationError(res, 'Either email or mobile number is required');
        return;
      }

      // Handle database constraint errors
      if (error.name === 'SequelizeUniqueConstraintError') {
        const field = error.errors[0]?.path || 'field';
        ResponseUtil.conflict(res, `User with this ${field} already exists`);
        return;
      }

      // Generic error for other cases
      ResponseUtil.error(res, 'Registration failed. Please try again.', error.message);
    }
  });

  /**
   * Verify registration OTP
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static verifyRegistration = asyncHandler(async (req, res) => {
    const otpData = req.body;

    const result = await AuthService.verifyRegistration(otpData);
    ResponseUtil.success(res, 'Registration completed successfully', result);
  });

  /**
   * Refresh JWT token
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static refreshToken = asyncHandler(async (req, res) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const newToken = await AuthService.refreshToken(token);
    ResponseUtil.success(res, 'Token refreshed successfully', { token: newToken });
  });

  /**
   * Get current user profile
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getProfile = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    // Get user details from database
    const user = await User.findByPk(req.user.userId);

    if (!user) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.USER_NOT_FOUND);
      return;
    }

    ResponseUtil.success(res, 'Profile retrieved successfully', user.toJSON());
  });

  /**
   * Change user password
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static changePassword = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { currentPassword, newPassword } = req.body;

    await AuthService.changePassword(req.user.userId, { currentPassword, newPassword });
    ResponseUtil.success(res, 'Password changed successfully');
  });

  /**
   * Logout user (client-side token invalidation)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static logout = asyncHandler(async (req, res) => {
    // In a JWT-based system, logout is typically handled client-side
    // by removing the token from storage. However, we can log the logout event.

    if (req.user) {
      logger.info(`User logged out: ${req.user.userId}`);
    }

    ResponseUtil.success(res, 'Logged out successfully');
  });

  /**
   * Verify token validity
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static verifyToken = asyncHandler(async (req, res) => {
    const { token } = req.body;

    if (!token) {
      ResponseUtil.validationError(res, 'Token is required');
      return;
    }

    try {
      const payload = await AuthService.verifyToken(token);
      ResponseUtil.success(res, 'Token is valid', {
        valid: true,
        userId: payload.userId,
        expiresAt: new Date(payload.exp * 1000)
      });
    } catch (error) {
      ResponseUtil.success(res, 'Token is invalid', { valid: false });
    }
  });

  /**
   * Check if user exists
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static checkUserExists = asyncHandler(async (req, res) => {
    const { identifier } = req.query;

    if (!identifier || typeof identifier !== 'string') {
      ResponseUtil.validationError(res, 'Identifier is required');
      return;
    }

    const user = await User.findByEmailOrMobile(identifier);

    ResponseUtil.success(res, 'User check completed', {
      exists: !!user,
      hasPassword: user ? !!user.password : false
    });
  });

  /**
   * Resend OTP
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static resendOTP = asyncHandler(async (req, res) => {
    const { identifier } = req.body;

    // First check if user exists
    const user = await User.findByEmailOrMobile(identifier);

    if (!user) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.USER_NOT_FOUND);
      return;
    }

    // Clean up any existing OTPs for this user
    await OTPService.cleanupUserOTPs(user.id);

    // Generate new OTP
    const result = await AuthService.requestOTP(identifier);
    ResponseUtil.success(res, 'OTP resent successfully', result);
  });

  /**
   * Get authentication statistics (admin endpoint)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getAuthStats = asyncHandler(async (req, res) => {
    // This would typically require admin permissions

    const stats = {
      totalUsers: await User.count(),
      activeUsers: await User.count({ where: { isActive: true } }),
      usersWithPassword: await User.count({
        where: {
          password: { [require('sequelize').Op.ne]: null }
        }
      }),
      pendingOTPs: await UserOTP.count({ where: { isUsed: false } }),
    };

    ResponseUtil.success(res, 'Authentication statistics', stats);
  });
}

export { AuthController  };
