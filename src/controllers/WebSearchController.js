/**
 * @fileoverview Web Search Controller for handling web search requests
 */

import { WebSearchService } from '../services/WebSearchService.js';
import { GuestSession } from '../models/chat/GuestSession.js';
import logger from '../config/logger.js';
import { HTTP_STATUS, ERROR_MESSAGES } from '../utils/constants.js';

export class WebSearchController {
  /**
   * Handle web search query with streaming response
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async searchQuery(req, res) {
    try {
      const { query, llmModel } = req.body;
      const sessionId = req.sessionId;
      const user = req.user;

      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          success: false,
          message: 'Query is required and must be a non-empty string',
        });
      }

      if (!WebSearchService.isAvailable()) {
        return res.status(HTTP_STATUS.SERVICE_UNAVAILABLE).json({
          success: false,
          message: 'Web search service is currently unavailable',
        });
      }

      const searchRequest = {
        query: query.trim(),
        sessionId,
        llmModel,
      };

      if (user) {
        // Authenticated user
        await WebSearchService.processUserWebSearchStreaming(
          user.userId,
          searchRequest,
          res
        );
      } else {
        // Guest user
        const clientIP = req.clientIP || '127.0.0.1';
        await WebSearchService.processGuestWebSearchStreaming(
          searchRequest,
          res,
          null,
          clientIP
        );
      }
    } catch (error) {
      logger.error('Error in web search query:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error.message || 'Web search failed',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  }

  /**
   * Handle web search query with file attachment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async searchQueryWithAttachment(req, res) {
    try {
      const { query, llmModel } = req.body;
      const sessionId = req.sessionId;
      const user = req.user;
      const processedFile = req.processedFile;

      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          success: false,
          message: 'Query is required and must be a non-empty string',
        });
      }

      if (!processedFile) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          success: false,
          message: 'File attachment is required for this endpoint',
        });
      }

      if (!WebSearchService.isAvailable()) {
        return res.status(HTTP_STATUS.SERVICE_UNAVAILABLE).json({
          success: false,
          message: 'Web search service is currently unavailable',
        });
      }

      const searchRequest = {
        query: query.trim(),
        sessionId,
        llmModel,
      };

      if (user) {
        // Authenticated user with attachment
        await WebSearchService.processUserWebSearchStreaming(
          user.userId,
          searchRequest,
          res,
          processedFile
        );
      } else {
        // Guest user with attachment
        const clientIP = req.clientIP || '127.0.0.1';
        await WebSearchService.processGuestWebSearchStreaming(
          searchRequest,
          res,
          processedFile,
          clientIP
        );
      }
    } catch (error) {
      logger.error('Error in web search with attachment:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error.message || 'Web search with attachment failed',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  }

  /**
   * Get search history for a session
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getSearchHistory(req, res) {
    try {
      const sessionId = req.sessionId;

      if (!sessionId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          success: false,
          message: 'Session ID is required',
        });
      }

      const searchHistory = await WebSearchService.getSearchHistory(sessionId);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: {
          sessionId,
          searchHistory,
          count: searchHistory.length,
        },
      });
    } catch (error) {
      logger.error('Error getting search history:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_ERROR,
      });
    }
  }

  /**
   * Check web search service status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getServiceStatus(req, res) {
    try {
      const isAvailable = WebSearchService.isAvailable();
      
      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: {
          webSearchAvailable: isAvailable,
          tavilyConfigured: !!process.env.TAVILY_API_KEY,
          maxResults: process.env.TAVILY_API_KEY ? 3 : 0,
        },
      });
    } catch (error) {
      logger.error('Error checking web search service status:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: ERROR_MESSAGES.INTERNAL_ERROR,
      });
    }
  }

  /**
   * Perform a simple web search without streaming (for testing)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async simpleSearch(req, res) {
    try {
      const { query } = req.body;

      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          success: false,
          message: 'Query is required and must be a non-empty string',
        });
      }

      if (!WebSearchService.isAvailable()) {
        return res.status(HTTP_STATUS.SERVICE_UNAVAILABLE).json({
          success: false,
          message: 'Web search service is currently unavailable',
        });
      }

      const searchResults = await WebSearchService.performSearch(query.trim());

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: {
          query: query.trim(),
          results: searchResults,
          count: searchResults.length,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error('Error in simple web search:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message || ERROR_MESSAGES.INTERNAL_ERROR,
      });
    }
  }
}
