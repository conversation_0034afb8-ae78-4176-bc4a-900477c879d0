/**
 * @fileoverview SpreadsheetsAgentController - Single API endpoint for spreadsheet agent operations
 */

import { spreadsheetsAgentService } from '../services/SpreadsheetsAgentService.js';
import logger from '../config/logger.js';

/**
 * SpreadsheetsAgentController Class
 * Handles HTTP requests for spreadsheet agent operations through a single endpoint
 */
export class SpreadsheetsAgentController {
  /**
   * Process user query through spreadsheet agent
   * POST /api/spreadsheets-agent/query
   *
   * This single endpoint handles all spreadsheet operations through natural language:
   * - "Show me all my spreadsheets"
   * - "Create a new spreadsheet called 'Project Tasks'"
   * - "Read data from my budget spreadsheet"
   * - "Update cell A1 in my project tracker to 'Task Name'"
   * - "Get details of spreadsheet with ID 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
   */
  static async processQuery(req, res) {
    try {
      const userId = req.user.userId;
      const { query, context, sessionId } = req.body;

      // Validate input
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Query is required and must be a non-empty string'
        });
      }

      logger.info(`Processing spreadsheet agent query for user: ${userId}${sessionId ? `, sessionId: ${sessionId}` : ''}`);
      logger.debug(`Query: ${query}`);

      // Process the query through the agent
      const result = await spreadsheetsAgentService.processRequest(userId, query, context, sessionId);

      // Log the result
      logger.info(`Spreadsheet agent query completed for user: ${userId}, success: ${result.success}`);

      res.json(result);
    } catch (error) {
      logger.error('Error in processQuery:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while processing your query'
      });
    }
  }

}
