import { GoogleAuthService } from '../services/GoogleAuthService.js';
import { ResponseUtil } from '../utils/response.js';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import logger from '../config/logger.js';

/**
 * Google OAuth Controller
 * Handles Google OAuth 2.0 authentication flow for Google Workspace APIs
 */
class GoogleAuthController {
  /**
   * Initiate Google OAuth flow
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static initiateAuth = asyncHandler(async (req, res) => {
    try {
      const userId = req.user.userId;
      const { scopes } = req.body;

      // Generate authorization URL
      const authUrl = GoogleAuthService.generateAuthUrl(userId, scopes);

      logger.info(`Google OAuth initiated for user: ${userId}`);

      ResponseUtil.success(res, 'Google OAuth URL generated successfully', {
        authUrl,
        message: 'Please visit the URL to authorize access to your Google account'
      });
    } catch (error) {
      logger.error('Error initiating Google OAuth:', error);
      ResponseUtil.error(res, 'Failed to initiate Google OAuth', error.message);
    }
  });

  /**
   * Handle OAuth callback
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static handleCallback = asyncHandler(async (req, res) => {
    try {
      const { code, state, error } = req.query;

      // Check for OAuth errors
      if (error) {
        logger.warn(`Google OAuth error: ${error}`);
        return ResponseUtil.badRequest(res, `Google OAuth error: ${error}`);
      }

      if (!code || !state) {
        return ResponseUtil.badRequest(res, 'Missing authorization code or state parameter');
      }

      const userId = state; // userId was passed as state parameter

      // Exchange code for tokens
      const result = await GoogleAuthService.exchangeCodeForTokens(code, userId);

      logger.info(`Google OAuth callback processed for user: ${userId}`);

      ResponseUtil.success(res, 'Google authentication successful', {
        success: true,
        userInfo: result.userInfo,
        grantedScopes: result.grantedScopes ? result.grantedScopes.split(' ') : [],
        message: 'Your Google account has been successfully connected'
      });
    } catch (error) {
      logger.error('Error handling OAuth callback:', error);
      ResponseUtil.error(res, 'Failed to complete Google authentication', error.message);
    }
  });

  /**
   * Get user's Google authentication status
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getAuthStatus = asyncHandler(async (req, res) => {
    try {
      const userId = req.user.userId;

      const status = await GoogleAuthService.getAuthStatus(userId);

      ResponseUtil.success(res, 'Google authentication status retrieved', status);
    } catch (error) {
      logger.error('Error getting Google auth status:', error);
      ResponseUtil.error(res, 'Failed to get Google authentication status', error.message);
    }
  });

  /**
   * Revoke Google authentication
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static revokeAuth = asyncHandler(async (req, res) => {
    try {
      const userId = req.user.userId;

      const success = await GoogleAuthService.revokeAuth(userId);

      if (success) {
        logger.info(`Google authentication revoked for user: ${userId}`);
        ResponseUtil.success(res, 'Google authentication revoked successfully', {
          message: 'Your Google account has been disconnected'
        });
      } else {
        ResponseUtil.error(res, 'Failed to revoke Google authentication');
      }
    } catch (error) {
      logger.error('Error revoking Google auth:', error);
      ResponseUtil.error(res, 'Failed to revoke Google authentication', error.message);
    }
  });

  /**
   * Refresh access token
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static refreshToken = asyncHandler(async (req, res) => {
    try {
      const userId = req.user.userId;

      const result = await GoogleAuthService.refreshAccessToken(userId);

      logger.info(`Google access token refreshed for user: ${userId}`);

      ResponseUtil.success(res, 'Access token refreshed successfully', {
        expiresAt: result.expiresAt,
        message: 'Your Google access token has been refreshed'
      });
    } catch (error) {
      logger.error('Error refreshing Google access token:', error);
      ResponseUtil.error(res, 'Failed to refresh access token', error.message);
    }
  });

  /**
   * Test Google API access
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static testApiAccess = asyncHandler(async (req, res) => {
    try {
      const userId = req.user.userId;
      const { service } = req.params; // drive, sheets, docs, calendar, slides

      // Validate service
      const validServices = ['drive', 'sheets', 'docs', 'calendar', 'slides'];
      if (!validServices.includes(service)) {
        return ResponseUtil.badRequest(res, `Invalid service. Must be one of: ${validServices.join(', ')}`);
      }

      // Get API client
      const apiClient = await GoogleAuthService.getApiClient(userId, service);

      // Test basic API access based on service
      let testResult;
      switch (service) {
        case 'drive':
          testResult = await apiClient.about.get({ fields: 'user' });
          break;
        case 'sheets':
          // Test by creating a simple spreadsheet (we'll delete it immediately)
          const createResponse = await apiClient.spreadsheets.create({
            resource: {
              properties: {
                title: 'Test Spreadsheet - Delete Me'
              }
            }
          });
          // Delete the test spreadsheet
          const driveClient = await GoogleAuthService.getApiClient(userId, 'drive');
          await driveClient.files.delete({ fileId: createResponse.data.spreadsheetId });
          testResult = { data: { message: 'Sheets API access confirmed' } };
          break;
        case 'docs':
          // Test by creating a simple document (we'll delete it immediately)
          const docResponse = await apiClient.documents.create({
            resource: {
              title: 'Test Document - Delete Me'
            }
          });
          // Delete the test document
          const driveClientDocs = await GoogleAuthService.getApiClient(userId, 'drive');
          await driveClientDocs.files.delete({ fileId: docResponse.data.documentId });
          testResult = { data: { message: 'Docs API access confirmed' } };
          break;
        case 'calendar':
          testResult = await apiClient.calendarList.list({ maxResults: 1 });
          break;
        case 'slides':
          // Test by creating a simple presentation (we'll delete it immediately)
          const slideResponse = await apiClient.presentations.create({
            resource: {
              title: 'Test Presentation - Delete Me'
            }
          });
          // Delete the test presentation
          const driveClientSlides = await GoogleAuthService.getApiClient(userId, 'drive');
          await driveClientSlides.files.delete({ fileId: slideResponse.data.presentationId });
          testResult = { data: { message: 'Slides API access confirmed' } };
          break;
      }

      logger.info(`Google ${service} API access tested for user: ${userId}`);

      ResponseUtil.success(res, `${service} API access confirmed`, {
        service,
        success: true,
        data: testResult.data
      });
    } catch (error) {
      logger.error(`Error testing Google ${req.params.service} API access:`, error);
      ResponseUtil.error(res, `Failed to access Google ${req.params.service} API`, error.message);
    }
  });

  /**
   * Get available Google services and their scopes
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getAvailableServices = asyncHandler(async (req, res) => {
    const services = {
      drive: {
        name: 'Google Drive',
        description: 'Access and manage files in Google Drive',
        scopes: {
          full: GoogleAuthService.SCOPES.DRIVE,
          file: GoogleAuthService.SCOPES.DRIVE_FILE,
          readonly: GoogleAuthService.SCOPES.DRIVE_READONLY
        }
      },
      sheets: {
        name: 'Google Sheets',
        description: 'Create and edit spreadsheets',
        scopes: {
          full: GoogleAuthService.SCOPES.SHEETS,
          readonly: GoogleAuthService.SCOPES.SHEETS_READONLY
        }
      },
      docs: {
        name: 'Google Docs',
        description: 'Create and edit documents',
        scopes: {
          full: GoogleAuthService.SCOPES.DOCS,
          readonly: GoogleAuthService.SCOPES.DOCS_READONLY
        }
      },
      calendar: {
        name: 'Google Calendar',
        description: 'Manage calendar events',
        scopes: {
          full: GoogleAuthService.SCOPES.CALENDAR,
          readonly: GoogleAuthService.SCOPES.CALENDAR_READONLY
        }
      },
      slides: {
        name: 'Google Slides',
        description: 'Create and edit presentations',
        scopes: {
          full: GoogleAuthService.SCOPES.SLIDES,
          readonly: GoogleAuthService.SCOPES.SLIDES_READONLY
        }
      }
    };

    ResponseUtil.success(res, 'Available Google services', {
      services,
      defaultScopes: GoogleAuthService.DEFAULT_SCOPES
    });
  });

  /**
   * Check if user has required scopes for a service
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static checkScopes = asyncHandler(async (req, res) => {
    try {
      const userId = req.user.userId;
      const { scopes } = req.body;

      if (!scopes || !Array.isArray(scopes)) {
        return ResponseUtil.badRequest(res, 'Scopes array is required');
      }

      const hasScopes = await GoogleAuthService.hasRequiredScopes(userId, scopes);

      ResponseUtil.success(res, 'Scope check completed', {
        hasRequiredScopes: hasScopes,
        requestedScopes: scopes
      });
    } catch (error) {
      logger.error('Error checking scopes:', error);
      ResponseUtil.error(res, 'Failed to check scopes', error.message);
    }
  });
}

export { GoogleAuthController };
