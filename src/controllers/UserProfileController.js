import { UserProfileService  } from '../services/UserProfileService.js';
import { CreditService  } from '../services/CreditService.js';
import { AuthService  } from '../services/AuthService.js';
import { ResponseUtil  } from '../utils/response.js';
import { asyncHandler  } from '../middleware/errorHandler.js';
import logger from '../config/logger.js';

/**
 * User Profile Controller
 * Handles user profile operations and credit management
 */
class UserProfileController {
  /**
   * Get user profile with credits
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getProfile = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const { profile, credits } = await UserProfileService.getUserProfileWithCredits(req.user.userId);
    const creditSummary = await CreditService.getCreditSummary(req.user.userId);

    ResponseUtil.success(res, 'Profile retrieved successfully', {
      profile,
      credits: {
        current: credits,
        ...creditSummary,
      },
    });
  });

  /**
   * Update user profile
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static updateProfile = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const { firstName, lastName, profilePicture } = req.body;

    const updatedProfile = await UserProfileService.updateUserProfile(req.user.userId, {
      firstName,
      lastName,
      profilePicture,
    });

    ResponseUtil.success(res, 'Profile updated successfully', { profile: updatedProfile });
  });

  /**
   * Get user credits
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getCredits = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const credits = await CreditService.getUserCredits(req.user.userId);
    const creditSummary = await CreditService.getCreditSummary(req.user.userId);

    ResponseUtil.success(res, 'Credits retrieved successfully', {
      current: credits,
      ...creditSummary,
    });
  });

  /**
   * Get credit transactions
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getCreditTransactions = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const { transactions, total } = await CreditService.getCreditTransactions(
      req.user.userId,
      limit,
      offset
    );

    ResponseUtil.success(res, 'Credit transactions retrieved successfully', {
      transactions,
      total,
      limit,
      offset,
    });
  });

  /**
   * Update user plan
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static updatePlan = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const { plan } = req.body;

    if (!plan || !['FREE', 'PREMIUM', 'ENTERPRISE'].includes(plan)) {
      ResponseUtil.validationError(res, 'Valid plan is required (FREE, PREMIUM, ENTERPRISE)');
      return;
    }

    const updatedProfile = await UserProfileService.updateUserPlan(req.user.userId, plan);

    ResponseUtil.success(res, 'Plan updated successfully', { profile: updatedProfile });
  });

  /**
   * Upload profile picture (placeholder for file upload)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static uploadProfilePicture = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    // This is a placeholder implementation
    // In a real application, you would handle file upload here
    const { profilePictureUrl } = req.body;

    if (!profilePictureUrl) {
      ResponseUtil.validationError(res, 'Profile picture URL is required');
      return;
    }

    const updatedProfile = await UserProfileService.updateUserProfile(req.user.userId, {
      profilePicture: profilePictureUrl,
    });

    ResponseUtil.success(res, 'Profile picture updated successfully', { profile: updatedProfile });
  });
}

export { UserProfileController  };
