import { SubscriptionService } from '../services/SubscriptionService.js';
import { PaymentService } from '../services/PaymentService.js';
import { CreditService } from '../services/CreditService.js';
import { AddonService } from '../services/AddonService.js';
import { Project } from '../models/chat/Project.js';
import { ChatMessage } from '../models/chat/ChatMessage.js';
import { ResponseUtil } from '../utils/response.js';
import { HTTP_STATUS, ERROR_MESSAGES } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * Async handler wrapper for error handling
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * SubscriptionController
 * Handles subscription-related HTTP requests
 */
export class SubscriptionController {
  /**
   * Get available subscription plans
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getPlans = asyncHandler(async (req, res) => {
    try {
      const plans = await SubscriptionService.getAvailablePlans();
      
      ResponseUtil.success(res, 'Subscription plans retrieved successfully', {
        plans: plans.map(plan => ({
          id: plan.id,
          type: plan.planType,
          name: plan.name,
          description: plan.description,
          price: parseFloat(plan.price),
          currency: plan.currency,
          billingCycle: plan.billingCycle,
          credits: plan.credits,
          isUnlimitedCredits: plan.isUnlimitedCredits,
          features: plan.getPlanFeatures(),
          limits: plan.getPlanLimits(),
          isFree: plan.isFree(),
        })),
      });
    } catch (error) {
      logger.error('Error fetching subscription plans:', error);
      ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  });

  /**
   * Get user's current subscription
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getCurrentSubscription = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const [subscriptionData, usageAndLimits] = await Promise.all([
        SubscriptionService.getCurrentSubscription(req.user.userId),
        AddonService.getUserUsageAndLimits(req.user.userId)
      ]);

      ResponseUtil.success(res, 'Current subscription retrieved successfully', {
        subscription: subscriptionData.subscription ? {
          id: subscriptionData.subscription.id,
          status: subscriptionData.subscription.status,
          startDate: subscriptionData.subscription.startDate,
          endDate: subscriptionData.subscription.endDate,
          nextBillingDate: subscriptionData.subscription.nextBillingDate,
          isActive: subscriptionData.isActive,
          isInGracePeriod: subscriptionData.isInGracePeriod,
          daysUntilBilling: subscriptionData.daysUntilBilling,
        } : null,
        plan: subscriptionData.plan ? {
          id: subscriptionData.plan.id,
          type: subscriptionData.plan.planType,
          name: subscriptionData.plan.name,
          description: subscriptionData.plan.description,
          price: parseFloat(subscriptionData.plan.price),
          currency: subscriptionData.plan.currency,
          billingCycle: subscriptionData.plan.billingCycle,
          credits: subscriptionData.plan.credits,
          isUnlimitedCredits: subscriptionData.plan.isUnlimitedCredits,
          features: subscriptionData.plan.getPlanFeatures(),
          limits: subscriptionData.plan.getPlanLimits(),
        } : null,
        isFree: subscriptionData.isFree,

        // Plan limits
        planCredits: usageAndLimits.planCredits,
        planProjects: usageAndLimits.planProjects,
        planFilesPerDay: usageAndLimits.planFilesPerDay,

        // Total limits (plan + addons)
        totalCredits: usageAndLimits.totalCredits,
        totalProjects: usageAndLimits.totalProjects,
        totalFilesPerDay: usageAndLimits.totalFilesPerDay,

        // Remaining limits
        remainingCredits: usageAndLimits.remainingCredits,
        remainingProjects: usageAndLimits.remainingProjects,
        remainingFilesPerDay: usageAndLimits.remainingFilesPerDay,

        // Addon information
        addons: {
          totalAddons: usageAndLimits.totalAddons,
          addonCredits: usageAndLimits.addonCredits,
          addonProjects: usageAndLimits.addonProjects,
          addonFilesPerDay: usageAndLimits.addonFilesPerDay,
          details: usageAndLimits.addons,
        },
      });
    } catch (error) {
      logger.error('Error fetching current subscription:', error);
      ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  });

  /**
   * Create a new subscription
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static createSubscription = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const { planType } = req.body;

      if (!planType) {
        ResponseUtil.badRequest(res, 'Plan type is required');
        return;
      }

      if (planType === 'EXPLORER') {
        ResponseUtil.badRequest(res, 'Cannot create subscription for free plan');
        return;
      }

      const result = await SubscriptionService.createSubscription(req.user.userId, planType);
      
      ResponseUtil.success(res, 'Subscription order created successfully', {
        orderId: result.order.id,
        amount: result.order.amount,
        currency: result.order.currency,
        plan: {
          id: result.plan.id,
          type: result.plan.planType,
          name: result.plan.name,
          price: parseFloat(result.plan.price),
        },
        razorpayKeyId: process.env.RAZORPAY_KEY_ID,
      }, HTTP_STATUS.CREATED);
    } catch (error) {
      logger.error('Error creating subscription:', error);

      const errorMessage = error.message || error.toString();

      if (errorMessage.includes('not found')) {
        ResponseUtil.notFound(res, errorMessage);
      } else {
        ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
      }
    }
  });

  /**
   * Upgrade user's subscription plan
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static upgradePlan = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const { planType } = req.body;

      if (!planType) {
        ResponseUtil.badRequest(res, 'Plan type is required');
        return;
      }

      if (planType === 'EXPLORER') {
        ResponseUtil.badRequest(res, 'Cannot upgrade to free plan. Use downgrade instead.');
        return;
      }

      const result = await SubscriptionService.upgradePlan(req.user.userId, planType);

      ResponseUtil.success(res, 'Plan upgrade order created successfully', {
        orderId: result.order.id,
        amount: result.order.amount,
        currency: result.order.currency,
        plan: {
          id: result.plan.id,
          type: result.plan.planType,
          name: result.plan.name,
          price: parseFloat(result.plan.price),
        },
        currentPlan: {
          type: result.currentPlan.planType,
          name: result.currentPlan.name,
        },
        razorpayKeyId: process.env.RAZORPAY_KEY_ID,
      }, HTTP_STATUS.CREATED);
    } catch (error) {
      logger.error('Error upgrading plan:', error);

      const errorMessage = error.message || error.toString();

      if (errorMessage.includes('not found')) {
        ResponseUtil.notFound(res, errorMessage);
      } else if (errorMessage.includes('not an upgrade')) {
        ResponseUtil.badRequest(res, errorMessage);
      } else {
        ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
      }
    }
  });

  /**
   * Downgrade user's subscription plan
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static downgradePlan = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const { planType } = req.body;

      if (!planType) {
        ResponseUtil.badRequest(res, 'Plan type is required');
        return;
      }

      const result = await SubscriptionService.downgradePlan(req.user.userId, planType);

      if (result.success) {
        // Downgrade to free plan - no payment required
        ResponseUtil.success(res, result.message, {
          plan: {
            type: result.newPlan.planType,
            name: result.newPlan.name,
            price: parseFloat(result.newPlan.price),
          },
          previousPlan: {
            type: result.previousPlan.planType,
            name: result.previousPlan.name,
          },
        });
      } else {
        // Downgrade to paid plan - payment required
        ResponseUtil.success(res, 'Plan downgrade order created successfully', {
          orderId: result.order.id,
          amount: result.order.amount,
          currency: result.order.currency,
          plan: {
            id: result.plan.id,
            type: result.plan.planType,
            name: result.plan.name,
            price: parseFloat(result.plan.price),
          },
          previousPlan: {
            type: result.previousPlan.planType,
            name: result.previousPlan.name,
          },
          razorpayKeyId: process.env.RAZORPAY_KEY_ID,
        }, HTTP_STATUS.CREATED);
      }
    } catch (error) {
      logger.error('Error downgrading plan:', error);

      const errorMessage = error.message || error.toString();

      if (errorMessage.includes('not found')) {
        ResponseUtil.notFound(res, errorMessage);
      } else if (errorMessage.includes('No active subscription')) {
        ResponseUtil.badRequest(res, 'No active subscription found to downgrade');
      } else if (errorMessage.includes('not a downgrade')) {
        ResponseUtil.badRequest(res, errorMessage);
      } else {
        ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
      }
    }
  });

  /**
   * Cancel user's subscription
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static cancelSubscription = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const { reason } = req.body;

      const subscription = await SubscriptionService.cancelSubscription(
        req.user.userId,
        reason || 'User requested cancellation'
      );

      ResponseUtil.success(res, 'Subscription cancelled successfully', {
        subscription: {
          id: subscription.id,
          status: subscription.status,
          cancelledAt: subscription.cancelledAt,
          cancellationReason: subscription.cancellationReason,
        },
      });
    } catch (error) {
      logger.error('Error cancelling subscription:', error);

      if (error.message.includes('No active subscription')) {
        ResponseUtil.badRequest(res, 'No active subscription found to cancel');
      } else {
        ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
      }
    }
  });

  /**
   * Create addon purchase order
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static createAddonOrder = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const result = await PaymentService.createAddonOrder(req.user.userId);
      
      ResponseUtil.success(res, 'Addon order created successfully', {
        orderId: result.order.id,
        amount: result.order.amount,
        currency: result.order.currency,
        plan: {
          id: result.plan.id,
          type: result.plan.planType,
          name: result.plan.name,
          price: parseFloat(result.plan.price),
          credits: result.plan.credits,
        },
        razorpayKeyId: process.env.RAZORPAY_KEY_ID,
      }, HTTP_STATUS.CREATED);
    } catch (error) {
      logger.error('Error creating addon order:', error);

      const errorMessage = error.message || error.toString();
      if (errorMessage && errorMessage.includes('not found')) {
        ResponseUtil.notFound(res, errorMessage);
      } else {
        ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
      }
    }
  });

  /**
   * Get subscription statistics
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getSubscriptionStats = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const [subscriptionData, paymentStats] = await Promise.all([
        SubscriptionService.getCurrentSubscription(req.user.userId),
        PaymentService.getPaymentStats(req.user.userId),
      ]);
      
      ResponseUtil.success(res, 'Subscription statistics retrieved successfully', {
        currentPlan: subscriptionData.plan?.planType || 'EXPLORER',
        isActive: subscriptionData.isActive,
        isFree: subscriptionData.isFree,
        daysUntilBilling: subscriptionData.daysUntilBilling,
        isInGracePeriod: subscriptionData.isInGracePeriod,
        paymentStats: {
          totalTransactions: paymentStats.totalTransactions,
          totalAmount: paymentStats.totalAmount,
          successfulTransactions: paymentStats.successfulTransactions,
          successfulAmount: paymentStats.successfulAmount,
        },
      });
    } catch (error) {
      logger.error('Error fetching subscription stats:', error);
      ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  });
}
