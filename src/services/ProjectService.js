import { Project, ChatThread, ChatMessage } from '../models/chat/index.js';
import { PineconeService } from './PineconeService.js';
import { AddonService } from './AddonService.js';
import logger from '../config/logger.js';

/**
 * @typedef {Object} CreateProjectRequest
 * @property {string} name - Project name
 * @property {string} description - Project description
 * @property {string} rules - Project rules
 */

/**
 * @typedef {Object} UpdateProjectRequest
 * @property {string} [name] - Project name
 * @property {string} [description] - Project description
 * @property {string} [rules] - Project rules
 */

export class ProjectService {
  /**
   * Create a new project
   * @param {string} userId - User ID
   * @param {CreateProjectRequest} projectData - Project data
   * @returns {Promise<Object>} Created project
   */
  static async createProject(userId, projectData) {
    try {
      const { name, description, rules } = projectData;

      if (!name || name.trim().length === 0) {
        throw new Error('Project name is required');
      }

      if (!description || description.trim().length === 0) {
        throw new Error('Project description is required');
      }

      if (!rules || rules.trim().length === 0) {
        throw new Error('Project rules are required');
      }

      // Check if user can create a project (considering plan + addon limits)
      const projectCheck = await AddonService.canCreateProject(userId);
      if (!projectCheck.canCreate) {
        throw new Error(`Project limit reached. You can create up to ${projectCheck.totalLimit} projects. Current: ${projectCheck.currentCount}`);
      }

      const project = await Project.createProject({
        userId,
        name: name.trim(),
        description: description.trim(),
        rules: rules.trim(),
      });

      // Consume project limit from addon if needed
      await AddonService.consumeProjectLimit(userId, 1);

      logger.info(`Created project ${project.id} for user ${userId}`);
      return project;
    } catch (error) {
      logger.error('Error creating project:', error);
      throw error;
    }
  }

  /**
   * Get project by ID and user
   * @param {string} projectId - Project ID
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} Project or null
   */
  static async getProject(projectId, userId) {
    try {
      return await Project.findByIdAndUser(projectId, userId);
    } catch (error) {
      logger.error('Error getting project:', error);
      throw error;
    }
  }

  /**
   * Get all projects for user
   * @param {string} userId - User ID
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} User projects
   */
  static async getUserProjects(userId, limit = 10, offset = 0) {
    try {
      return await Project.findUserProjects(userId, limit, offset);
    } catch (error) {
      logger.error('Error getting user projects:', error);
      throw error;
    }
  }

  /**
   * Update project
   * @param {string} projectId - Project ID
   * @param {string} userId - User ID
   * @param {UpdateProjectRequest} updateData - Update data
   * @returns {Promise<Object>} Updated project
   */
  static async updateProject(projectId, userId, updateData) {
    try {
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      const { name, description, rules } = updateData;

      const updates = {};
      if (name !== undefined) {
        if (!name || name.trim().length === 0) {
          throw new Error('Project name cannot be empty');
        }
        updates.name = name.trim();
      }

      if (description !== undefined) {
        if (!description || description.trim().length === 0) {
          throw new Error('Project description cannot be empty');
        }
        updates.description = description.trim();
      }

      if (rules !== undefined) {
        if (!rules || rules.trim().length === 0) {
          throw new Error('Project rules cannot be empty');
        }
        updates.rules = rules.trim();
      }

      await project.update(updates);

      logger.info(`Updated project ${projectId} for user ${userId}`);
      return project;
    } catch (error) {
      logger.error('Error updating project:', error);
      throw error;
    }
  }

  /**
   * Delete project and all associated data
   * @param {string} projectId - Project ID
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async deleteProject(projectId, userId) {
    try {
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Delete all project threads
      const projectThreads = await ChatThread.findProjectThreads(projectId);
      for (const thread of projectThreads) {
        // Delete Pinecone data for each thread
        await PineconeService.deleteThreadMessages(projectId, thread.id);
      }

      // Delete project threads from database
      await ChatThread.destroy({
        where: { projectId }
      });

      // Delete the project
      await project.destroy();

      logger.info(`Deleted project ${projectId} and all associated data for user ${userId}`);
    } catch (error) {
      logger.error('Error deleting project:', error);
      throw error;
    }
  }

  /**
   * Get project with threads
   * @param {string} projectId - Project ID
   * @param {string} userId - User ID
   * @param {number} [threadLimit] - Limit threads
   * @param {number} [threadOffset] - Offset for thread pagination
   * @returns {Promise<Object>} Project with threads
   */
  static async getProjectWithThreads(projectId, userId, threadLimit = 10, threadOffset = 0) {
    try {
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      const threads = await ChatThread.findProjectThreads(projectId, threadLimit, threadOffset);

      return {
        ...project.toJSON(),
        threads,
      };
    } catch (error) {
      logger.error('Error getting project with threads:', error);
      throw error;
    }
  }

  /**
   * Get project statistics
   * @param {string} projectId - Project ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Project statistics
   */
  static async getProjectStats(projectId, userId) {
    try {
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Get thread count
      const threadCount = await ChatThread.count({
        where: { projectId }
      });

      // Get message count
      const messageCount = await ChatMessage.count({
        include: [{
          model: ChatThread,
          where: { projectId },
          attributes: []
        }]
      });

      // Get last activity
      const lastThread = await ChatThread.findOne({
        where: { projectId },
        order: [['updatedAt', 'DESC']],
        attributes: ['updatedAt']
      });

      return {
        threadCount,
        messageCount,
        lastActivity: lastThread?.updatedAt || null,
      };
    } catch (error) {
      logger.error('Error getting project stats:', error);
      throw error;
    }
  }

  /**
   * Search projects by name
   * @param {string} userId - User ID
   * @param {string} searchTerm - Search term
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} Matching projects
   */
  static async searchProjects(
    userId,
    searchTerm,
    limit = 6,
    offset = 0
  ) {
    try {
      const { Op } = await import('sequelize');

      return Project.findAll({
        where: {
          userId,
          name: {
            [Op.like]: `%${searchTerm}%`
          }
        },
        order: [['updatedAt', 'DESC']],
        limit,
        offset,
      });
    } catch (error) {
      logger.error('Error searching projects:', error);
      throw error;
    }
  }

  /**
   * Get recent projects for user
   * @param {string} userId - User ID
   * @param {number} [limit] - Limit results
   * @returns {Promise<Array>} Recent projects
   */
  static async getRecentProjects(userId, limit = 5) {
    try {
      return await Project.findAll({
        where: { userId },
        order: [['updatedAt', 'DESC']],
        limit,
      });
    } catch (error) {
      logger.error('Error getting recent projects:', error);
      throw error;
    }
  }

  /**
   * Duplicate project
   * @param {string} projectId - Project ID to duplicate
   * @param {string} userId - User ID
   * @param {string} [newName] - New project name
   * @returns {Promise<Object>} Duplicated project
   */
  static async duplicateProject(projectId, userId, newName) {
    try {
      const originalProject = await Project.findByIdAndUser(projectId, userId);
      if (!originalProject) {
        throw new Error('Project not found');
      }

      const duplicatedProject = await Project.createProject({
        userId,
        name: newName || `${originalProject.name} (Copy)`,
        description: originalProject.description,
        rules: originalProject.rules,
      });

      logger.info(`Duplicated project ${projectId} to ${duplicatedProject.id} for user ${userId}`);
      return duplicatedProject;
    } catch (error) {
      logger.error('Error duplicating project:', error);
      throw error;
    }
  }

  /**
   * Get project activity summary
   * @param {string} projectId - Project ID
   * @param {string} userId - User ID
   * @param {number} [days] - Number of days to look back
   * @returns {Promise<Object>} Activity summary
   */
  static async getProjectActivity(projectId, userId, days = 7) {
    try {
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      const { Op } = await import('sequelize');
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // Get recent threads
      const recentThreads = await ChatThread.count({
        where: {
          projectId,
          createdAt: {
            [Op.gte]: startDate
          }
        }
      });

      // Get recent messages
      const recentMessages = await ChatMessage.count({
        include: [{
          model: ChatThread,
          where: { projectId },
          attributes: []
        }],
        where: {
          createdAt: {
            [Op.gte]: startDate
          }
        }
      });

      return {
        period: `${days} days`,
        newThreads: recentThreads,
        newMessages: recentMessages,
      };
    } catch (error) {
      logger.error('Error getting project activity:', error);
      throw error;
    }
  }

  /**
   * Validate project data
   * @param {Object} data - Project data to validate
   * @returns {Object} Validation result
   */
  static validateProjectData(data) {
    const errors = [];

    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
      errors.push('Project name is required and must be a non-empty string');
    }

    if (!data.description || typeof data.description !== 'string' || data.description.trim().length === 0) {
      errors.push('Project description is required and must be a non-empty string');
    }

    if (!data.rules || typeof data.rules !== 'string' || data.rules.trim().length === 0) {
      errors.push('Project rules are required and must be a non-empty string');
    }

    if (data.name && data.name.length > 100) {
      errors.push('Project name must be 100 characters or less');
    }

    if (data.description && data.description.length > 500) {
      errors.push('Project description must be 500 characters or less');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
