import { UserOTP } from '../models/user/index.js';
import { EncryptionUtil } from '../utils/encryption.js';
import { VALIDATION_RULES } from '../utils/constants.js';
import { EmailService } from './EmailService.js';
import logger from '../config/logger.js';

export class OTPService {
  /**
   * Generate and store OTP for user
   * @param {string} userId - User ID
   * @returns {Promise<string>} Generated OTP
   */
  static async generateOTP(userId) {
    try {
      // Clean up any existing unused OTPs for this user
      await this.cleanupUserOTPs(userId);

      // Generate new OTP
      const otp = EncryptionUtil.generateOTP(VALIDATION_RULES.OTP_LENGTH);
      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');

      // Store OTP in database
      await UserOTP.createOTP(userId, otp, expiryMinutes);

      logger.info(`OTP generated for user: ${userId}`);
      return otp;
    } catch (error) {
      logger.error('Error generating OTP:', error);
      throw new Error('Failed to generate OTP');
    }
  }

  /**
   * Verify OTP for user
   * @param {string} userId - User ID
   * @param {string} otp - OTP to verify
   * @returns {Promise<boolean>} Whether OTP is valid
   */
  static async verifyOTP(userId, otp) {
    try {
      // Find valid OTP
      const otpRecord = await UserOTP.findValidOTP(userId, otp);

      if (!otpRecord) {
        logger.warn(`Invalid OTP attempt for user: ${userId}`);
        return false;
      }

      // Check if OTP is expired
      if (otpRecord.isExpired()) {
        logger.warn(`Expired OTP attempt for user: ${userId}`);
        await otpRecord.destroy(); // Clean up expired OTP
        return false;
      }

      // Mark OTP as used and delete it
      await otpRecord.destroy();
      
      logger.info(`OTP verified successfully for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error verifying OTP:', error);
      throw new Error('Failed to verify OTP');
    }
  }

  /**
   * Clean up expired OTPs
   * @returns {Promise<number>} Number of deleted OTPs
   */
  static async cleanupExpiredOTPs() {
    try {
      const deletedCount = await UserOTP.cleanupExpiredOTPs();
      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} expired OTPs`);
      }
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up expired OTPs:', error);
      return 0;
    }
  }

  /**
   * Clean up used OTPs
   * @returns {Promise<number>} Number of deleted OTPs
   */
  static async cleanupUsedOTPs() {
    try {
      const deletedCount = await UserOTP.cleanupUsedOTPs();
      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} used OTPs`);
      }
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up used OTPs:', error);
      return 0;
    }
  }

  /**
   * Clean up all OTPs for a specific user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Number of deleted OTPs
   */
  static async cleanupUserOTPs(userId) {
    try {
      const deletedCount = await UserOTP.invalidateUserOTPs(userId);
      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} OTPs for user: ${userId}`);
      }
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up user OTPs:', error);
      return 0;
    }
  }

  /**
   * Check if user has pending OTP
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Whether user has pending OTP
   */
  static async hasPendingOTP(userId) {
    try {
      const otpRecord = await UserOTP.findOne({
        where: {
          userId,
          isUsed: false,
        },
      });

      if (!otpRecord) {
        return false;
      }

      // Check if OTP is expired
      if (otpRecord.isExpired()) {
        await otpRecord.destroy(); // Clean up expired OTP
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error checking pending OTP:', error);
      return false;
    }
  }

  /**
   * Get OTP expiry time for user
   * @param {string} userId - User ID
   * @returns {Promise<Date|null>} OTP expiry time or null if no pending OTP
   */
  static async getOTPExpiry(userId) {
    try {
      const otpRecord = await UserOTP.findOne({
        where: {
          userId,
          isUsed: false,
        },
        order: [['createdAt', 'DESC']],
      });

      if (!otpRecord || otpRecord.isExpired()) {
        return null;
      }

      return otpRecord.expiresAt;
    } catch (error) {
      logger.error('Error getting OTP expiry:', error);
      return null;
    }
  }

  /**
   * Send OTP via SMS/Email
   * @param {string} identifier - Email or mobile number
   * @param {string} otp - OTP to send
   * @param {string} action - Action type (signup, login, etc.)
   * @returns {Promise<boolean>} Whether OTP was sent successfully
   */
  static async sendOTP(identifier, otp, action = 'verify') {
    try {
      const { VALIDATION_RULES } = await import('../utils/constants.js');
      const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(identifier);
      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');

      if (isEmail) {
        // Send email OTP
        logger.info(`Sending OTP via email to: ${identifier}`);
        const emailSent = await EmailService.sendOTPEmail(identifier, otp, action, expiryMinutes);

        if (!emailSent) {
          throw new Error('Failed to send OTP email');
        }

        logger.info(`OTP email sent successfully to: ${identifier}`);
      } else {
        // Send SMS OTP (placeholder for future SMS integration)
        logger.info(`Sending OTP via SMS to: ${identifier}`);
        logger.warn('SMS OTP sending not implemented yet. Please use email for OTP verification.');

        // For now, we'll return false for SMS since it's not implemented
        // In the future, integrate with SMS services like Twilio
        return false;
      }

      // For development, log the OTP
      if (process.env.NODE_ENV === 'development') {
        logger.info(`OTP for ${identifier}: ${otp}`);
      }

      return true;
    } catch (error) {
      logger.error('Error sending OTP:', error);
      return false;
    }
  }

  /**
   * Resend OTP for user
   * @param {string} userId - User ID
   * @param {string} identifier - Email or mobile number
   * @returns {Promise<string>} New OTP
   */
  static async resendOTP(userId, identifier) {
    try {
      // Generate new OTP
      const otp = await this.generateOTP(userId);
      
      // Send OTP
      const sent = await this.sendOTP(identifier, otp);
      if (!sent) {
        throw new Error('Failed to send OTP');
      }

      logger.info(`OTP resent for user: ${userId}`);
      return otp;
    } catch (error) {
      logger.error('Error resending OTP:', error);
      throw error;
    }
  }

  /**
   * Get OTP statistics
   * @returns {Promise<Object>} OTP statistics
   */
  static async getOTPStats() {
    try {
      const [totalOTPs, expiredOTPs, usedOTPs] = await Promise.all([
        UserOTP.count(),
        UserOTP.count({
          where: {
            expiresAt: {
              [UserOTP.sequelize.Sequelize.Op.lt]: new Date()
            }
          }
        }),
        UserOTP.count({
          where: {
            isUsed: true
          }
        })
      ]);

      return {
        totalOTPs,
        expiredOTPs,
        usedOTPs,
        activeOTPs: totalOTPs - expiredOTPs - usedOTPs,
      };
    } catch (error) {
      logger.error('Error getting OTP stats:', error);
      throw error;
    }
  }

  /**
   * Schedule cleanup job for expired OTPs
   */
  static scheduleCleanup() {
    // Run cleanup every hour
    setInterval(async () => {
      await this.cleanupExpiredOTPs();
      await this.cleanupUsedOTPs();
    }, 60 * 60 * 1000); // 1 hour

    logger.info('OTP cleanup scheduler started');
  }
}
