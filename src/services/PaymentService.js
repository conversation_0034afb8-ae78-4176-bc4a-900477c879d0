import { PaymentTransaction, SubscriptionPlan, UserAddon } from '../models/subscription/index.js';
import { User } from '../models/user/index.js';
import { RazorpayService } from './RazorpayService.js';
import { SubscriptionService } from './SubscriptionService.js';
import { CreditService } from './CreditService.js';
import { InvoiceService } from './InvoiceService.js';
import { EmailService } from './EmailService.js';
import { PAYMENT_SYSTEM } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * PaymentService
 * Handles payment processing, verification, and transaction management
 */
export class PaymentService {
  /**
   * Create payment order for subscription
   * @param {string} userId - User ID
   * @param {string} planType - Plan type
   * @returns {Promise<Object>} Payment order details
   */
  static async createSubscriptionOrder(userId, planType) {
    try {
      return await SubscriptionService.createSubscription(userId, planType);
    } catch (error) {
      logger.error('Error creating subscription order:', error);
      throw error;
    }
  }

  /**
   * Create payment order for addon purchase
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Payment order details
   */
  static async createAddonOrder(userId) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const addonPlan = await SubscriptionPlan.findByType('ADDON');
      if (!addonPlan) {
        throw new Error('Addon plan not found');
      }

      const razorpayService = RazorpayService.getInstance();

      // Create payment order for addon
      // Generate a shorter receipt ID (max 40 chars for Razorpay)
      const timestamp = Date.now().toString().slice(-8); // Last 8 digits
      const userIdShort = userId.slice(-8); // Last 8 chars of UUID
      const receipt = `addon_${userIdShort}_${timestamp}`;

      const orderData = {
        amount: RazorpayService.convertToPaise(addonPlan.price),
        currency: addonPlan.currency,
        receipt: receipt,
        notes: {
          userId: userId,
          planId: addonPlan.id,
          planType: 'ADDON',
          type: 'addon',
        },
      };

      const order = await razorpayService.createOrder(orderData);

      // Create payment transaction record
      const transaction = await PaymentTransaction.createTransaction({
        userId: userId,
        planId: addonPlan.id,
        transactionType: PAYMENT_SYSTEM.TRANSACTION_TYPES.ADDON,
        amount: addonPlan.price,
        currency: addonPlan.currency,
        status: PAYMENT_SYSTEM.STATUS.PENDING,
        razorpayOrderId: order.id,
      });

      return {
        order,
        transaction,
        plan: addonPlan,
      };
    } catch (error) {
      logger.error('Error creating addon order:', error);
      throw error;
    }
  }

  /**
   * Verify and process payment
   * @param {Object} paymentData - Payment verification data
   * @param {string} paymentData.razorpay_payment_id - Payment ID
   * @param {string} paymentData.razorpay_order_id - Order ID
   * @param {string} paymentData.razorpay_signature - Payment signature
   * @returns {Promise<Object>} Payment result
   */
  static async verifyAndProcessPayment(paymentData) {
    try {
      const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = paymentData;

      // Verify payment signature
      const razorpayService = RazorpayService.getInstance();
      const isValidSignature = razorpayService.verifyPaymentSignature(paymentData);

      if (!isValidSignature) {
        throw new Error('Invalid payment signature');
      }

      // Find transaction by order ID
      const transaction = await PaymentTransaction.findByRazorpayOrderId(razorpay_order_id);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Get payment details from Razorpay
      const paymentDetails = await razorpayService.fetchPayment(razorpay_payment_id);

      // Mark transaction as successful
      await transaction.markAsSuccessful(razorpay_payment_id, paymentDetails);

      // Process based on transaction type
      let result;
      switch (transaction.transactionType) {
        case PAYMENT_SYSTEM.TRANSACTION_TYPES.SUBSCRIPTION:
          result = await this.processSubscriptionPayment(transaction);
          break;
        case PAYMENT_SYSTEM.TRANSACTION_TYPES.ADDON:
          result = await this.processAddonPayment(transaction);
          break;
        default:
          throw new Error(`Unsupported transaction type: ${transaction.transactionType}`);
      }

      logger.info(`Payment processed successfully: ${razorpay_payment_id}`);

      return {
        success: true,
        transaction,
        result,
      };
    } catch (error) {
      logger.error('Error verifying and processing payment:', error);
      
      // Try to mark transaction as failed if we can find it
      try {
        const transaction = await PaymentTransaction.findByRazorpayOrderId(paymentData.razorpay_order_id);
        if (transaction) {
          await transaction.markAsFailed(error.message);
        }
      } catch (updateError) {
        logger.error('Error updating transaction status:', updateError);
      }

      throw error;
    }
  }

  /**
   * Process subscription payment
   * @param {Object} transaction - Payment transaction
   * @returns {Promise<Object>} Subscription activation result
   */
  static async processSubscriptionPayment(transaction) {
    try {
      const plan = await SubscriptionPlan.findByPk(transaction.planId);
      if (!plan) {
        throw new Error('Plan not found');
      }

      const user = await User.findByPk(transaction.userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Activate subscription
      const result = await SubscriptionService.activateSubscription(
        transaction.razorpayPaymentId,
        transaction.razorpayOrderId,
        transaction.razorpaySignature
      );

      // Generate invoice
      let invoiceData = null;
      try {
        invoiceData = await InvoiceService.generateSubscriptionInvoice({
          transaction,
          user,
          plan,
          subscription: result.subscription,
        });

        // Update transaction with invoice details
        await transaction.update({
          invoiceId: invoiceData.invoiceId,
          invoiceNumber: invoiceData.invoiceNumber,
          invoiceUrl: invoiceData.downloadUrl,
        });

        logger.info(`Invoice generated for subscription: ${invoiceData.invoiceNumber}`);
      } catch (invoiceError) {
        logger.error('Error generating subscription invoice:', invoiceError);
        // Don't fail the payment processing if invoice generation fails
      }

      // Send confirmation email
      try {
        await EmailService.sendSubscriptionPurchaseEmail(user.email, {
          user,
          plan,
          transaction,
          subscription: result.subscription,
          invoiceUrl: invoiceData?.downloadUrl,
        });

        logger.info(`Subscription confirmation email sent to: ${user.email}`);
      } catch (emailError) {
        logger.error('Error sending subscription confirmation email:', emailError);
        // Don't fail the payment processing if email fails
      }

      return {
        type: 'subscription',
        subscription: result.subscription,
        plan: result.plan,
        invoice: invoiceData,
      };
    } catch (error) {
      logger.error('Error processing subscription payment:', error);
      throw error;
    }
  }

  /**
   * Process addon payment
   * @param {Object} transaction - Payment transaction
   * @returns {Promise<Object>} Addon purchase result
   */
  static async processAddonPayment(transaction) {
    try {
      const plan = await SubscriptionPlan.findByPk(transaction.planId);
      if (!plan) {
        throw new Error('Addon plan not found');
      }

      const user = await User.findByPk(transaction.userId);
      if (!user) {
        throw new Error('User not found');
      }

      const planLimits = plan.getPlanLimits();

      // Create addon record to track limits
      const addon = await UserAddon.createUserAddon({
        userId: transaction.userId,
        planId: plan.id,
        transactionId: transaction.id,
        totalCredits: plan.credits,
        totalProjects: planLimits.projects || 0,
        totalFilesPerDay: planLimits.filesPerDay || 0,
      });

      // Also add credits to user account for backward compatibility
      await CreditService.addCredits(
        transaction.userId,
        plan.credits,
        `Addon pack purchase - ${plan.name}`
      );

      logger.info(`Addon processed for user ${transaction.userId}: ${plan.credits} credits, ${planLimits.projects || 0} projects, ${planLimits.filesPerDay || 0} files added`);

      // Generate invoice
      let invoiceData = null;
      try {
        invoiceData = await InvoiceService.generateAddonInvoice({
          transaction,
          user,
          plan,
        });

        // Update transaction with invoice details
        await transaction.update({
          invoiceId: invoiceData.invoiceId,
          invoiceNumber: invoiceData.invoiceNumber,
          invoiceUrl: invoiceData.downloadUrl,
        });

        logger.info(`Invoice generated for add-on: ${invoiceData.invoiceNumber}`);
      } catch (invoiceError) {
        logger.error('Error generating add-on invoice:', invoiceError);
        // Don't fail the payment processing if invoice generation fails
      }

      // Send confirmation email
      try {
        await EmailService.sendAddonPurchaseEmail(user.email, {
          user,
          plan,
          transaction,
          invoiceUrl: invoiceData?.downloadUrl,
        });

        logger.info(`Add-on confirmation email sent to: ${user.email}`);
      } catch (emailError) {
        logger.error('Error sending add-on confirmation email:', emailError);
        // Don't fail the payment processing if email fails
      }

      return {
        type: 'addon',
        creditsAdded: plan.credits,
        projectsAdded: planLimits.projects || 0,
        filesAdded: planLimits.filesPerDay || 0,
        plan: plan,
        addon: addon,
        invoice: invoiceData,
      };
    } catch (error) {
      logger.error('Error processing addon payment:', error);
      throw error;
    }
  }

  /**
   * Get user's payment history
   * @param {string} userId - User ID
   * @param {number} limit - Limit results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Object>} Payment history
   */
  static async getUserPaymentHistory(userId, limit = 10, offset = 0) {
    try {
      const transactions = await PaymentTransaction.getUserPaymentHistory(userId, limit, offset);
      
      // Get total count for pagination
      const totalCount = await PaymentTransaction.count({
        where: { userId },
      });

      return {
        transactions,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount,
        },
      };
    } catch (error) {
      logger.error('Error fetching payment history:', error);
      throw error;
    }
  }

  /**
   * Get payment statistics for user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Payment statistics
   */
  static async getPaymentStats(userId) {
    try {
      const { Op } = await import('sequelize');
      
      const stats = await PaymentTransaction.findAll({
        where: { userId },
        attributes: [
          [PaymentTransaction.sequelize.fn('COUNT', PaymentTransaction.sequelize.col('id')), 'totalTransactions'],
          [PaymentTransaction.sequelize.fn('SUM', PaymentTransaction.sequelize.col('amount')), 'totalAmount'],
          [PaymentTransaction.sequelize.fn('COUNT', PaymentTransaction.sequelize.literal("CASE WHEN status = 'SUCCESS' THEN 1 END")), 'successfulTransactions'],
          [PaymentTransaction.sequelize.fn('SUM', PaymentTransaction.sequelize.literal("CASE WHEN status = 'SUCCESS' THEN amount ELSE 0 END")), 'successfulAmount'],
        ],
        raw: true,
      });

      const recentTransactions = await PaymentTransaction.findAll({
        where: { 
          userId,
          status: PAYMENT_SYSTEM.STATUS.SUCCESS,
        },
        order: [['createdAt', 'DESC']],
        limit: 5,
      });

      return {
        totalTransactions: parseInt(stats[0].totalTransactions) || 0,
        totalAmount: parseFloat(stats[0].totalAmount) || 0,
        successfulTransactions: parseInt(stats[0].successfulTransactions) || 0,
        successfulAmount: parseFloat(stats[0].successfulAmount) || 0,
        recentTransactions,
      };
    } catch (error) {
      logger.error('Error fetching payment stats:', error);
      throw error;
    }
  }

  /**
   * Refund a payment
   * @param {string} paymentId - Razorpay payment ID
   * @param {number} amount - Refund amount (optional, full refund if not specified)
   * @param {string} reason - Refund reason
   * @returns {Promise<Object>} Refund result
   */
  static async refundPayment(paymentId, amount = null, reason = 'Requested by user') {
    try {
      const transaction = await PaymentTransaction.findByRazorpayPaymentId(paymentId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.status !== PAYMENT_SYSTEM.STATUS.SUCCESS) {
        throw new Error('Cannot refund unsuccessful payment');
      }

      const razorpayService = RazorpayService.getInstance();
      
      // Create refund in Razorpay
      const refundData = {
        amount: amount ? RazorpayService.convertToPaise(amount) : undefined,
        notes: {
          reason: reason,
          transactionId: transaction.id,
        },
      };

      const refund = await razorpayService.razorpay.payments.refund(paymentId, refundData);

      // Update transaction status
      transaction.status = PAYMENT_SYSTEM.STATUS.REFUNDED;
      transaction.paymentDetails = {
        ...transaction.paymentDetails,
        refund: refund,
      };
      await transaction.save();

      logger.info(`Payment refunded: ${paymentId}, amount: ${amount || 'full'}`);

      return {
        refund,
        transaction,
      };
    } catch (error) {
      logger.error('Error refunding payment:', error);
      throw error;
    }
  }
}
