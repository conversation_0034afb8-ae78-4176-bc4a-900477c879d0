import { UserAddon } from '../models/subscription/UserAddon.js';
import { SubscriptionService } from './SubscriptionService.js';
import { CreditService } from './CreditService.js';
import { Project } from '../models/chat/Project.js';
import { ChatMessage } from '../models/chat/ChatMessage.js';
import logger from '../config/logger.js';

/**
 * AddonService
 * Manages addon consumption logic and limit checking
 */
export class AddonService {
  /**
   * Get combined limits for user (plan + addons)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Combined limits
   */
  static async getCombinedLimits(userId) {
    try {
      const [subscriptionData, addonSummary] = await Promise.all([
        SubscriptionService.getCurrentSubscription(userId),
        UserAddon.getAddonSummary(userId)
      ]);

      const planLimits = subscriptionData.plan?.getPlanLimits() || {};
      
      return {
        // Plan limits
        planCredits: subscriptionData.plan?.isUnlimitedCredits ? -1 : (subscriptionData.plan?.credits || 0),
        planProjects: planLimits.projects || 0,
        planFilesPerDay: planLimits.filesPerDay || 0,
        
        // Addon limits
        addonCredits: addonSummary.totalRemainingCredits,
        addonProjects: addonSummary.totalRemainingProjects,
        addonFilesPerDay: addonSummary.totalRemainingFilesPerDay,
        
        // Combined totals
        totalCredits: subscriptionData.plan?.isUnlimitedCredits ? -1 : 
          (subscriptionData.plan?.credits || 0) + addonSummary.totalRemainingCredits,
        totalProjects: (planLimits.projects || 0) + addonSummary.totalRemainingProjects,
        totalFilesPerDay: (planLimits.filesPerDay || 0) + addonSummary.totalRemainingFilesPerDay,
        
        // Addon details
        addons: addonSummary.addons,
        totalAddons: addonSummary.totalAddons,
      };
    } catch (error) {
      logger.error('Error getting combined limits:', error);
      throw error;
    }
  }

  /**
   * Check if user can create a project
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Check result
   */
  static async canCreateProject(userId) {
    try {
      const [currentProjectCount, limits] = await Promise.all([
        Project.countUserProjects(userId),
        this.getCombinedLimits(userId)
      ]);

      const canCreate = currentProjectCount < limits.totalProjects;
      const remainingProjects = Math.max(0, limits.totalProjects - currentProjectCount);

      return {
        canCreate,
        currentCount: currentProjectCount,
        totalLimit: limits.totalProjects,
        remainingProjects,
        planLimit: limits.planProjects,
        addonLimit: limits.addonProjects,
      };
    } catch (error) {
      logger.error('Error checking project creation:', error);
      throw error;
    }
  }

  /**
   * Check if user can upload files
   * @param {string} userId - User ID
   * @param {number} [fileCount=1] - Number of files to upload
   * @returns {Promise<Object>} Check result
   */
  static async canUploadFiles(userId, fileCount = 1) {
    try {
      const [currentFileCount, limits] = await Promise.all([
        ChatMessage.countDailyFileUploads(userId),
        this.getCombinedLimits(userId)
      ]);

      const canUpload = (currentFileCount + fileCount) <= limits.totalFilesPerDay;
      const remainingFiles = Math.max(0, limits.totalFilesPerDay - currentFileCount);

      return {
        canUpload,
        currentCount: currentFileCount,
        totalLimit: limits.totalFilesPerDay,
        remainingFiles,
        planLimit: limits.planFilesPerDay,
        addonLimit: limits.addonFilesPerDay,
      };
    } catch (error) {
      logger.error('Error checking file upload:', error);
      throw error;
    }
  }

  /**
   * Consume project limit (prioritize plan limit, then addon)
   * @param {string} userId - User ID
   * @param {number} [amount=1] - Number of projects to consume
   * @returns {Promise<Object>} Consumption result
   */
  static async consumeProjectLimit(userId, amount = 1) {
    try {
      const [currentProjectCount, limits] = await Promise.all([
        Project.countUserProjects(userId),
        this.getCombinedLimits(userId)
      ]);

      // Check if within plan limits (no consumption needed for plan limits)
      if (currentProjectCount < limits.planProjects) {
        return {
          success: true,
          consumedFromAddon: false,
          remainingPlanProjects: limits.planProjects - currentProjectCount - amount,
          remainingAddonProjects: limits.addonProjects,
        };
      }

      // Need to consume from addon
      const addons = await UserAddon.findActiveByUserId(userId);
      let remainingToConsume = amount;

      for (const addon of addons) {
        if (remainingToConsume <= 0) break;
        
        const canConsume = Math.min(remainingToConsume, addon.remainingProjects);
        if (canConsume > 0) {
          await addon.consumeProjects(canConsume);
          remainingToConsume -= canConsume;
        }
      }

      const success = remainingToConsume === 0;
      
      return {
        success,
        consumedFromAddon: true,
        consumedAmount: amount - remainingToConsume,
        remainingPlanProjects: Math.max(0, limits.planProjects - currentProjectCount),
        remainingAddonProjects: limits.addonProjects - (amount - remainingToConsume),
      };
    } catch (error) {
      logger.error('Error consuming project limit:', error);
      throw error;
    }
  }

  /**
   * Consume file upload limit (prioritize plan limit, then addon)
   * @param {string} userId - User ID
   * @param {number} [amount=1] - Number of files to consume
   * @returns {Promise<Object>} Consumption result
   */
  static async consumeFileLimit(userId, amount = 1) {
    try {
      const [currentFileCount, limits] = await Promise.all([
        ChatMessage.countDailyFileUploads(userId),
        this.getCombinedLimits(userId)
      ]);

      // Check if within plan limits (no consumption needed for plan limits)
      if ((currentFileCount + amount) <= limits.planFilesPerDay) {
        return {
          success: true,
          consumedFromAddon: false,
          remainingPlanFiles: limits.planFilesPerDay - currentFileCount - amount,
          remainingAddonFiles: limits.addonFilesPerDay,
        };
      }

      // Calculate how many files need to come from addon
      const filesFromPlan = Math.max(0, limits.planFilesPerDay - currentFileCount);
      const filesFromAddon = amount - filesFromPlan;

      if (filesFromAddon <= 0) {
        return {
          success: true,
          consumedFromAddon: false,
          remainingPlanFiles: limits.planFilesPerDay - currentFileCount - amount,
          remainingAddonFiles: limits.addonFilesPerDay,
        };
      }

      // Need to consume from addon
      const addons = await UserAddon.findActiveByUserId(userId);
      let remainingToConsume = filesFromAddon;

      for (const addon of addons) {
        if (remainingToConsume <= 0) break;
        
        const canConsume = Math.min(remainingToConsume, addon.remainingFilesPerDay);
        if (canConsume > 0) {
          await addon.consumeFiles(canConsume);
          remainingToConsume -= canConsume;
        }
      }

      const success = remainingToConsume === 0;
      
      return {
        success,
        consumedFromAddon: true,
        consumedAmount: filesFromAddon - remainingToConsume,
        remainingPlanFiles: Math.max(0, limits.planFilesPerDay - currentFileCount - filesFromPlan),
        remainingAddonFiles: limits.addonFilesPerDay - (filesFromAddon - remainingToConsume),
      };
    } catch (error) {
      logger.error('Error consuming file limit:', error);
      throw error;
    }
  }

  /**
   * Get user's current usage and limits
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Usage and limits data
   */
  static async getUserUsageAndLimits(userId) {
    try {
      const [
        currentProjectCount,
        currentFileCount,
        remainingCredits,
        limits
      ] = await Promise.all([
        Project.countUserProjects(userId),
        ChatMessage.countDailyFileUploads(userId),
        CreditService.getUserCredits(userId),
        this.getCombinedLimits(userId)
      ]);

      return {
        // Current usage
        currentProjects: currentProjectCount,
        currentFilesPerDay: currentFileCount,
        currentCredits: remainingCredits,
        
        // Plan limits
        planCredits: limits.planCredits,
        planProjects: limits.planProjects,
        planFilesPerDay: limits.planFilesPerDay,
        
        // Addon limits
        addonCredits: limits.addonCredits,
        addonProjects: limits.addonProjects,
        addonFilesPerDay: limits.addonFilesPerDay,
        
        // Total limits
        totalCredits: limits.totalCredits,
        totalProjects: limits.totalProjects,
        totalFilesPerDay: limits.totalFilesPerDay,
        
        // Remaining limits
        remainingCredits: remainingCredits,
        remainingProjects: Math.max(0, limits.totalProjects - currentProjectCount),
        remainingFilesPerDay: Math.max(0, limits.totalFilesPerDay - currentFileCount),
        
        // Addon details
        addons: limits.addons,
        totalAddons: limits.totalAddons,
      };
    } catch (error) {
      logger.error('Error getting user usage and limits:', error);
      throw error;
    }
  }
}
