import Razorpay from 'razorpay';
import crypto from 'crypto';
import logger from '../config/logger.js';

/**
 * RazorpayService
 * Handles all Razorpay API interactions for payments and subscriptions
 */
export class RazorpayService {
  static instance = null;

  /**
   * Initialize Razorpay service
   * @returns {RazorpayService} Service instance
   */
  static initialize() {
    if (!this.instance) {
      this.instance = new RazorpayService();
    }
    return this.instance;
  }

  /**
   * Get Razorpay service instance
   * @returns {RazorpayService} Service instance
   */
  static getInstance() {
    if (!this.instance) {
      this.initialize();
    }
    return this.instance;
  }

  constructor() {
    this.razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET,
    });

    if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
      logger.warn('Razorpay credentials not found in environment variables');
    }
  }

  /**
   * Create a Razorpay order
   * @param {Object} orderData - Order data
   * @param {number} orderData.amount - Amount in paise
   * @param {string} orderData.currency - Currency code
   * @param {string} orderData.receipt - Receipt ID
   * @param {Object} orderData.notes - Additional notes
   * @returns {Promise<Object>} Created order
   */
  async createOrder(orderData) {
    try {
      const order = await this.razorpay.orders.create({
        amount: orderData.amount,
        currency: orderData.currency || 'INR',
        receipt: orderData.receipt,
        notes: orderData.notes || {},
      });

      logger.info(`Razorpay order created: ${order.id}`);
      return order;
    } catch (error) {
      logger.error('Error creating Razorpay order:', error);
      throw error;
    }
  }

  /**
   * Create a Razorpay plan for subscriptions
   * @param {Object} planData - Plan data
   * @param {number} planData.amount - Amount in paise
   * @param {string} planData.currency - Currency code
   * @param {string} planData.interval - Billing interval (monthly, yearly)
   * @param {number} planData.period - Billing period
   * @param {string} planData.item - Plan item details
   * @returns {Promise<Object>} Created plan
   */
  async createPlan(planData) {
    try {
      const plan = await this.razorpay.plans.create({
        period: planData.period || 1,
        interval: planData.interval || 'monthly',
        item: {
          name: planData.item.name,
          amount: planData.amount,
          currency: planData.currency || 'INR',
          description: planData.item.description,
        },
        notes: planData.notes || {},
      });

      logger.info(`Razorpay plan created: ${plan.id}`);
      return plan;
    } catch (error) {
      logger.error('Error creating Razorpay plan:', error);
      throw error;
    }
  }

  /**
   * Create a Razorpay subscription
   * @param {Object} subscriptionData - Subscription data
   * @param {string} subscriptionData.plan_id - Plan ID
   * @param {string} subscriptionData.customer_id - Customer ID
   * @param {number} subscriptionData.total_count - Total billing cycles
   * @param {Object} subscriptionData.notes - Additional notes
   * @returns {Promise<Object>} Created subscription
   */
  async createSubscription(subscriptionData) {
    try {
      const subscription = await this.razorpay.subscriptions.create({
        plan_id: subscriptionData.plan_id,
        customer_id: subscriptionData.customer_id,
        total_count: subscriptionData.total_count,
        quantity: subscriptionData.quantity || 1,
        notes: subscriptionData.notes || {},
        notify: subscriptionData.notify !== false,
      });

      logger.info(`Razorpay subscription created: ${subscription.id}`);
      return subscription;
    } catch (error) {
      logger.error('Error creating Razorpay subscription:', error);
      throw error;
    }
  }

  /**
   * Create a Razorpay customer
   * @param {Object} customerData - Customer data
   * @param {string} customerData.name - Customer name
   * @param {string} customerData.email - Customer email
   * @param {string} customerData.contact - Customer contact
   * @returns {Promise<Object>} Created customer
   */
  async createCustomer(customerData) {
    try {
      const customer = await this.razorpay.customers.create({
        name: customerData.name,
        email: customerData.email,
        contact: customerData.contact,
        notes: customerData.notes || {},
      });

      logger.info(`Razorpay customer created: ${customer.id}`);
      return customer;
    } catch (error) {
      logger.error('Error creating Razorpay customer:', error);
      throw error;
    }
  }

  /**
   * Fetch payment details
   * @param {string} paymentId - Payment ID
   * @returns {Promise<Object>} Payment details
   */
  async fetchPayment(paymentId) {
    try {
      const payment = await this.razorpay.payments.fetch(paymentId);
      return payment;
    } catch (error) {
      logger.error('Error fetching payment:', error);
      throw error;
    }
  }

  /**
   * Fetch subscription details
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise<Object>} Subscription details
   */
  async fetchSubscription(subscriptionId) {
    try {
      const subscription = await this.razorpay.subscriptions.fetch(subscriptionId);
      return subscription;
    } catch (error) {
      logger.error('Error fetching subscription:', error);
      throw error;
    }
  }

  /**
   * Cancel a subscription
   * @param {string} subscriptionId - Subscription ID
   * @param {boolean} cancelAtCycleEnd - Cancel at cycle end
   * @returns {Promise<Object>} Cancelled subscription
   */
  async cancelSubscription(subscriptionId, cancelAtCycleEnd = false) {
    try {
      const subscription = await this.razorpay.subscriptions.cancel(
        subscriptionId,
        cancelAtCycleEnd
      );

      logger.info(`Razorpay subscription cancelled: ${subscriptionId}`);
      return subscription;
    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  /**
   * Verify payment signature
   * @param {Object} paymentData - Payment data
   * @param {string} paymentData.razorpay_order_id - Order ID
   * @param {string} paymentData.razorpay_payment_id - Payment ID
   * @param {string} paymentData.razorpay_signature - Signature
   * @returns {boolean} True if signature is valid
   */
  verifyPaymentSignature(paymentData) {
    try {
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = paymentData;

      // Debug logging
      logger.debug('Verifying payment signature:', {
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature,
        hasSecret: !!process.env.RAZORPAY_KEY_SECRET
      });

      if (!process.env.RAZORPAY_KEY_SECRET) {
        logger.error('RAZORPAY_KEY_SECRET is not set');
        return false;
      }

      const body = razorpay_order_id + '|' + razorpay_payment_id;
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
        .update(body.toString())
        .digest('hex');

      logger.debug('Signature verification:', {
        body,
        expectedSignature,
        receivedSignature: razorpay_signature,
        match: expectedSignature === razorpay_signature
      });

      return expectedSignature === razorpay_signature;
    } catch (error) {
      logger.error('Error verifying payment signature:', error);
      return false;
    }
  }

  /**
   * Verify webhook signature
   * @param {string} body - Webhook body
   * @param {string} signature - Webhook signature
   * @param {string} secret - Webhook secret
   * @returns {boolean} True if signature is valid
   */
  verifyWebhookSignature(body, signature, secret) {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(body)
        .digest('hex');

      return expectedSignature === signature;
    } catch (error) {
      logger.error('Error verifying webhook signature:', error);
      return false;
    }
  }

  /**
   * Convert amount to paise (smallest currency unit)
   * @param {number} amount - Amount in rupees
   * @returns {number} Amount in paise
   */
  static convertToPaise(amount) {
    return Math.round(amount * 100);
  }

  /**
   * Convert amount from paise to rupees
   * @param {number} amount - Amount in paise
   * @returns {number} Amount in rupees
   */
  static convertFromPaise(amount) {
    return amount / 100;
  }
}
