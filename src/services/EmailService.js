import nodemailer from 'nodemailer';
import handlebars from 'handlebars';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import logger from '../config/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Email Service Class
 * Handles all email sending functionality with beautiful templates
 */
export class EmailService {
  static transporter = null;
  static templates = new Map();

  /**
   * Initialize email service with SMTP configuration
   */
  static async initialize() {
    try {
      // Check if SMTP credentials are configured
      if (!process.env.SMTP_PASSWORD || process.env.SMTP_PASSWORD === 'your_app_password_here') {
        logger.warn('SMTP password not configured or using placeholder. Email service will be initialized in test mode.');
        this.transporter = null;
        await this.loadTemplates();
        logger.info('Email service initialized in test mode (no SMTP credentials)');
        return;
      }

      // Create SMTP transporter
      const smtpPort = parseInt(process.env.SMTP_PORT || '465');
      const isSecure = process.env.SMTP_SECURE === 'true' || smtpPort === 465;

      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'smtpout.secureserver.net',
        port: smtpPort,
        secure: isSecure, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASSWORD,
        },
        tls: {
          // Do not fail on invalid certs for development
          rejectUnauthorized: process.env.NODE_ENV === 'production',
          // Force TLS version for better compatibility
          minVersion: 'TLSv1.2',
        },
        // Enable STARTTLS for port 587
        requireTLS: !isSecure,
        // Improved connection settings for GoDaddy SMTP
        connectionTimeout: 120000, // 2 minutes
        greetingTimeout: 60000, // 1 minute
        socketTimeout: 120000, // 2 minutes
        // Connection pooling for better performance
        pool: true,
        maxConnections: 5,
        maxMessages: 100,
        // Keep connections alive
        keepAlive: true,
        // Additional options for reliability
        debug: process.env.NODE_ENV === 'development',
        logger: process.env.NODE_ENV === 'development',
      });

      // Verify SMTP connection with timeout and fallback
      logger.info('Verifying SMTP connection...');
      try {
        // Add timeout to SMTP verification
        await Promise.race([
          this.verifyConnection(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('SMTP verification timeout')), 30000)
          )
        ]);
      } catch (verifyError) {
        logger.warn(`SMTP verification failed: ${verifyError.message}`);

        // In development, continue without SMTP but log the issue
        if (process.env.NODE_ENV === 'development') {
          logger.warn('Continuing in development mode without SMTP verification');
          logger.warn('Emails will be logged but may not be sent');
        } else {
          throw verifyError; // Fail in production
        }
      }

      // Load email templates
      await this.loadTemplates();

      logger.info('Email service initialized successfully');
    } catch (error) {
      logger.error('Error initializing email service:', error);
      // Don't throw error in development to allow server to start
      if (process.env.NODE_ENV === 'production') {
        throw error;
      } else {
        logger.warn('Email service initialization failed, continuing in test mode');
        this.transporter = null;
        await this.loadTemplates();
      }
    }
  }

  /**
   * Verify SMTP connection
   */
  static async verifyConnection() {
    try {
      await this.transporter.verify();
      logger.info('SMTP connection verified successfully');
    } catch (error) {
      logger.error('SMTP connection verification failed:', error);
      throw new Error('Failed to connect to SMTP server. Please check your email configuration.');
    }
  }

  /**
   * Load email templates from the templates directory
   */
  static async loadTemplates() {
    try {
      const templatesDir = path.join(__dirname, '../templates/email');
      
      // Load base template
      const baseTemplatePath = path.join(templatesDir, 'base.hbs');
      const baseTemplateContent = await fs.readFile(baseTemplatePath, 'utf-8');
      this.templates.set('base', handlebars.compile(baseTemplateContent));

      // Load OTP template
      const otpTemplatePath = path.join(templatesDir, 'otp-verification.hbs');
      const otpTemplateContent = await fs.readFile(otpTemplatePath, 'utf-8');
      this.templates.set('otp-verification', handlebars.compile(otpTemplateContent));

      // Load ticket confirmation template
      const ticketTemplatePath = path.join(templatesDir, 'ticket-confirmation.hbs');
      const ticketTemplateContent = await fs.readFile(ticketTemplatePath, 'utf-8');
      this.templates.set('ticket-confirmation', handlebars.compile(ticketTemplateContent));

      // Load purchase notification templates
      const subscriptionTemplatePath = path.join(templatesDir, 'subscription-purchase.hbs');
      const subscriptionTemplateContent = await fs.readFile(subscriptionTemplatePath, 'utf-8');
      this.templates.set('subscription-purchase', handlebars.compile(subscriptionTemplateContent));

      const addonTemplatePath = path.join(templatesDir, 'addon-purchase.hbs');
      const addonTemplateContent = await fs.readFile(addonTemplatePath, 'utf-8');
      this.templates.set('addon-purchase', handlebars.compile(addonTemplateContent));

      const paymentFailedTemplatePath = path.join(templatesDir, 'payment-failed.hbs');
      const paymentFailedTemplateContent = await fs.readFile(paymentFailedTemplatePath, 'utf-8');
      this.templates.set('payment-failed', handlebars.compile(paymentFailedTemplateContent));

      const subscriptionCancelledTemplatePath = path.join(templatesDir, 'subscription-cancelled.hbs');
      const subscriptionCancelledTemplateContent = await fs.readFile(subscriptionCancelledTemplatePath, 'utf-8');
      this.templates.set('subscription-cancelled', handlebars.compile(subscriptionCancelledTemplateContent));

      logger.info('Email templates loaded successfully');
    } catch (error) {
      logger.error('Error loading email templates:', error);
      throw error;
    }
  }

  /**
   * Send OTP verification email
   * @param {string} email - Recipient email address
   * @param {string} otp - OTP code
   * @param {string} action - Action type (signup, login, etc.)
   * @param {number} expiryMinutes - OTP expiry time in minutes
   * @returns {Promise<boolean>} Success status
   */
  static async sendOTPEmail(email, otp, action = 'verify', expiryMinutes = 5) {
    try {
      // In development mode, log the OTP for testing but still attempt to send email
      if (process.env.NODE_ENV === 'development') {
        logger.info(`🔐 DEVELOPMENT MODE - OTP for ${email}: ${otp} (Action: ${action}, Expires in: ${expiryMinutes} minutes)`);
      }

      // Check if transporter is configured
      if (!this.transporter) {
        logger.error('Email service not configured. Cannot send email.');
        // In development mode, still return true so OTP flow continues
        if (process.env.NODE_ENV === 'development') {
          logger.warn('Development mode: OTP logged above, email sending skipped');
          return true;
        }
        return false;
      }

      // Attempt to send email
      await this.attemptEmailSend(email, otp, action, expiryMinutes);

      // Log success message
      if (process.env.NODE_ENV === 'development') {
        logger.info(`📧 Email sent successfully to ${email} (OTP also logged above for testing)`);
      }

      return true;
    } catch (error) {
      logger.error('Error sending OTP email:', {
        error: error.message,
        stack: error.stack,
        email,
        action,
        smtpConfig: {
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT,
          secure: process.env.SMTP_SECURE,
          user: process.env.SMTP_USER
        }
      });
      return false;
    }
  }

  /**
   * Generate plain text version of OTP email
   * @param {string} otp - OTP code
   * @param {string} action - Action type
   * @param {number} expiryMinutes - Expiry time in minutes
   * @returns {string} Plain text email content
   */
  static generatePlainTextOTP(otp, action, expiryMinutes) {
    return `
Hello!

We received a request to verify your account with The Infini AI.

Your verification code is: ${otp}

This code expires in ${expiryMinutes} minutes.

Please enter this code to ${action} your account. If you didn't request this verification, please ignore this email.

For security reasons, never share this code with anyone.

Need help? Contact our support <NAME_EMAIL>

Best regards,
The Infini AI Team
    `.trim();
  }

  /**
   * Send welcome email (for future use)
   * @param {string} email - Recipient email address
   * @param {string} name - User name
   * @returns {Promise<boolean>} Success status
   */
  static async sendWelcomeEmail(email, name = 'there') {
    try {
      if (!this.transporter) {
        logger.warn(`Email service not configured. Would send welcome email to ${email}`);
        return true; // Return true in development to allow testing
      }

      const mailOptions = {
        from: {
          name: 'The Infini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: '🎉 Welcome to The Infini AI!',
        html: `
          <h2>Welcome to The Infini AI, ${name}!</h2>
          <p>Thank you for joining our platform. We're excited to have you on board!</p>
          <p>You can now start exploring our AI-powered features and capabilities.</p>
          <p>If you have any questions, feel free to reach out to our support team.</p>
          <p>Best regards,<br>The Infini AI Team</p>
        `,
        text: `Welcome to The Infini AI, ${name}! Thank you for joining our platform.`,
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      logger.info(`Welcome email sent successfully to ${email}`, {
        messageId: info.messageId,
      });

      return true;
    } catch (error) {
      logger.error('Error sending welcome email:', error);
      return false;
    }
  }

  /**
   * Test email configuration
   * @param {string} testEmail - Email to send test message to
   * @returns {Promise<boolean>} Success status
   */
  static async testEmailConfiguration(testEmail) {
    try {
      if (!this.transporter) {
        logger.warn(`Email service not configured. Would send test email to ${testEmail}`);
        return true; // Return true in development to allow testing
      }

      const mailOptions = {
        from: {
          name: 'The Infini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: testEmail,
        subject: '✅ Email Configuration Test - The Infini AI',
        html: `
          <h2>Email Configuration Test</h2>
          <p>This is a test email to verify that your SMTP configuration is working correctly.</p>
          <p>If you received this email, your email service is configured properly!</p>
          <p>Timestamp: ${new Date().toISOString()}</p>
        `,
        text: 'Email configuration test successful!',
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      logger.info(`Test email sent successfully to ${testEmail}`, {
        messageId: info.messageId,
      });

      return true;
    } catch (error) {
      logger.error('Error sending test email:', error);
      return false;
    }
  }

  /**
   * Send ticket confirmation email
   * @param {string} email - Recipient email
   * @param {string} userName - User name
   * @param {Object} ticket - Ticket details
   * @param {string} ticket.ticketId - Ticket ID
   * @param {string} ticket.subject - Ticket subject
   * @param {string} ticket.priority - Ticket priority
   * @param {string} ticket.status - Ticket status
   * @param {Date} ticket.createdAt - Ticket creation date
   * @returns {Promise<boolean>} Success status
   */
  static async sendTicketConfirmationEmail(email, userName, ticket) {
    try {
      if (!this.transporter) {
        logger.warn('Email service not initialized, skipping ticket confirmation email');
        return false;
      }

      // Format ticket data for template
      const templateData = {
        userName,
        ticket: {
          ticketId: ticket.ticketId,
          subject: ticket.subject,
          priority: ticket.priority,
          priorityDisplay: this.getPriorityDisplayName(ticket.priority),
          status: ticket.status,
          statusDisplay: this.getStatusDisplayName(ticket.status),
          createdAtFormatted: new Date(ticket.createdAt).toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
          })
        }
      };

      // Generate email content
      const ticketTemplate = this.templates.get('ticket-confirmation');
      const baseTemplate = this.templates.get('base');

      if (!ticketTemplate || !baseTemplate) {
        throw new Error('Email templates not loaded');
      }

      const ticketContent = ticketTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: 'Support Ticket Confirmation - The Infini AI',
        body: ticketContent,
        logoUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/assets/infini-logo.svg`,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'The Infini AI Support',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: `🎫 Support Ticket Confirmation - #${ticket.ticketId}`,
        html: fullEmailContent,
        text: this.generatePlainTextTicketConfirmation(userName, ticket),
      };

      const info = await this.transporter.sendMail(mailOptions);

      logger.info(`Ticket confirmation email sent successfully to ${email}`, {
        messageId: info.messageId,
        ticketId: ticket.ticketId,
      });

      return true;
    } catch (error) {
      logger.error('Error sending ticket confirmation email:', error);
      return false;
    }
  }

  /**
   * Get priority display name
   * @param {string} priority - Priority value
   * @returns {string} Display name
   * @private
   */
  static getPriorityDisplayName(priority) {
    const priorityMap = {
      'LOW': 'Low',
      'MEDIUM': 'Medium',
      'HIGH': 'High',
      'URGENT': 'Urgent'
    };
    return priorityMap[priority] || priority;
  }

  /**
   * Get status display name
   * @param {string} status - Status value
   * @returns {string} Display name
   * @private
   */
  static getStatusDisplayName(status) {
    const statusMap = {
      'OPEN': 'Open',
      'IN_PROGRESS': 'In Progress',
      'RESOLVED': 'Resolved',
      'CLOSED': 'Closed'
    };
    return statusMap[status] || status;
  }

  /**
   * Generate plain text ticket confirmation
   * @param {string} userName - User name
   * @param {Object} ticket - Ticket details
   * @returns {string} Plain text content
   * @private
   */
  static generatePlainTextTicketConfirmation(userName, ticket) {
    return `
Hello ${userName}!

Thank you for contacting The Infini AI support team. We have successfully received your support ticket.

Ticket Details:
- Ticket ID: #${ticket.ticketId}
- Subject: ${ticket.subject}
- Priority: ${this.getPriorityDisplayName(ticket.priority)}
- Status: ${this.getStatusDisplayName(ticket.status)}
- Submitted: ${new Date(ticket.createdAt).toLocaleString()}

What happens next?
- Our support team will review your ticket within 24 hours
- You will receive email updates as we work on your request
- For urgent issues, we aim to respond within 4 hours
- You can reference your ticket using ID: #${ticket.ticketId}

Thank you for choosing The Infini AI.

Best regards,
The Infini AI Support Team
    `.trim();
  }

  /**
   * Get email service status
   * @returns {Object} Service status information
   */
  static getStatus() {
    return {
      initialized: !!this.transporter,
      templatesLoaded: this.templates.size,
      smtpHost: process.env.SMTP_HOST || 'smtpout.secureserver.net',
      smtpPort: process.env.SMTP_PORT || '465',
      fromEmail: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
    };
  }

  /**
   * Helper method to attempt email sending with retry logic
   * @param {string} email - Recipient email
   * @param {string} otp - OTP code
   * @param {string} action - Action type
   * @param {number} expiryMinutes - Expiry minutes
   * @param {number} retryCount - Current retry attempt (default: 0)
   * @private
   */
  static async attemptEmailSend(email, otp, action, expiryMinutes, retryCount = 0) {
    const maxRetries = 3;
    const baseDelay = 5000; // 5 seconds
    // Prepare template data
    const templateData = {
      otp,
      action,
      expiryMinutes,
    };

    // Generate email content
    const otpTemplate = this.templates.get('otp-verification');
    const baseTemplate = this.templates.get('base');

    if (!otpTemplate || !baseTemplate) {
      throw new Error('Email templates not loaded');
    }

    const otpContent = otpTemplate(templateData);
    const fullEmailContent = baseTemplate({
      subject: 'Verify Your Account - The Infini AI',
      body: otpContent,
      logoUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/assets/infini-logo.svg`,
    });

    // Email options
    const mailOptions = {
      from: {
        name: 'The Infini AI',
        address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
      },
      to: email,
      subject: `🔐 Your verification code for The Infini AI`,
      html: fullEmailContent,
      text: this.generatePlainTextOTP(otp, action, expiryMinutes),
    };

    try {
      // Send email with increased timeout (90 seconds)
      const info = await Promise.race([
        this.transporter.sendMail(mailOptions),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Email sending timeout')), 90000)
        )
      ]);

      logger.info(`OTP email sent successfully to ${email}`, {
        messageId: info.messageId,
        action,
        retryCount,
      });

      return info;
    } catch (error) {
      // Retry logic with exponential backoff
      if (retryCount < maxRetries) {
        const delay = baseDelay * Math.pow(2, retryCount); // Exponential backoff
        logger.warn(`Email sending failed (attempt ${retryCount + 1}/${maxRetries + 1}), retrying in ${delay}ms: ${error.message}`);

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));

        // Recursive retry
        return this.attemptEmailSend(email, otp, action, expiryMinutes, retryCount + 1);
      } else {
        // All retries exhausted
        logger.error(`Email sending failed after ${maxRetries + 1} attempts: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Send subscription purchase confirmation email
   * @param {string} email - Recipient email address
   * @param {Object} data - Purchase data
   * @param {Object} data.user - User information
   * @param {Object} data.plan - Subscription plan
   * @param {Object} data.transaction - Payment transaction
   * @param {Object} data.subscription - User subscription
   * @param {string} data.invoiceUrl - Invoice download URL (optional)
   * @returns {Promise<boolean>} Success status
   */
  static async sendSubscriptionPurchaseEmail(email, data) {
    try {
      if (!this.transporter) {
        logger.warn('Email service not initialized - skipping subscription purchase email');
        return false;
      }

      const { user, plan, transaction, subscription, invoiceUrl } = data;

      // Prepare template data
      const templateData = {
        userName: user.name || user.email.split('@')[0],
        planName: plan.name,
        billingCycle: this.getBillingCycleText(plan.billingCycle),
        credits: plan.credits || 'Unlimited',
        projects: plan.getPlanLimits?.()?.projects || 'Unlimited',
        filesPerDay: plan.getPlanLimits?.()?.filesPerDay || 'Unlimited',
        amount: parseFloat(transaction.amount).toFixed(2),
        currency: transaction.currency || 'INR',
        nextBillingDate: subscription?.nextBillingDate ?
          new Date(subscription.nextBillingDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }) : null,
        invoiceUrl,
      };

      // Generate email content
      const subscriptionTemplate = this.templates.get('subscription-purchase');
      const baseTemplate = this.templates.get('base');

      if (!subscriptionTemplate || !baseTemplate) {
        throw new Error('Email templates not loaded');
      }

      const subscriptionContent = subscriptionTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: 'Subscription Activated - The Infini AI',
        body: subscriptionContent,
        logoUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/assets/infini-logo.svg`,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'The Infini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: `🎉 Your ${plan.name} subscription is now active!`,
        html: fullEmailContent,
        text: this.generatePlainTextPurchase('subscription', templateData),
      };

      const info = await this.transporter.sendMail(mailOptions);

      logger.info(`Subscription purchase email sent successfully to ${email}`, {
        messageId: info.messageId,
        planName: plan.name,
      });

      return true;
    } catch (error) {
      logger.error('Error sending subscription purchase email:', error);
      return false;
    }
  }

  /**
   * Send add-on purchase confirmation email
   * @param {string} email - Recipient email address
   * @param {Object} data - Purchase data
   * @param {Object} data.user - User information
   * @param {Object} data.plan - Add-on plan
   * @param {Object} data.transaction - Payment transaction
   * @param {string} data.invoiceUrl - Invoice download URL (optional)
   * @returns {Promise<boolean>} Success status
   */
  static async sendAddonPurchaseEmail(email, data) {
    try {
      if (!this.transporter) {
        logger.warn('Email service not initialized - skipping add-on purchase email');
        return false;
      }

      const { user, plan, transaction, invoiceUrl } = data;

      // Prepare template data
      const templateData = {
        userName: user.name || user.email.split('@')[0],
        planName: plan.name,
        credits: plan.credits,
        projects: plan.getPlanLimits?.()?.projects,
        filesPerDay: plan.getPlanLimits?.()?.filesPerDay,
        amount: parseFloat(transaction.amount).toFixed(2),
        currency: transaction.currency || 'INR',
        invoiceUrl,
      };

      // Generate email content
      const addonTemplate = this.templates.get('addon-purchase');
      const baseTemplate = this.templates.get('base');

      if (!addonTemplate || !baseTemplate) {
        throw new Error('Email templates not loaded');
      }

      const addonContent = addonTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: 'Add-on Purchase Confirmed - The Infini AI',
        body: addonContent,
        logoUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/assets/infini-logo.svg`,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'The Infini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: `⚡ Your ${plan.name} add-on is ready!`,
        html: fullEmailContent,
        text: this.generatePlainTextPurchase('addon', templateData),
      };

      const info = await this.transporter.sendMail(mailOptions);

      logger.info(`Add-on purchase email sent successfully to ${email}`, {
        messageId: info.messageId,
        planName: plan.name,
      });

      return true;
    } catch (error) {
      logger.error('Error sending add-on purchase email:', error);
      return false;
    }
  }

  /**
   * Send payment failure notification email
   * @param {string} email - Recipient email address
   * @param {Object} data - Payment failure data
   * @param {Object} data.user - User information
   * @param {Object} data.plan - Plan information
   * @param {Object} data.transaction - Failed transaction
   * @param {boolean} data.isSubscription - Whether it's a subscription payment
   * @returns {Promise<boolean>} Success status
   */
  static async sendPaymentFailedEmail(email, data) {
    try {
      if (!this.transporter) {
        logger.warn('Email service not initialized - skipping payment failed email');
        return false;
      }

      const { user, plan, transaction, isSubscription } = data;

      // Prepare template data
      const templateData = {
        userName: user.name || user.email.split('@')[0],
        planName: plan.name,
        amount: parseFloat(transaction.amount).toFixed(2),
        currency: transaction.currency || 'INR',
        paymentDate: new Date(transaction.createdAt).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        failureReason: transaction.failureReason || 'Payment processing failed',
        isSubscription,
        dashboardUrl: `${process.env.FRONTEND_URL || 'https://theinfiniai.live'}/dashboard`,
      };

      // Generate email content
      const paymentFailedTemplate = this.templates.get('payment-failed');
      const baseTemplate = this.templates.get('base');

      if (!paymentFailedTemplate || !baseTemplate) {
        throw new Error('Email templates not loaded');
      }

      const paymentFailedContent = paymentFailedTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: 'Payment Issue - The Infini AI',
        body: paymentFailedContent,
        logoUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/assets/infini-logo.svg`,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'The Infini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: `⚠️ Payment issue for your ${plan.name} ${isSubscription ? 'subscription' : 'add-on'}`,
        html: fullEmailContent,
        text: this.generatePlainTextPaymentFailed(templateData),
      };

      const info = await this.transporter.sendMail(mailOptions);

      logger.info(`Payment failed email sent successfully to ${email}`, {
        messageId: info.messageId,
        planName: plan.name,
      });

      return true;
    } catch (error) {
      logger.error('Error sending payment failed email:', error);
      return false;
    }
  }

  /**
   * Send subscription cancellation email
   * @param {string} email - Recipient email
   * @param {Object} data - Cancellation data
   * @param {Object} data.user - User information
   * @param {Object} data.plan - Cancelled plan information
   * @param {Object} data.subscription - Subscription information
   * @returns {Promise<boolean>} Success status
   */
  static async sendSubscriptionCancelledEmail(email, data) {
    try {
      if (!this.transporter) {
        logger.warn('Email transporter not initialized, skipping subscription cancellation email');
        return false;
      }

      const { user, plan, subscription } = data;

      // Prepare template data
      const templateData = {
        userName: user.name || user.email.split('@')[0],
        planName: plan.name,
        cancellationDate: new Date(subscription.cancelledAt).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        cancellationReason: subscription.cancellationReason || null,
        accessUntil: subscription.endDate ?
          new Date(subscription.endDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }) : null,
        dashboardUrl: `${process.env.FRONTEND_URL || 'https://theinfiniai.live'}/dashboard`,
        supportUrl: `${process.env.FRONTEND_URL || 'https://theinfiniai.live'}/support`,
      };

      // Generate email content
      const cancellationTemplate = this.templates.get('subscription-cancelled');
      const baseTemplate = this.templates.get('base');

      if (!cancellationTemplate || !baseTemplate) {
        throw new Error('Email templates not loaded');
      }

      const cancellationContent = cancellationTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: 'Subscription Cancelled - The Infini AI',
        body: cancellationContent,
        logoUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/assets/infini-logo.svg`,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'The Infini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: `😔 Your ${plan.name} subscription has been cancelled`,
        html: fullEmailContent,
        text: this.generatePlainTextCancellation(templateData),
      };

      const info = await this.transporter.sendMail(mailOptions);

      logger.info(`Subscription cancellation email sent successfully to ${email}`, {
        messageId: info.messageId,
        planName: plan.name,
      });

      return true;
    } catch (error) {
      logger.error('Error sending subscription cancellation email:', error);
      return false;
    }
  }

  /**
   * Get billing cycle text
   * @param {string} billingCycle - Billing cycle
   * @returns {string} Formatted billing cycle text
   */
  static getBillingCycleText(billingCycle) {
    const cycles = {
      'WEEKLY': 'week',
      'MONTHLY': 'month',
      'YEARLY': 'year',
      'ONE_TIME': 'one-time'
    };
    return cycles[billingCycle] || 'month';
  }

  /**
   * Generate plain text version of purchase email
   * @param {string} type - Purchase type (subscription/addon)
   * @param {Object} data - Template data
   * @returns {string} Plain text email content
   */
  static generatePlainTextPurchase(type, data) {
    const isSubscription = type === 'subscription';

    return `
${isSubscription ? 'Subscription Activated!' : 'Add-on Purchase Confirmed!'}

Hello ${data.userName}!

${isSubscription ?
  `Your ${data.planName} subscription has been successfully activated.` :
  `Your ${data.planName} add-on purchase has been processed successfully.`
}

Purchase Details:
- ${isSubscription ? 'Plan' : 'Add-on'}: ${data.planName}
${data.credits ? `- Credits: ${data.credits}` : ''}
${data.projects ? `- Projects: ${data.projects}` : ''}
${data.filesPerDay ? `- Files per day: ${data.filesPerDay}` : ''}
- Amount: ${data.currency} ${data.amount}
${data.nextBillingDate ? `- Next billing: ${data.nextBillingDate}` : ''}

${isSubscription ?
  'Your subscription is now active and will automatically renew on the next billing date.' :
  'Your additional resources are now available in your account.'
}

${data.invoiceUrl ? `Download your invoice: ${data.invoiceUrl}` : ''}

Thank you for choosing The Infini AI!

Best regards,
The Infini AI Team
    `.trim();
  }

  /**
   * Generate plain text version of payment failed email
   * @param {Object} data - Template data
   * @returns {string} Plain text email content
   */
  static generatePlainTextPaymentFailed(data) {
    return `
Payment Issue Detected

Hello ${data.userName},

We encountered an issue processing your payment for ${data.planName}.

Payment Details:
- Plan/Add-on: ${data.planName}
- Amount: ${data.currency} ${data.amount}
- Date: ${data.paymentDate}
- Status: Failed
- Reason: ${data.failureReason}

What you can do:
1. Check if your payment method has sufficient funds
2. Verify your card details are correct
3. Try a different payment method
4. Contact your bank if the issue persists

You can retry your payment from your dashboard: ${data.dashboardUrl}

${data.isSubscription ?
  'Your subscription will remain active for a grace period. Please retry payment within 7 days to avoid service interruption.' :
  'Your add-on purchase was not completed and no charges were made to your account.'
}

For assistance, contact <NAME_EMAIL>

Best regards,
The Infini AI Team
    `.trim();
  }

  /**
   * Generate plain text version of subscription cancellation email
   * @param {Object} data - Template data
   * @returns {string} Plain text email content
   */
  static generatePlainTextCancellation(data) {
    return `
Subscription Cancelled

Hello ${data.userName},

We're sorry to see you go! Your ${data.planName} subscription has been successfully cancelled as requested.

Cancellation Details:
- Plan: ${data.planName}
- Cancelled On: ${data.cancellationDate}${data.cancellationReason ? `
- Reason: ${data.cancellationReason}` : ''}${data.accessUntil ? `
- Access Until: ${data.accessUntil}` : ''}

What Happens Next:
• Your account has been downgraded to the Explorer Plan (free tier)
• You'll have 30 credits per week and access to basic features
• All your projects and data remain safe and accessible
• No further charges will be made to your payment method

Want to Come Back?
You can reactivate your subscription anytime from your dashboard. We'd love to have you back!

What you'll get back:
• Full access to premium features
• Higher credit limits and project quotas
• Priority support and advanced AI models
• All the tools you need to succeed

Visit your dashboard: ${data.dashboardUrl}
Contact support: ${data.supportUrl}

Feedback Welcome:
We're always looking to improve. If you have a moment, we'd love to hear about your experience and how we can serve you better in the future.

Thank you for being part of The Infini AI community. We hope to see you again soon!

Best regards,
The Infini AI Team
    `.trim();
  }
}
