import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import logger from '../config/logger.js';
import { S3Service } from './S3Service.js';
import { LogoUtils } from '../utils/logoUtils.js';
import { HtmlInvoiceService } from './HtmlInvoiceService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Invoice Service Class
 * Handles PDF invoice generation for subscriptions and add-ons
 */
export class InvoiceService {
  static invoicesDir = path.join(process.cwd(), 'storage', 'invoices');
  static invoiceMetadataFile = path.join(process.cwd(), 'storage', 'invoice-metadata.json');
  static invoiceMetadata = new Map();

  /**
   * Initialize invoice service
   */
  static async initialize() {
    try {
      // Create invoices directory if it doesn't exist
      await fs.mkdir(this.invoicesDir, { recursive: true });

      // Load existing invoice metadata
      try {
        const metadataContent = await fs.readFile(this.invoiceMetadataFile, 'utf-8');
        const metadata = JSON.parse(metadataContent);
        this.invoiceMetadata = new Map(Object.entries(metadata));
        logger.info(`Loaded ${this.invoiceMetadata.size} invoice metadata entries`);
      } catch (metadataError) {
        // File doesn't exist yet, that's okay
        logger.info('No existing invoice metadata found, starting fresh');
      }

      logger.info('Invoice service initialized');
    } catch (error) {
      logger.error('Error initializing invoice service:', error);
      throw error;
    }
  }

  /**
   * Save invoice metadata
   * @private
   */
  static async saveMetadata() {
    try {
      const metadataObj = Object.fromEntries(this.invoiceMetadata);
      await fs.writeFile(this.invoiceMetadataFile, JSON.stringify(metadataObj, null, 2));
    } catch (error) {
      logger.error('Error saving invoice metadata:', error);
    }
  }

  /**
   * Generate invoice for subscription purchase
   * @param {Object} data - Invoice data
   * @param {Object} data.transaction - Payment transaction
   * @param {Object} data.user - User information
   * @param {Object} data.plan - Subscription plan
   * @param {Object} data.subscription - User subscription (optional)
   * @returns {Promise<Object>} Invoice details
   */
  static async generateSubscriptionInvoice(data) {
    try {
      const { transaction, user, plan, subscription } = data;
      
      const invoiceData = {
        invoiceNumber: this.generateInvoiceNumber('SUB', transaction.id),
        type: 'subscription',
        date: new Date(),
        dueDate: new Date(),
        customer: {
          name: user.name || user.email,
          email: user.email,
          id: user.id,
        },
        items: [{
          description: `${plan.name} Subscription`,
          details: `${plan.planType} Plan - ${this.getBillingCycleText(plan.billingCycle)}`,
          quantity: 1,
          price: parseFloat(plan.price),
          total: parseFloat(plan.price),
          features: this.getPlanFeatures(plan),
        }],
        subtotal: parseFloat(plan.price),
        tax: 0, // Add tax calculation if needed
        total: parseFloat(plan.price),
        currency: plan.currency || 'INR',
        paymentMethod: transaction.paymentMethod || 'Online Payment',
        transactionId: transaction.razorpayPaymentId,
        orderId: transaction.razorpayOrderId,
        paidAt: transaction.paidAt || new Date(),
        nextBillingDate: subscription?.nextBillingDate,
      };

      return await this.generateBeautifulPDFInvoice(invoiceData);
    } catch (error) {
      logger.error('Error generating subscription invoice:', error);
      throw error;
    }
  }

  /**
   * Generate invoice for add-on purchase
   * @param {Object} data - Invoice data
   * @param {Object} data.transaction - Payment transaction
   * @param {Object} data.user - User information
   * @param {Object} data.plan - Add-on plan
   * @returns {Promise<Object>} Invoice details
   */
  static async generateAddonInvoice(data) {
    try {
      const { transaction, user, plan } = data;
      
      const invoiceData = {
        invoiceNumber: this.generateInvoiceNumber('ADD', transaction.id),
        type: 'addon',
        date: new Date(),
        dueDate: new Date(),
        customer: {
          name: user.name || user.email,
          email: user.email,
          id: user.id,
        },
        items: [{
          description: `${plan.name} Add-on`,
          details: 'One-time purchase - Additional resources',
          quantity: 1,
          price: parseFloat(plan.price),
          total: parseFloat(plan.price),
          features: this.getPlanFeatures(plan),
        }],
        subtotal: parseFloat(plan.price),
        tax: 0, // Add tax calculation if needed
        total: parseFloat(plan.price),
        currency: plan.currency || 'INR',
        paymentMethod: transaction.paymentMethod || 'Online Payment',
        transactionId: transaction.razorpayPaymentId,
        orderId: transaction.razorpayOrderId,
        paidAt: transaction.paidAt || new Date(),
      };

      return await this.generateBeautifulPDFInvoice(invoiceData);
    } catch (error) {
      logger.error('Error generating add-on invoice:', error);
      throw error;
    }
  }

  /**
   * Generate PDF invoice document
   * @param {Object} invoiceData - Invoice data
   * @returns {Promise<Object>} Generated invoice details
   */
  static async generatePDFInvoice(invoiceData) {
    try {
      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage([595, 842]); // A4 size
      const { width, height } = page.getSize();

      // Load fonts
      const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
      const regularFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

      // Get brand colors
      const colors = LogoUtils.getBrandColors();
      const primaryColor = this.hexToRgb(colors.primary);
      const secondaryColor = this.hexToRgb(colors.secondary);
      const darkColor = this.hexToRgb(colors.dark);
      const lightColor = this.hexToRgb(colors.light);
      const textColor = this.hexToRgb(colors.text);
      const accentColor = this.hexToRgb(colors.accent);
      const whiteColor = rgb(1, 1, 1);

      let yPosition = height - 60;

      // Header section with improved design
      await this.drawHeader(page, boldFont, regularFont, primaryColor, darkColor, yPosition, width);
      yPosition -= 120;

      // Invoice details section
      await this.drawInvoiceDetails(page, boldFont, regularFont, primaryColor, darkColor, textColor, invoiceData, yPosition, width);
      yPosition -= 120;

      // Billing information section
      await this.drawBillingInfo(page, boldFont, regularFont, darkColor, textColor, invoiceData, yPosition, width);
      yPosition -= 100;

      // Items table
      await this.drawItemsTable(page, boldFont, regularFont, primaryColor, darkColor, textColor, whiteColor, invoiceData, yPosition, width);
      yPosition -= 150;

      // Totals section
      await this.drawTotalsSection(page, boldFont, regularFont, primaryColor, darkColor, textColor, whiteColor, invoiceData, yPosition, width);

      // Footer
      await this.drawFooter(page, boldFont, regularFont, primaryColor, darkColor, width);

      // Generate PDF buffer
      const pdfBytes = await pdfDoc.save();
      
      // Save invoice
      const filename = `invoice-${invoiceData.invoiceNumber}.pdf`;
      const invoiceId = uuidv4();

      let filePath, downloadUrl, uploadResult = null;

      if (S3Service.isAvailable()) {
        // Save to S3
        uploadResult = await S3Service.uploadFile(
          pdfBytes,
          filename,
          'application/pdf',
          `invoices/${invoiceId}`
        );
        filePath = uploadResult.s3Key; // Fix: use s3Key instead of key
        downloadUrl = `/api/invoices/${invoiceId}/download`;

        // Store secure file mapping like chat messages
        S3Service.storeSecureFileMapping(
          uploadResult.secureFileId,
          uploadResult.s3Key,
          filename,
          'application/pdf'
        );
      } else {
        // Save locally
        filePath = path.join(this.invoicesDir, filename);
        await fs.writeFile(filePath, pdfBytes);
        downloadUrl = `/api/invoices/${invoiceId}/download`;
      }

      // Store invoice metadata
      const invoiceMetadata = {
        invoiceId,
        invoiceNumber: invoiceData.invoiceNumber,
        filename,
        filePath,
        downloadUrl,
        size: pdfBytes.length,
        createdAt: new Date().toISOString(),
        type: invoiceData.type,
        customerEmail: invoiceData.customer.email,
        amount: invoiceData.total,
        currency: invoiceData.currency,
        // Add S3 information if available
        ...(uploadResult && {
          s3Key: uploadResult.s3Key,
          s3Url: uploadResult.s3Url,
          secureFileId: uploadResult.secureFileId,
          storageType: 's3'
        }),
        ...(!uploadResult && {
          storageType: 'local'
        })
      };

      this.invoiceMetadata.set(invoiceId, invoiceMetadata);
      await this.saveMetadata();

      logger.info(`Invoice generated: ${filename}`);

      return invoiceMetadata;
    } catch (error) {
      logger.error('Error generating PDF invoice:', error);
      throw error;
    }
  }

  /**
   * Generate invoice number
   * @param {string} type - Invoice type (SUB, ADD)
   * @param {string} transactionId - Transaction ID
   * @returns {string} Invoice number
   */
  static generateInvoiceNumber(type, transactionId) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const shortId = transactionId.slice(-8).toUpperCase();
    return `INV-${type}-${year}${month}-${shortId}`;
  }

  /**
   * Get billing cycle text
   * @param {string} billingCycle - Billing cycle
   * @returns {string} Formatted billing cycle text
   */
  static getBillingCycleText(billingCycle) {
    const cycles = {
      'WEEKLY': 'Weekly',
      'MONTHLY': 'Monthly',
      'YEARLY': 'Yearly',
      'ONE_TIME': 'One-time'
    };
    return cycles[billingCycle] || 'Monthly';
  }

  /**
   * Get plan features for invoice
   * @param {Object} plan - Subscription plan
   * @returns {Array} Plan features
   */
  static getPlanFeatures(plan) {
    const features = [];

    if (plan.credits) {
      features.push(`${plan.credits} AI Credits`);
    }

    const limits = plan.getPlanLimits ? plan.getPlanLimits() : {};

    if (limits.projects) {
      features.push(`${limits.projects} Projects`);
    }

    if (limits.filesPerDay) {
      features.push(`${limits.filesPerDay} Files per day`);
    }

    if (plan.planType === 'PRO') {
      features.push('Unlimited AI Credits');
      features.push('Priority Support');
    }

    return features;
  }

  /**
   * Format date for invoice
   * @param {Date} date - Date to format
   * @returns {string} Formatted date
   */
  static formatDate(date) {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Get invoice by ID
   * @param {string} invoiceId - Invoice ID
   * @returns {Promise<Buffer>} Invoice PDF buffer
   */
  static async getInvoiceById(invoiceId) {
    try {
      // First try to get invoice metadata from this service
      let metadata = this.invoiceMetadata.get(invoiceId);

      // If not found, try to get from HtmlInvoiceService (for new HTML-to-PDF invoices)
      if (!metadata) {
        try {
          // Initialize HtmlInvoiceService to load its metadata
          await HtmlInvoiceService.initialize();
          return await HtmlInvoiceService.getInvoiceById(invoiceId);
        } catch (htmlError) {
          // If not found in HtmlInvoiceService either, throw error
          throw new Error('Invoice not found');
        }
      }

      // Try to get the file based on stored metadata (legacy invoices)
      if (S3Service.isAvailable() && metadata.storageType === 's3' && metadata.filePath) {
        // File is stored in S3
        try {
          return await S3Service.downloadFile(metadata.filePath);
        } catch (s3Error) {
          logger.warn('S3 invoice not found, trying local storage:', s3Error.message);
        }
      }

      // Try to find invoice locally
      try {
        let localPath = metadata.filePath;
        if (!path.isAbsolute(localPath)) {
          localPath = path.join(this.invoicesDir, metadata.filename);
        }
        return await fs.readFile(localPath);
      } catch (localError) {
        logger.warn('Local invoice not found:', localError.message);
      }

      throw new Error('Invoice file not found');
    } catch (error) {
      logger.error('Error retrieving invoice:', error);
      throw error;
    }
  }

  /**
   * Draw professional header with logo and company info
   * @param {Object} page - PDF page object
   * @param {Object} boldFont - Bold font
   * @param {Object} regularFont - Regular font
   * @param {Object} primaryColor - Primary color
   * @param {Object} darkColor - Dark color
   * @param {number} yPosition - Y position
   * @param {number} width - Page width
   */
  static async drawHeader(page, boldFont, regularFont, primaryColor, darkColor, yPosition, width) {
    // Draw header background
    page.drawRectangle({
      x: 0,
      y: yPosition - 10,
      width: width,
      height: 80,
      color: this.hexToRgb('#f8f9fa'),
    });

    // Company logo and name
    page.drawText('The Infini AI', {
      x: 50,
      y: yPosition + 30,
      size: 28,
      font: boldFont,
      color: primaryColor,
    });

    page.drawText('AI-Powered Solutions', {
      x: 50,
      y: yPosition + 5,
      size: 12,
      font: regularFont,
      color: darkColor,
    });

    // INVOICE text on the right
    page.drawText('INVOICE', {
      x: width - 150,
      y: yPosition + 30,
      size: 32,
      font: boldFont,
      color: darkColor,
    });
  }

  /**
   * Draw invoice details section
   * @param {Object} page - PDF page object
   * @param {Object} boldFont - Bold font
   * @param {Object} regularFont - Regular font
   * @param {Object} primaryColor - Primary color
   * @param {Object} darkColor - Dark color
   * @param {Object} textColor - Text color
   * @param {Object} invoiceData - Invoice data
   * @param {number} yPosition - Y position
   * @param {number} width - Page width
   */
  static async drawInvoiceDetails(page, boldFont, regularFont, primaryColor, darkColor, textColor, invoiceData, yPosition, width) {
    // Invoice details in a card-like design
    page.drawRectangle({
      x: 40,
      y: yPosition - 80,
      width: width - 80,
      height: 80,
      color: this.hexToRgb('#f8f9fa'),
      borderColor: this.hexToRgb('#e2e8f0'),
      borderWidth: 1,
    });

    // Invoice details
    const details = [
      { label: 'Invoice Number:', value: invoiceData.invoiceNumber },
      { label: 'Date:', value: new Date(invoiceData.date).toLocaleDateString() },
      { label: 'Due Date:', value: new Date(invoiceData.dueDate).toLocaleDateString() },
      { label: 'Payment Status:', value: 'Paid' }
    ];

    details.forEach((detail, index) => {
      const xPos = 60 + (index % 2) * 220;
      const yPos = yPosition - 25 - Math.floor(index / 2) * 25;

      page.drawText(detail.label, {
        x: xPos,
        y: yPos,
        size: 10,
        font: regularFont,
        color: textColor,
      });

      page.drawText(detail.value, {
        x: xPos + 100,
        y: yPos,
        size: 10,
        font: boldFont,
        color: darkColor,
      });
    });
  }

  /**
   * Draw billing information section
   * @param {Object} page - PDF page object
   * @param {Object} boldFont - Bold font
   * @param {Object} regularFont - Regular font
   * @param {Object} darkColor - Dark color
   * @param {Object} textColor - Text color
   * @param {Object} invoiceData - Invoice data
   * @param {number} yPosition - Y position
   * @param {number} width - Page width
   */
  static async drawBillingInfo(page, boldFont, regularFont, darkColor, textColor, invoiceData, yPosition, width) {
    // Billed To section (left side)
    page.drawText('Billed To:', {
      x: 50,
      y: yPosition,
      size: 14,
      font: boldFont,
      color: darkColor,
    });

    page.drawText(invoiceData.customer.name, {
      x: 50,
      y: yPosition - 25,
      size: 12,
      font: boldFont,
      color: darkColor,
    });

    page.drawText(invoiceData.customer.email, {
      x: 50,
      y: yPosition - 45,
      size: 10,
      font: regularFont,
      color: textColor,
    });

    // Company details (right side)
    const rightColumnX = width - 200;
    page.drawText('From:', {
      x: rightColumnX,
      y: yPosition,
      size: 14,
      font: boldFont,
      color: darkColor,
    });

    const companyInfo = [
      'The Infini AI',
      '<EMAIL>',
      'theinfiniai.live'
    ];

    companyInfo.forEach((info, index) => {
      page.drawText(info, {
        x: rightColumnX,
        y: yPosition - 25 - (index * 20),
        size: 10,
        font: index === 0 ? boldFont : regularFont,
        color: index === 0 ? darkColor : textColor,
      });
    });
  }

  /**
   * Draw items table with professional styling
   * @param {Object} page - PDF page object
   * @param {Object} boldFont - Bold font
   * @param {Object} regularFont - Regular font
   * @param {Object} primaryColor - Primary color
   * @param {Object} darkColor - Dark color
   * @param {Object} textColor - Text color
   * @param {Object} whiteColor - White color
   * @param {Object} invoiceData - Invoice data
   * @param {number} yPosition - Y position
   * @param {number} width - Page width
   */
  static async drawItemsTable(page, boldFont, regularFont, primaryColor, darkColor, textColor, whiteColor, invoiceData, yPosition, width) {
    // Table header
    page.drawRectangle({
      x: 50,
      y: yPosition - 35,
      width: width - 100,
      height: 35,
      color: primaryColor,
    });

    // Table headers
    const headers = [
      { text: 'Description', x: 70 },
      { text: 'Price', x: 280 },
      { text: 'Qty', x: 380 },
      { text: 'Total', x: 450 }
    ];

    headers.forEach(header => {
      page.drawText(header.text, {
        x: header.x,
        y: yPosition - 18,
        size: 12,
        font: boldFont,
        color: darkColor,
      });
    });

    yPosition -= 45;

    // Table rows
    invoiceData.items.forEach((item, index) => {
      // Alternate row background
      if (index % 2 === 0) {
        page.drawRectangle({
          x: 50,
          y: yPosition - 25,
          width: width - 100,
          height: 25,
          color: this.hexToRgb('#f8f9fa'),
        });
      }

      // Item details
      page.drawText(item.description, {
        x: 70,
        y: yPosition - 12,
        size: 10,
        font: regularFont,
        color: darkColor,
      });

      page.drawText(`${invoiceData.currency} ${(item.price || item.total).toFixed(2)}`, {
        x: 280,
        y: yPosition - 12,
        size: 10,
        font: regularFont,
        color: darkColor,
      });

      page.drawText((item.quantity || 1).toString(), {
        x: 380,
        y: yPosition - 12,
        size: 10,
        font: regularFont,
        color: darkColor,
      });

      page.drawText(`${invoiceData.currency} ${item.total.toFixed(2)}`, {
        x: 450,
        y: yPosition - 12,
        size: 10,
        font: boldFont,
        color: darkColor,
      });

      yPosition -= 30;
    });
  }

  /**
   * Draw totals section with professional styling
   * @param {Object} page - PDF page object
   * @param {Object} boldFont - Bold font
   * @param {Object} regularFont - Regular font
   * @param {Object} primaryColor - Primary color
   * @param {Object} darkColor - Dark color
   * @param {Object} textColor - Text color
   * @param {Object} whiteColor - White color
   * @param {Object} invoiceData - Invoice data
   * @param {number} yPosition - Y position
   * @param {number} width - Page width
   */
  static async drawTotalsSection(page, boldFont, regularFont, primaryColor, darkColor, textColor, whiteColor, invoiceData, yPosition, width) {
    const totalsX = width - 200;
    yPosition -= 40;

    // Subtotal
    page.drawText('Subtotal:', {
      x: totalsX,
      y: yPosition,
      size: 11,
      font: regularFont,
      color: textColor,
    });

    page.drawText(`${invoiceData.currency} ${invoiceData.subtotal.toFixed(2)}`, {
      x: totalsX + 80,
      y: yPosition,
      size: 11,
      font: regularFont,
      color: darkColor,
    });

    yPosition -= 20;

    // Tax (if any)
    if (invoiceData.tax && invoiceData.tax > 0) {
      page.drawText('Tax:', {
        x: totalsX,
        y: yPosition,
        size: 11,
        font: regularFont,
        color: textColor,
      });

      page.drawText(`${invoiceData.currency} ${invoiceData.tax.toFixed(2)}`, {
        x: totalsX + 80,
        y: yPosition,
        size: 11,
        font: regularFont,
        color: darkColor,
      });

      yPosition -= 20;
    }

    // Total with background
    page.drawRectangle({
      x: totalsX - 10,
      y: yPosition - 25,
      width: 140,
      height: 30,
      color: primaryColor,
    });

    page.drawText('Total:', {
      x: totalsX,
      y: yPosition - 8,
      size: 12,
      font: boldFont,
      color: darkColor,
    });

    page.drawText(`${invoiceData.currency} ${invoiceData.total.toFixed(2)}`, {
      x: totalsX + 80,
      y: yPosition - 8,
      size: 12,
      font: boldFont,
      color: darkColor,
    });
  }

  /**
   * Draw footer with company information
   * @param {Object} page - PDF page object
   * @param {Object} boldFont - Bold font
   * @param {Object} regularFont - Regular font
   * @param {Object} primaryColor - Primary color
   * @param {Object} darkColor - Dark color
   * @param {number} width - Page width
   */
  static async drawFooter(page, boldFont, regularFont, primaryColor, darkColor, width) {
    // Footer background
    page.drawRectangle({
      x: 0,
      y: 0,
      width: width,
      height: 60,
      color: this.hexToRgb('#f8f9fa'),
    });

    // Footer text
    page.drawText('Thank you for your business!', {
      x: 50,
      y: 35,
      size: 12,
      font: boldFont,
      color: primaryColor,
    });

    page.drawText('For support, contact <NAME_EMAIL>', {
      x: 50,
      y: 15,
      size: 10,
      font: regularFont,
      color: darkColor,
    });
  }

  /**
   * Generate beautiful HTML-to-PDF invoice with logo (RECOMMENDED)
   * @param {Object} invoiceData - Invoice data
   * @returns {Promise<Object>} Generated invoice details
   */
  static async generateBeautifulPDFInvoice(invoiceData) {
    try {
      // Initialize HTML invoice service
      await HtmlInvoiceService.initialize();

      // Generate beautiful PDF using HTML template and Puppeteer
      const result = await HtmlInvoiceService.generateInvoice(invoiceData);

      logger.info(`Beautiful PDF Invoice generated: ${result.filename}`);

      return {
        invoiceId: result.invoiceId,
        invoiceNumber: result.invoiceNumber,
        filename: result.filename,
        filePath: result.filePath,
        downloadUrl: result.downloadUrl,
        htmlUrl: result.htmlUrl,
        createdAt: result.createdAt,
        customer: result.customer,
        total: result.total,
        currency: result.currency,
        type: 'beautiful-pdf'
      };
    } catch (error) {
      logger.error('Error generating beautiful PDF invoice:', error);
      throw error;
    }
  }

  /**
   * Convert hex color to RGB values for pdf-lib
   * @param {string} hex - Hex color code
   * @returns {Object} RGB object for pdf-lib
   */
  static hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
      return rgb(0, 0, 0); // Default to black
    }
    return rgb(
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    );
  }
}
