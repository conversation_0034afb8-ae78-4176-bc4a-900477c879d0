import { LLMModel } from '../models/chat/LLMModel.js';
import logger from '../config/logger.js';

/**
 * LLM Model Data Service
 * Handles initialization and management of LLM model data
 */
export class LLMModelDataService {
  /**
   * Initialize all LLM models in the database
   */
  static async initializeModels() {
    try {
      logger.info('Initializing LLM models database...');

      // Clear existing models (optional - for fresh start)
      // await LLMModel.destroy({ where: {} });

      const models = [
        ...this.getOpenAIModels(),
        ...this.getAnthropicModels(),
        ...this.getGoogleModels(),
        ...this.getDeepSeekModels(),
        ...this.getMetaModels(),
      ];

      for (const modelData of models) {
        try {
          const existingModel = await LLMModel.getModelById(modelData.modelId);
          if (!existingModel) {
            await LLMModel.createModel(modelData);
            logger.debug(`Created model: ${modelData.displayName}`);
          } else {
            logger.debug(`Model already exists: ${modelData.displayName}`);
          }
        } catch (error) {
          logger.error(`Error creating model ${modelData.displayName}:`, error);
        }
      }

      logger.info('LLM models database initialization completed');
    } catch (error) {
      logger.error('Error initializing LLM models:', error);
      throw error;
    }
  }

  /**
   * Get OpenAI models configuration
   */
  static getOpenAIModels() {
    return [
      {
        provider: 'OPENAI',
        modelName: 'GPT-4o',
        modelId: 'gpt-4o',
        displayName: 'GPT-4o',
        description: 'Most advanced GPT-4 model with improved reasoning, coding, and multimodal capabilities',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/openai-logo.png',
        pricing: {
          inputTokens: 2.50, // per 1M tokens
          outputTokens: 10.00,
          currency: 'USD',
        },
        contextWindow: 128000,
        maxOutputTokens: 16384,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'OPENAI',
        modelName: 'GPT-4o-mini',
        modelId: 'gpt-4o-mini',
        displayName: 'GPT-4o Mini',
        description: 'Smaller, faster, and more affordable version of GPT-4o',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/openai-logo.png',
        pricing: {
          inputTokens: 0.15,
          outputTokens: 0.60,
          currency: 'USD',
        },
        contextWindow: 128000,
        maxOutputTokens: 16384,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'OPENAI',
        modelName: 'GPT-4-Turbo',
        modelId: 'gpt-4-turbo',
        displayName: 'GPT-4 Turbo',
        description: 'High-performance GPT-4 model optimized for speed and efficiency',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/openai-logo.png',
        pricing: {
          inputTokens: 10.00,
          outputTokens: 30.00,
          currency: 'USD',
        },
        contextWindow: 128000,
        maxOutputTokens: 4096,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'OPENAI',
        modelName: 'GPT-3.5-Turbo',
        modelId: 'gpt-3.5-turbo',
        displayName: 'GPT-3.5 Turbo',
        description: 'Fast and efficient model for most conversational and text generation tasks',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: false,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: false,
        },
        logoUrl: '/assets/logos/openai-logo.png',
        pricing: {
          inputTokens: 0.50,
          outputTokens: 1.50,
          currency: 'USD',
        },
        contextWindow: 16385,
        maxOutputTokens: 4096,
        supportsVision: false,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
    ];
  }

  /**
   * Get Anthropic models configuration
   */
  static getAnthropicModels() {
    return [
      {
        provider: 'ANTHROPIC',
        modelName: 'Claude-4-Opus',
        modelId: 'claude-opus-4-20250514',
        displayName: 'Claude 4 Opus',
        description: 'Most powerful and capable Claude model with superior reasoning capabilities',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          extendedThinking: true,
        },
        logoUrl: '/assets/logos/anthropic-logo.png',
        pricing: {
          inputTokens: 15.00,
          outputTokens: 75.00,
          currency: 'USD',
        },
        contextWindow: 200000,
        maxOutputTokens: 32000,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'ANTHROPIC',
        modelName: 'Claude-4-Sonnet',
        modelId: 'claude-sonnet-4-20250514',
        displayName: 'Claude 4 Sonnet',
        description: 'High-performance model with exceptional reasoning capabilities',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          extendedThinking: true,
        },
        logoUrl: '/assets/logos/anthropic-logo.png',
        pricing: {
          inputTokens: 3.00,
          outputTokens: 15.00,
          currency: 'USD',
        },
        contextWindow: 200000,
        maxOutputTokens: 64000,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'ANTHROPIC',
        modelName: 'Claude-3.7-Sonnet',
        modelId: 'claude-3-7-sonnet-20250219',
        displayName: 'Claude 3.7 Sonnet',
        description: 'High-performance model with early extended thinking capabilities',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          extendedThinking: true,
        },
        logoUrl: '/assets/logos/anthropic-logo.png',
        pricing: {
          inputTokens: 3.00,
          outputTokens: 15.00,
          currency: 'USD',
        },
        contextWindow: 200000,
        maxOutputTokens: 64000,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'ANTHROPIC',
        modelName: 'Claude-3.5-Sonnet',
        modelId: 'claude-3-5-sonnet-20241022',
        displayName: 'Claude 3.5 Sonnet',
        description: 'Previous intelligent model with high level of capability',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/anthropic-logo.png',
        pricing: {
          inputTokens: 3.00,
          outputTokens: 15.00,
          currency: 'USD',
        },
        contextWindow: 200000,
        maxOutputTokens: 8192,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'ANTHROPIC',
        modelName: 'Claude-3.5-Haiku',
        modelId: 'claude-3-5-haiku-20241022',
        displayName: 'Claude 3.5 Haiku',
        description: 'Fastest Claude model with intelligence at blazing speeds',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: false,
        },
        logoUrl: '/assets/logos/anthropic-logo.png',
        pricing: {
          inputTokens: 0.80,
          outputTokens: 4.00,
          currency: 'USD',
        },
        contextWindow: 200000,
        maxOutputTokens: 8192,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
    ];
  }

  /**
   * Get Google Gemini models configuration
   */
  static getGoogleModels() {
    return [
      {
        provider: 'GOOGLE',
        modelName: 'Gemini-2.5-Pro',
        modelId: 'gemini-2.5-pro',
        displayName: 'Gemini 2.5 Pro',
        description: 'Most advanced thinking model with maximum accuracy and cutting-edge performance',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          videoUnderstanding: true,
          audioUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          thinking: true,
          codeExecution: true,
        },
        logoUrl: '/assets/logos/google-logo.png',
        pricing: {
          inputTokens: 1.25,
          outputTokens: 5.00,
          currency: 'USD',
        },
        contextWindow: 1048576,
        maxOutputTokens: 65536,
        supportsVision: true,
        supportsAudio: true,
        supportsCodeExecution: true,
      },
      {
        provider: 'GOOGLE',
        modelName: 'Gemini-2.5-Flash',
        modelId: 'gemini-2.5-flash',
        displayName: 'Gemini 2.5 Flash',
        description: 'Best price-performance model with versatile capabilities and adaptive thinking',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          videoUnderstanding: true,
          audioUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          thinking: true,
          codeExecution: true,
        },
        logoUrl: '/assets/logos/google-logo.png',
        pricing: {
          inputTokens: 0.075,
          outputTokens: 0.30,
          currency: 'USD',
        },
        contextWindow: 1048576,
        maxOutputTokens: 65536,
        supportsVision: true,
        supportsAudio: true,
        supportsCodeExecution: true,
      },
      {
        provider: 'GOOGLE',
        modelName: 'Gemini-2.0-Flash',
        modelId: 'gemini-2.0-flash',
        displayName: 'Gemini 2.0 Flash',
        description: 'Next-generation features with enhanced capabilities and speed',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          videoUnderstanding: true,
          audioUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          codeExecution: true,
        },
        logoUrl: '/assets/logos/google-logo.png',
        pricing: {
          inputTokens: 0.075,
          outputTokens: 0.30,
          currency: 'USD',
        },
        contextWindow: 1048576,
        maxOutputTokens: 8192,
        supportsVision: true,
        supportsAudio: true,
        supportsCodeExecution: true,
      },
      {
        provider: 'GOOGLE',
        modelName: 'Gemini-1.5-Pro',
        modelId: 'gemini-1.5-pro',
        displayName: 'Gemini 1.5 Pro',
        description: 'Medium-sized multimodal model optimized for reasoning tasks with long context',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          videoUnderstanding: true,
          audioUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          codeExecution: true,
        },
        logoUrl: '/assets/logos/google-logo.png',
        pricing: {
          inputTokens: 1.25,
          outputTokens: 5.00,
          currency: 'USD',
        },
        contextWindow: 2097152,
        maxOutputTokens: 8192,
        supportsVision: true,
        supportsAudio: true,
        supportsCodeExecution: true,
      },
      {
        provider: 'GOOGLE',
        modelName: 'Gemini-1.5-Flash',
        modelId: 'gemini-1.5-flash',
        displayName: 'Gemini 1.5 Flash',
        description: 'Fast and versatile multimodal model for scaling across diverse tasks',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          videoUnderstanding: true,
          audioUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          codeExecution: true,
        },
        logoUrl: '/assets/logos/google-logo.png',
        pricing: {
          inputTokens: 0.075,
          outputTokens: 0.30,
          currency: 'USD',
        },
        contextWindow: 1048576,
        maxOutputTokens: 8192,
        supportsVision: true,
        supportsAudio: true,
        supportsCodeExecution: true,
      },
    ];
  }

  /**
   * Get DeepSeek models configuration
   */
  static getDeepSeekModels() {
    return [
      {
        provider: 'DEEPSEEK',
        modelName: 'DeepSeek-R1',
        modelId: 'deepseek-reasoner',
        displayName: 'DeepSeek R1',
        description: 'Advanced reasoning model with performance on par with OpenAI o1',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: false,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
          thinking: true,
          chainOfThought: true,
        },
        logoUrl: '/assets/logos/deepseek-logo.png',
        pricing: {
          inputTokens: 0.55,
          outputTokens: 2.19,
          currency: 'USD',
        },
        contextWindow: 64000,
        maxOutputTokens: 64000,
        supportsVision: false,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'DEEPSEEK',
        modelName: 'DeepSeek-V3',
        modelId: 'deepseek-chat',
        displayName: 'DeepSeek V3',
        description: 'High-performance general-purpose model for various tasks',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: false,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/deepseek-logo.png',
        pricing: {
          inputTokens: 0.27,
          outputTokens: 1.10,
          currency: 'USD',
        },
        contextWindow: 64000,
        maxOutputTokens: 8000,
        supportsVision: false,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
    ];
  }

  /**
   * Get Meta Llama models configuration
   */
  static getMetaModels() {
    return [
      {
        provider: 'META',
        modelName: 'Llama-3.3-70B',
        modelId: 'llama-3.3-70b-instruct',
        displayName: 'Llama 3.3 70B',
        description: 'Latest Llama model with improved performance and efficiency',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: false,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/meta-logo.png',
        pricing: {
          inputTokens: 0.60,
          outputTokens: 0.60,
          currency: 'USD',
        },
        contextWindow: 131072,
        maxOutputTokens: 4096,
        supportsVision: false,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'META',
        modelName: 'Llama-3.2-90B-Vision',
        modelId: 'llama-3.2-90b-vision-instruct',
        displayName: 'Llama 3.2 90B Vision',
        description: 'Large multimodal model with advanced text and visual intelligence',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/meta-logo.png',
        pricing: {
          inputTokens: 1.20,
          outputTokens: 1.20,
          currency: 'USD',
        },
        contextWindow: 131072,
        maxOutputTokens: 4096,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'META',
        modelName: 'Llama-3.2-11B-Vision',
        modelId: 'llama-3.2-11b-vision-instruct',
        displayName: 'Llama 3.2 11B Vision',
        description: 'Efficient multimodal model for vision and text tasks',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: true,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/meta-logo.png',
        pricing: {
          inputTokens: 0.35,
          outputTokens: 0.35,
          currency: 'USD',
        },
        contextWindow: 131072,
        maxOutputTokens: 4096,
        supportsVision: true,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'META',
        modelName: 'Llama-3.1-405B',
        modelId: 'llama-3.1-405b-instruct',
        displayName: 'Llama 3.1 405B',
        description: 'Largest and most capable Llama model for complex reasoning tasks',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: false,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/meta-logo.png',
        pricing: {
          inputTokens: 2.70,
          outputTokens: 2.70,
          currency: 'USD',
        },
        contextWindow: 131072,
        maxOutputTokens: 4096,
        supportsVision: false,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'META',
        modelName: 'Llama-3.1-70B',
        modelId: 'llama-3.1-70b-instruct',
        displayName: 'Llama 3.1 70B',
        description: 'High-performance model balancing capability and efficiency',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: false,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/meta-logo.png',
        pricing: {
          inputTokens: 0.90,
          outputTokens: 0.90,
          currency: 'USD',
        },
        contextWindow: 131072,
        maxOutputTokens: 4096,
        supportsVision: false,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
      {
        provider: 'META',
        modelName: 'Llama-3.1-8B',
        modelId: 'llama-3.1-8b-instruct',
        displayName: 'Llama 3.1 8B',
        description: 'Compact and efficient model for lightweight applications',
        capabilities: {
          textGeneration: true,
          imageUnderstanding: false,
          codeGeneration: true,
          functionCalling: true,
          jsonMode: true,
          streaming: true,
          reasoning: true,
        },
        logoUrl: '/assets/logos/meta-logo.png',
        pricing: {
          inputTokens: 0.20,
          outputTokens: 0.20,
          currency: 'USD',
        },
        contextWindow: 131072,
        maxOutputTokens: 4096,
        supportsVision: false,
        supportsAudio: false,
        supportsCodeExecution: true,
      },
    ];
  }
}
