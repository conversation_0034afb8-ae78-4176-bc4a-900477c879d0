import { UserTicket } from '../models/support/UserTicket.js';
import { User, UserProfile } from '../models/user/index.js';
import { EmailService } from './EmailService.js';
import { ResponseUtil } from '../utils/response.js';
import { ERROR_MESSAGES } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * Support Service
 * Handles customer support ticket operations and email notifications
 */
class SupportService {
  /**
   * Create a new support ticket
   * @param {string} userId - User ID
   * @param {Object} ticketData - Ticket data
   * @param {string} ticketData.subject - Ticket subject
   * @param {string} ticketData.description - Ticket description
   * @param {string} [ticketData.attachmentUrl] - Optional attachment URL
   * @param {string} [ticketData.techDetails] - Optional technical details
   * @param {'LOW'|'MEDIUM'|'HIGH'|'URGENT'} ticketData.priority - Ticket priority
   * @returns {Promise<Object>} Created ticket with user info
   */
  static async createTicket(userId, ticketData) {
    try {
      // Verify user exists and is active
      const user = await User.findByPk(userId);
      if (!user || !user.isActive) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Get user profile for name information
      const userProfile = await UserProfile.findByUserId(userId);

      // Create the ticket
      const ticket = await UserTicket.createTicket({
        userId,
        subject: ticketData.subject,
        description: ticketData.description,
        attachmentUrl: ticketData.attachmentUrl,
        attachmentName: ticketData.attachmentName,
        attachmentType: ticketData.attachmentType,
        attachmentSize: ticketData.attachmentSize,
        attachmentSecureId: ticketData.attachmentSecureId,
        attachmentS3Url: ticketData.attachmentS3Url,
        attachmentS3Key: ticketData.attachmentS3Key,
        attachmentStorageType: ticketData.attachmentStorageType,
        techDetails: ticketData.techDetails,
        priority: ticketData.priority || 'MEDIUM',
      });

      // Send confirmation email
      await this.sendTicketConfirmationEmail(user, userProfile, ticket);

      logger.info(`Support ticket created successfully for user ${userId}`, {
        ticketId: ticket.id,
        subject: ticket.subject,
        priority: ticket.priority
      });

      return {
        ticket: UserTicket.formatForResponse(ticket),
        user: {
          id: user.id,
          email: user.email,
          mobile: user.mobile,
          firstName: userProfile?.firstName,
          lastName: userProfile?.lastName
        }
      };
    } catch (error) {
      logger.error('Error creating support ticket:', error);
      throw error;
    }
  }

  /**
   * Get user's support tickets
   * @param {string} userId - User ID
   * @param {Object} [options] - Query options
   * @param {number} [options.page=1] - Page number
   * @param {number} [options.limit=10] - Items per page
   * @param {string} [options.status] - Filter by status
   * @param {string} [options.priority] - Filter by priority
   * @returns {Promise<Object>} Paginated tickets
   */
  static async getUserTickets(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        priority
      } = options;

      const offset = (page - 1) * limit;

      const result = await UserTicket.findByUserId(userId, {
        limit: parseInt(limit),
        offset,
        status,
        priority
      });

      const totalPages = Math.ceil(result.total / limit);

      return {
        tickets: result.tickets.map(ticket => UserTicket.formatForResponse(ticket)),
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: result.total,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };
    } catch (error) {
      logger.error('Error fetching user tickets:', error);
      throw error;
    }
  }

  /**
   * Get a specific ticket by ID for a user
   * @param {string} ticketId - Ticket ID
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} Ticket details or null
   */
  static async getTicketById(ticketId, userId) {
    try {
      const ticket = await UserTicket.findByIdAndUserId(ticketId, userId);
      
      if (!ticket) {
        return null;
      }

      const formattedTicket = UserTicket.formatForResponse(ticket);

      return {
        ...formattedTicket,
        user: {
          id: ticket.user.id,
          email: ticket.user.email,
          mobile: ticket.user.mobile
        }
      };
    } catch (error) {
      logger.error('Error fetching ticket by ID:', error);
      throw error;
    }
  }

  /**
   * Get ticket statistics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Ticket statistics
   */
  static async getUserTicketStats(userId) {
    try {
      const stats = await UserTicket.getTicketStats(userId);
      return stats;
    } catch (error) {
      logger.error('Error fetching user ticket stats:', error);
      throw error;
    }
  }

  /**
   * Send ticket confirmation email to user
   * @param {Object} user - User object
   * @param {Object} userProfile - User profile object
   * @param {Object} ticket - Ticket object
   * @private
   */
  static async sendTicketConfirmationEmail(user, userProfile, ticket) {
    try {
      const email = user.email;
      if (!email) {
        logger.warn(`No email found for user ${user.id}, skipping confirmation email`);
        return;
      }

      const userName = userProfile 
        ? `${userProfile.firstName || ''} ${userProfile.lastName || ''}`.trim() || 'Valued Customer'
        : 'Valued Customer';

      await EmailService.sendTicketConfirmationEmail(
        email,
        userName,
        {
          ticketId: ticket.id,
          subject: ticket.subject,
          priority: ticket.priority,
          status: ticket.status,
          createdAt: ticket.createdAt
        }
      );

      logger.info(`Ticket confirmation email sent to ${email}`, {
        ticketId: ticket.id,
        userId: user.id
      });
    } catch (error) {
      logger.error('Error sending ticket confirmation email:', error);
      // Don't throw error here as ticket creation should succeed even if email fails
    }
  }

  /**
   * Validate ticket data
   * @param {Object} ticketData - Ticket data to validate
   * @returns {Object} Validation result
   */
  static validateTicketData(ticketData) {
    const errors = [];

    if (!ticketData.subject || ticketData.subject.trim().length === 0) {
      errors.push('Subject is required');
    } else if (ticketData.subject.length > 255) {
      errors.push('Subject must be less than 255 characters');
    }

    if (!ticketData.description || ticketData.description.trim().length === 0) {
      errors.push('Description is required');
    } else if (ticketData.description.length > 5000) {
      errors.push('Description must be less than 5000 characters');
    }

    if (ticketData.priority && !['LOW', 'MEDIUM', 'HIGH', 'URGENT'].includes(ticketData.priority)) {
      errors.push('Priority must be one of: LOW, MEDIUM, HIGH, URGENT');
    }

    if (ticketData.techDetails && ticketData.techDetails.length > 5000) {
      errors.push('Technical details must be less than 5000 characters');
    }

    // Note: attachmentUrl validation removed as it's now handled by file upload middleware

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get priority display name
   * @param {string} priority - Priority value
   * @returns {string} Display name
   */
  static getPriorityDisplayName(priority) {
    const priorityMap = {
      'LOW': 'Low',
      'MEDIUM': 'Medium',
      'HIGH': 'High',
      'URGENT': 'Urgent'
    };
    return priorityMap[priority] || priority;
  }

  /**
   * Get status display name
   * @param {string} status - Status value
   * @returns {string} Display name
   */
  static getStatusDisplayName(status) {
    const statusMap = {
      'OPEN': 'Open',
      'IN_PROGRESS': 'In Progress',
      'RESOLVED': 'Resolved',
      'CLOSED': 'Closed'
    };
    return statusMap[status] || status;
  }
}

export { SupportService };
