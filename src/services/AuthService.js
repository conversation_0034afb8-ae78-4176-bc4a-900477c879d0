import { User } from '../models/user/index.js';
import { OTPService } from './OTPService.js';
import { CreditService } from './CreditService.js';
import { UserProfileService } from './UserProfileService.js';
import { JWTUtil } from '../config/jwt.js';
import { EncryptionUtil } from '../utils/encryption.js';
import { VALIDATION_RULES, ERROR_MESSAGES } from '../utils/constants.js';
import logger from '../config/logger.js';

export class AuthService {
  /**
   * Login with email/mobile and password
   * @param {Object} loginData - Login credentials
   * @param {string} loginData.identifier - Email or mobile
   * @param {string} loginData.password - Password
   * @returns {Promise<Object>} Token and user data
   */
  static async loginWithPassword(loginData) {
    try {
      const { identifier, password } = loginData;

      if (!identifier || !password) {
        throw new Error('Identifier and password are required');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Check if user is verified
      if (!user.isVerified) {
        throw new Error('User account is not verified. Please verify your account first.');
      }

      // Validate password
      const isValidPassword = await user.validatePassword(password);
      if (!isValidPassword) {
        throw new Error(ERROR_MESSAGES.INVALID_CREDENTIALS);
      }

      // Generate JWT token
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`User logged in successfully: ${user.id}`);

      return {
        token,
        user: user.toJSON(),
      };
    } catch (error) {
      logger.error('Error in password login:', error);
      throw error;
    }
  }

  /**
   * Login with OTP
   * @param {Object} otpData - OTP login data
   * @param {string} otpData.identifier - Email or mobile
   * @param {string} otpData.otp - OTP code
   * @returns {Promise<Object>} Token and user data
   */
  static async loginWithOTP(otpData) {
    try {
      const { identifier, otp } = otpData;

      if (!identifier || !otp) {
        throw new Error('Identifier and OTP are required');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Verify OTP
      const isValidOTP = await OTPService.verifyOTP(user.id, otp);
      if (!isValidOTP) {
        throw new Error('Invalid or expired OTP');
      }

      // Mark user as verified if not already
      if (!user.isVerified) {
        await user.update({ isVerified: true });
        
        // Initialize user credits and profile
        await Promise.all([
          CreditService.initializeUserCredits(user.id),
          UserProfileService.initializeUserProfile(user.id),
        ]);
      }

      // Generate JWT token
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`User logged in with OTP successfully: ${user.id}`);

      return {
        token,
        user: user.toJSON(),
      };
    } catch (error) {
      logger.error('Error in OTP login:', error);
      throw error;
    }
  }

  /**
   * Send OTP for login
   * @param {Object} otpRequest - OTP request data
   * @param {string} otpRequest.identifier - Email or mobile
   * @returns {Promise<Object>} OTP sent confirmation
   */
  static async sendLoginOTP(otpRequest) {
    try {
      const { identifier } = otpRequest;

      if (!identifier) {
        throw new Error('Identifier is required');
      }

      // Validate identifier format
      const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(identifier);
      const isMobile = VALIDATION_RULES.MOBILE_REGEX.test(identifier);

      if (!isEmail && !isMobile) {
        throw new Error('Invalid email or mobile format');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Check if user already has a pending OTP
      const hasPendingOTP = await OTPService.hasPendingOTP(user.id);
      if (hasPendingOTP) {
        const expiryTime = await OTPService.getOTPExpiry(user.id);
        throw new Error(`OTP already sent. Please wait until ${expiryTime?.toLocaleTimeString()} or try again later.`);
      }

      // Generate and send OTP
      const otp = await OTPService.generateOTP(user.id);
      await OTPService.sendOTP(identifier, otp, 'login');

      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

      logger.info(`Login OTP sent to user: ${user.id}`);

      return {
        message: 'OTP sent successfully',
        expiresAt,
      };
    } catch (error) {
      logger.error('Error sending login OTP:', error);
      throw error;
    }
  }

  /**
   * Register new user
   * @param {Object} userData - User registration data
   * @param {string} [userData.email] - Email address
   * @param {string} [userData.mobile] - Mobile number
   * @param {string} [userData.password] - Password
   * @returns {Promise<Object>} Registration result
   */
  static async registerUser(userData) {
    try {
      const { email, mobile, password } = userData;

      // Validate that at least email or mobile is provided
      if (!email && !mobile) {
        throw new Error('Either email or mobile is required');
      }

      // Validate email format if provided
      if (email && !VALIDATION_RULES.EMAIL_REGEX.test(email)) {
        throw new Error('Invalid email format');
      }

      // Validate mobile format if provided
      if (mobile && !VALIDATION_RULES.MOBILE_REGEX.test(mobile)) {
        throw new Error('Invalid mobile format');
      }

      // Validate password if provided
      if (password && !VALIDATION_RULES.PASSWORD_REGEX.test(password)) {
        throw new Error('Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character');
      }

      // Check if user already exists
      const existingUser = await User.findByEmailOrMobile(email || mobile);
      if (existingUser) {
        if (existingUser.isVerified) {
          throw new Error('User already exists and is verified');
        } else {
          // User exists but not verified, delete the old record
          await existingUser.destroy();
          logger.info(`Deleted unverified user record for re-registration: ${email || mobile}`);
        }
      }

      const identifier = email || mobile;

      // Create new unverified user
      const user = await User.createUser({ email, mobile, password, isVerified: false });

      // Generate and send OTP
      const otp = await OTPService.generateOTP(user.id);
      await OTPService.sendOTP(identifier, otp, 'signup');

      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

      logger.info(`New user registered (unverified): ${user.id}`);

      return {
        message: 'User registered successfully. Please verify your account with the OTP sent to your email/mobile.',
        userId: user.id,
        expiresAt,
      };
    } catch (error) {
      logger.error('Error in user registration:', error);
      throw error;
    }
  }

  /**
   * Verify user account with OTP
   * @param {Object} verificationData - Verification data
   * @param {string} verificationData.identifier - Email or mobile
   * @param {string} verificationData.otp - OTP code
   * @returns {Promise<Object>} Verification result
   */
  static async verifyAccount(verificationData) {
    try {
      const { identifier, otp } = verificationData;

      if (!identifier || !otp) {
        throw new Error('Identifier and OTP are required');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is already verified
      if (user.isVerified) {
        throw new Error('User account is already verified');
      }

      // Verify OTP
      const isValidOTP = await OTPService.verifyOTP(user.id, otp);
      if (!isValidOTP) {
        throw new Error('Invalid or expired OTP');
      }

      // Mark user as verified
      await user.update({ isVerified: true });

      // Initialize user credits and profile
      await Promise.all([
        CreditService.initializeUserCredits(user.id),
        UserProfileService.initializeUserProfile(user.id),
      ]);

      // Generate JWT token
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`User account verified successfully: ${user.id}`);

      return {
        message: 'Account verified successfully',
        token,
        user: user.toJSON(),
      };
    } catch (error) {
      logger.error('Error in account verification:', error);
      throw error;
    }
  }

  /**
   * Resend verification OTP
   * @param {Object} resendData - Resend data
   * @param {string} resendData.identifier - Email or mobile
   * @returns {Promise<Object>} Resend result
   */
  static async resendVerificationOTP(resendData) {
    try {
      const { identifier } = resendData;

      if (!identifier) {
        throw new Error('Identifier is required');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is already verified
      if (user.isVerified) {
        throw new Error('User account is already verified');
      }

      // Check if user already has a pending OTP
      const hasPendingOTP = await OTPService.hasPendingOTP(user.id);
      if (hasPendingOTP) {
        const expiryTime = await OTPService.getOTPExpiry(user.id);
        throw new Error(`OTP already sent. Please wait until ${expiryTime?.toLocaleTimeString()} or try again later.`);
      }

      // Generate and send new OTP
      const otp = await OTPService.generateOTP(user.id);
      await OTPService.sendOTP(identifier, otp);

      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

      logger.info(`Verification OTP resent to user: ${user.id}`);

      return {
        message: 'OTP resent successfully',
        expiresAt,
      };
    } catch (error) {
      logger.error('Error resending verification OTP:', error);
      throw error;
    }
  }

  /**
   * Verify JWT token
   * @param {string} token - JWT token
   * @returns {Promise<Object>} Token payload
   */
  static async verifyToken(token) {
    try {
      const payload = JWTUtil.verifyToken(token);

      // Verify user still exists, is active, and is verified
      const user = await User.findByPk(payload.userId);
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      if (!user.isVerified) {
        throw new Error('User account is not verified');
      }

      return payload;
    } catch (error) {
      logger.error('Error verifying token:', error);
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Change user password
   * @param {string} userId - User ID
   * @param {Object} passwordData - Password change data
   * @param {string} passwordData.currentPassword - Current password
   * @param {string} passwordData.newPassword - New password
   * @returns {Promise<Object>} Change result
   */
  static async changePassword(userId, passwordData) {
    try {
      const { currentPassword, newPassword } = passwordData;

      if (!currentPassword || !newPassword) {
        throw new Error('Current password and new password are required');
      }

      // Validate new password
      if (!VALIDATION_RULES.PASSWORD_REGEX.test(newPassword)) {
        throw new Error('New password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character');
      }

      // Find user
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Validate current password
      const isValidPassword = await user.validatePassword(currentPassword);
      if (!isValidPassword) {
        throw new Error('Current password is incorrect');
      }

      // Update password
      await user.updatePassword(newPassword);

      logger.info(`Password changed for user: ${userId}`);

      return {
        message: 'Password changed successfully',
      };
    } catch (error) {
      logger.error('Error changing password:', error);
      throw error;
    }
  }

  /**
   * Reset password with OTP
   * @param {Object} resetData - Password reset data
   * @param {string} resetData.identifier - Email or mobile
   * @param {string} resetData.otp - OTP code
   * @param {string} resetData.newPassword - New password
   * @returns {Promise<Object>} Reset result
   */
  static async resetPassword(resetData) {
    try {
      const { identifier, otp, newPassword } = resetData;

      if (!identifier || !otp || !newPassword) {
        throw new Error('Identifier, OTP, and new password are required');
      }

      // Validate new password
      if (!VALIDATION_RULES.PASSWORD_REGEX.test(newPassword)) {
        throw new Error('New password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Verify OTP
      const isValidOTP = await OTPService.verifyOTP(user.id, otp);
      if (!isValidOTP) {
        throw new Error('Invalid or expired OTP');
      }

      // Update password
      await user.updatePassword(newPassword);

      logger.info(`Password reset for user: ${user.id}`);

      return {
        message: 'Password reset successfully',
      };
    } catch (error) {
      logger.error('Error resetting password:', error);
      throw error;
    }
  }

  /**
   * Send password reset OTP
   * @param {Object} resetRequest - Reset request data
   * @param {string} resetRequest.identifier - Email or mobile
   * @returns {Promise<Object>} Reset OTP sent confirmation
   */
  static async sendPasswordResetOTP(resetRequest) {
    try {
      const { identifier } = resetRequest;

      if (!identifier) {
        throw new Error('Identifier is required');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is active and verified
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      if (!user.isVerified) {
        throw new Error('User account is not verified');
      }

      // Check if user already has a pending OTP
      const hasPendingOTP = await OTPService.hasPendingOTP(user.id);
      if (hasPendingOTP) {
        const expiryTime = await OTPService.getOTPExpiry(user.id);
        throw new Error(`OTP already sent. Please wait until ${expiryTime?.toLocaleTimeString()} or try again later.`);
      }

      // Generate and send OTP
      const otp = await OTPService.generateOTP(user.id);
      await OTPService.sendOTP(identifier, otp);

      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

      logger.info(`Password reset OTP sent to user: ${user.id}`);

      return {
        message: 'Password reset OTP sent successfully',
        expiresAt,
      };
    } catch (error) {
      logger.error('Error sending password reset OTP:', error);
      throw error;
    }
  }

  /**
   * Refresh JWT token
   * @param {string} token - Current JWT token
   * @returns {Promise<Object>} New token
   */
  static async refreshToken(token) {
    try {
      const payload = await this.verifyToken(token);

      // Generate new token
      const newTokenPayload = {
        userId: payload.userId,
        email: payload.email,
        mobile: payload.mobile,
      };

      const newToken = JWTUtil.generateToken(newTokenPayload);

      logger.info(`Token refreshed for user: ${payload.userId}`);

      return {
        token: newToken,
      };
    } catch (error) {
      logger.error('Error refreshing token:', error);
      throw error;
    }
  }

  /**
   * Request OTP for login
   * @param {string} identifier - Email or mobile
   * @returns {Promise<Object>} OTP sent confirmation
   */
  static async requestOTP(identifier) {
    try {
      if (!identifier) {
        throw new Error('Identifier is required');
      }

      // Validate identifier format
      const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(identifier);
      const isMobile = VALIDATION_RULES.MOBILE_REGEX.test(identifier);

      if (!isEmail && !isMobile) {
        throw new Error('Invalid email or mobile format');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Check if user already has a pending OTP
      const hasPendingOTP = await OTPService.hasPendingOTP(user.id);
      if (hasPendingOTP) {
        const expiryTime = await OTPService.getOTPExpiry(user.id);
        throw new Error(`OTP already sent. Please wait until ${expiryTime?.toLocaleTimeString()} or try again later.`);
      }

      // Generate and send OTP
      const otp = await OTPService.generateOTP(user.id);
      await OTPService.sendOTP(identifier, otp, 'login');

      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

      logger.info(`Login OTP sent to user: ${user.id}`);

      return {
        message: 'OTP sent successfully',
        expiresAt,
      };
    } catch (error) {
      logger.error('Error sending login OTP:', error);
      throw error;
    }
  }

  /**
   * Verify OTP and login
   * @param {Object} otpData - OTP verification data
   * @param {string} otpData.identifier - Email or mobile
   * @param {string} otpData.otp - OTP code
   * @returns {Promise<Object>} Token and user data
   */
  static async verifyOTPAndLogin(otpData) {
    try {
      const { identifier, otp } = otpData;

      if (!identifier || !otp) {
        throw new Error('Identifier and OTP are required');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Verify OTP
      const isValidOTP = await OTPService.verifyOTP(user.id, otp);
      if (!isValidOTP) {
        throw new Error('Invalid or expired OTP');
      }

      // Mark user as verified if not already
      if (!user.isVerified) {
        await user.update({ isVerified: true });

        // Initialize user credits and profile
        await Promise.all([
          CreditService.initializeUserCredits(user.id),
          UserProfileService.initializeUserProfile(user.id),
        ]);
      }

      // Generate JWT token
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`User logged in with OTP successfully: ${user.id}`);

      return {
        token,
        user: user.toJSON(),
      };
    } catch (error) {
      logger.error('Error in OTP login:', error);
      throw error;
    }
  }

  /**
   * Register new user with password (creates unverified user and sends OTP)
   * @param {Object} userData - User registration data
   * @param {string} [userData.email] - Email address
   * @param {string} [userData.mobile] - Mobile number
   * @param {string} userData.password - Password
   * @returns {Promise<Object>} Registration result
   */
  static async register(userData) {
    try {
      const { email, mobile, password } = userData;

      // Validate that at least email or mobile is provided
      if (!email && !mobile) {
        throw new Error('Either email or mobile is required');
      }

      // Validate email format if provided
      if (email && !VALIDATION_RULES.EMAIL_REGEX.test(email)) {
        throw new Error('Invalid email format');
      }

      // Validate mobile format if provided
      if (mobile && !VALIDATION_RULES.MOBILE_REGEX.test(mobile)) {
        throw new Error('Invalid mobile format');
      }

      // Validate password
      if (!password || password.length < VALIDATION_RULES.PASSWORD_MIN_LENGTH) {
        throw new Error(`Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters long`);
      }

      // Check if user already exists
      const existingUser = await User.findByEmailOrMobile(email || mobile);
      if (existingUser) {
        if (existingUser.isVerified) {
          throw new Error('User already exists and is verified');
        } else {
          // User exists but not verified, delete the old record
          await existingUser.destroy();
          logger.info(`Deleted unverified user record for re-registration: ${email || mobile}`);
        }
      }

      const identifier = email || mobile;

      // Create new unverified user
      const user = await User.createUser({ email, mobile, password, isVerified: false });

      // Generate and send OTP
      const otp = await OTPService.generateOTP(user.id);
      await OTPService.sendOTP(identifier, otp, 'signup');

      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

      logger.info(`New user registered (unverified): ${user.id}`);

      return {
        message: 'User registered successfully. Please verify your account with the OTP sent to your email/mobile.',
        userId: user.id,
        expiresAt,
      };
    } catch (error) {
      logger.error('Error in user registration:', error);
      throw error;
    }
  }

  /**
   * Verify registration OTP and complete user registration
   * @param {Object} otpData - OTP verification data
   * @param {string} otpData.identifier - Email or mobile
   * @param {string} otpData.otp - OTP code
   * @returns {Promise<Object>} Verification result with token and user data
   */
  static async verifyRegistration(otpData) {
    try {
      const { identifier, otp } = otpData;

      if (!identifier || !otp) {
        throw new Error('Identifier and OTP are required');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is already verified
      if (user.isVerified) {
        throw new Error('User account is already verified');
      }

      // Verify OTP
      const isValidOTP = await OTPService.verifyOTP(user.id, otp);
      if (!isValidOTP) {
        throw new Error('Invalid or expired OTP');
      }

      // Mark user as verified
      await user.update({ isVerified: true });

      // Initialize user credits and profile
      await Promise.all([
        CreditService.initializeUserCredits(user.id),
        UserProfileService.initializeUserProfile(user.id),
      ]);

      // Generate JWT token
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`User account verified successfully: ${user.id}`);

      return {
        message: 'Account verified successfully',
        token,
        user: user.toJSON(),
      };
    } catch (error) {
      logger.error('Error in account verification:', error);
      throw error;
    }
  }

  /**
   * Logout user (placeholder for token blacklisting)
   * @param {string} token - JWT token to invalidate
   * @returns {Promise<Object>} Logout result
   */
  static async logout(token) {
    try {
      // In a production system, you would add the token to a blacklist
      // For now, we'll just log the logout
      const payload = JWTUtil.verifyToken(token);

      logger.info(`User logged out: ${payload.userId}`);

      return {
        message: 'Logged out successfully',
      };
    } catch (error) {
      logger.error('Error during logout:', error);
      throw error;
    }
  }
}

