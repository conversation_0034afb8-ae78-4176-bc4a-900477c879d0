import { google } from 'googleapis';
import { UserGoogleAuth } from '../models/user/index.js';
import logger from '../config/logger.js';

/**
 * Google OAuth Service
 * Handles Google OAuth 2.0 authentication flow and API client management
 */
export class GoogleAuthService {
  // Google Workspace API scopes
  static SCOPES = {
    DRIVE: 'https://www.googleapis.com/auth/drive',
    DRIVE_FILE: 'https://www.googleapis.com/auth/drive.file',
    DRIVE_READONLY: 'https://www.googleapis.com/auth/drive.readonly',
    SHEETS: 'https://www.googleapis.com/auth/spreadsheets',
    SHEETS_READONLY: 'https://www.googleapis.com/auth/spreadsheets.readonly',
    DOCS: 'https://www.googleapis.com/auth/documents',
    DOCS_READONLY: 'https://www.googleapis.com/auth/documents.readonly',
    CALENDAR: 'https://www.googleapis.com/auth/calendar',
    CALENDAR_READONLY: 'https://www.googleapis.com/auth/calendar.readonly',
    SLIDES: 'https://www.googleapis.com/auth/presentations',
    SLIDES_READONLY: 'https://www.googleapis.com/auth/presentations.readonly',
    USERINFO_EMAIL: 'https://www.googleapis.com/auth/userinfo.email',
    USERINFO_PROFILE: 'https://www.googleapis.com/auth/userinfo.profile'
  };

  // Default scopes for full Google Workspace access
  static DEFAULT_SCOPES = [
    GoogleAuthService.SCOPES.DRIVE,
    GoogleAuthService.SCOPES.SHEETS,
    GoogleAuthService.SCOPES.DOCS,
    GoogleAuthService.SCOPES.CALENDAR,
    GoogleAuthService.SCOPES.SLIDES,
    GoogleAuthService.SCOPES.USERINFO_EMAIL,
    GoogleAuthService.SCOPES.USERINFO_PROFILE
  ];

  /**
   * Create OAuth2 client
   * @returns {google.auth.OAuth2} OAuth2 client instance
   */
  static createOAuth2Client() {
    return new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );
  }

  /**
   * Generate authorization URL
   * @param {string} userId - User ID for state parameter
   * @param {string[]} [scopes] - OAuth scopes to request
   * @returns {string} Authorization URL
   */
  static generateAuthUrl(userId, scopes = GoogleAuthService.DEFAULT_SCOPES) {
    const oauth2Client = GoogleAuthService.createOAuth2Client();
    
    return oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      state: userId, // Pass userId in state for security
      prompt: 'consent' // Force consent screen to get refresh token
    });
  }

  /**
   * Exchange authorization code for tokens
   * @param {string} code - Authorization code from Google
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Token and user info
   */
  static async exchangeCodeForTokens(code, userId) {
    try {
      const oauth2Client = GoogleAuthService.createOAuth2Client();
      
      // Exchange code for tokens
      const { tokens } = await oauth2Client.getToken(code);
      oauth2Client.setCredentials(tokens);

      // Get user info
      const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
      const { data: userInfo } = await oauth2.userinfo.get();

      // Calculate expiry date
      const accessTokenExpiresAt = new Date(Date.now() + (tokens.expiry_date - Date.now()));

      // Store in database
      const authRecord = await UserGoogleAuth.createOrUpdate({
        userId,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        accessTokenExpiresAt,
        grantedScopes: tokens.scope,
        userInfo
      });

      logger.info(`Google OAuth tokens stored for user: ${userId}`);

      return {
        success: true,
        userInfo,
        grantedScopes: tokens.scope,
        authRecord: authRecord.toJSON()
      };
    } catch (error) {
      logger.error('Error exchanging code for tokens:', error);
      throw new Error('Failed to authenticate with Google');
    }
  }

  /**
   * Refresh access token
   * @param {string} userId - User ID
   * @returns {Promise<Object>} New tokens
   */
  static async refreshAccessToken(userId) {
    try {
      const authRecord = await UserGoogleAuth.findByUserId(userId);
      if (!authRecord || !authRecord.refreshToken) {
        throw new Error('No refresh token available');
      }

      const oauth2Client = GoogleAuthService.createOAuth2Client();
      oauth2Client.setCredentials({
        refresh_token: authRecord.refreshToken
      });

      // Refresh the token
      const { credentials } = await oauth2Client.refreshAccessToken();
      
      // Calculate new expiry date
      const accessTokenExpiresAt = new Date(Date.now() + (credentials.expiry_date - Date.now()));

      // Update database
      await authRecord.update({
        accessToken: credentials.access_token,
        accessTokenExpiresAt,
        lastAuthenticatedAt: new Date()
      });

      logger.info(`Access token refreshed for user: ${userId}`);

      return {
        accessToken: credentials.access_token,
        expiresAt: accessTokenExpiresAt
      };
    } catch (error) {
      logger.error('Error refreshing access token:', error);
      throw new Error('Failed to refresh access token');
    }
  }

  /**
   * Get authenticated OAuth2 client for user
   * @param {string} userId - User ID
   * @returns {Promise<google.auth.OAuth2>} Authenticated OAuth2 client
   */
  static async getAuthenticatedClient(userId) {
    const authRecord = await UserGoogleAuth.findByUserId(userId);
    if (!authRecord || !authRecord.isActive) {
      throw new Error('User not authenticated with Google');
    }

    const oauth2Client = GoogleAuthService.createOAuth2Client();

    // Check if access token is expired and refresh if needed
    if (authRecord.isAccessTokenExpired()) {
      if (!authRecord.refreshToken) {
        throw new Error('Access token expired and no refresh token available');
      }
      
      await GoogleAuthService.refreshAccessToken(userId);
      // Reload the record to get updated tokens
      await authRecord.reload();
    }

    oauth2Client.setCredentials({
      access_token: authRecord.accessToken,
      refresh_token: authRecord.refreshToken
    });

    return oauth2Client;
  }

  /**
   * Get Google API client for specific service
   * @param {string} userId - User ID
   * @param {string} service - Service name (drive, sheets, docs, calendar, slides)
   * @returns {Promise<Object>} Google API client
   */
  static async getApiClient(userId, service) {
    const auth = await GoogleAuthService.getAuthenticatedClient(userId);

    switch (service.toLowerCase()) {
      case 'drive':
        return google.drive({ version: 'v3', auth });
      case 'sheets':
        return google.sheets({ version: 'v4', auth });
      case 'docs':
        return google.docs({ version: 'v1', auth });
      case 'calendar':
        return google.calendar({ version: 'v3', auth });
      case 'slides':
        return google.slides({ version: 'v1', auth });
      default:
        throw new Error(`Unsupported service: ${service}`);
    }
  }

  /**
   * Check user's authentication status
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Authentication status
   */
  static async getAuthStatus(userId) {
    try {
      const authRecord = await UserGoogleAuth.findByUserId(userId);
      
      if (!authRecord || !authRecord.isActive) {
        return {
          isAuthenticated: false,
          message: 'Not authenticated with Google'
        };
      }

      const isExpired = authRecord.isAccessTokenExpired();
      const hasRefreshToken = !!authRecord.refreshToken;

      return {
        isAuthenticated: true,
        isAccessTokenExpired: isExpired,
        hasRefreshToken,
        grantedScopes: authRecord.grantedScopes ? authRecord.grantedScopes.split(' ') : [],
        lastAuthenticatedAt: authRecord.lastAuthenticatedAt,
        userInfo: authRecord.googleUserInfo
      };
    } catch (error) {
      logger.error('Error checking auth status:', error);
      return {
        isAuthenticated: false,
        message: 'Error checking authentication status'
      };
    }
  }

  /**
   * Revoke Google authentication
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Success status
   */
  static async revokeAuth(userId) {
    try {
      const authRecord = await UserGoogleAuth.findByUserId(userId);
      if (!authRecord) {
        return true; // Already not authenticated
      }

      // Revoke token with Google
      if (authRecord.accessToken) {
        const oauth2Client = GoogleAuthService.createOAuth2Client();
        oauth2Client.setCredentials({
          access_token: authRecord.accessToken
        });
        
        try {
          await oauth2Client.revokeCredentials();
        } catch (error) {
          logger.warn('Failed to revoke token with Google:', error);
          // Continue with local revocation even if Google revocation fails
        }
      }

      // Revoke in database
      await UserGoogleAuth.revokeAuth(userId);
      
      logger.info(`Google authentication revoked for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error revoking Google auth:', error);
      throw new Error('Failed to revoke Google authentication');
    }
  }

  /**
   * Check if user has required scopes
   * @param {string} userId - User ID
   * @param {string[]} requiredScopes - Required scopes
   * @returns {Promise<boolean>} True if user has all required scopes
   */
  static async hasRequiredScopes(userId, requiredScopes) {
    const authRecord = await UserGoogleAuth.findByUserId(userId);
    if (!authRecord || !authRecord.isActive) {
      return false;
    }

    return authRecord.hasAllScopes(requiredScopes);
  }
}
