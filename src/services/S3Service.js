import { S3<PERSON>lient, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import logger from '../config/logger.js';
import { EncryptionUtil } from '../utils/encryption.js';

/**
 * S3Service for handling file uploads and management in AWS S3
 */
export class S3Service {
  static client = null;
  static bucketName = null;
  static folderPrefix = null;
  static region = null;

  /**
   * Initialize S3 client
   * @returns {Promise<void>}
   */
  static async initialize() {
    try {
      // Check if AWS credentials are configured
      if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
        logger.warn('AWS credentials not found. S3 service will be disabled.');
        return;
      }

      this.region = process.env.AWS_REGION || 'us-east-1';
      this.bucketName = process.env.AWS_S3_BUCKET;
      this.folderPrefix = process.env.AWS_S3_FOLDER_PREFIX || 'chat-attachments';

      if (!this.bucketName) {
        logger.warn('AWS S3 bucket name not configured. S3 service will be disabled.');
        return;
      }

      // Initialize S3 client
      this.client = new S3Client({
        region: this.region,
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        },
      });

      logger.info(`S3 service initialized successfully. Bucket: ${this.bucketName}, Region: ${this.region}`);
    } catch (error) {
      logger.error('Failed to initialize S3 service:', error);
      throw error;
    }
  }

  /**
   * Check if S3 service is available
   * @returns {boolean}
   */
  static isAvailable() {
    return this.client !== null && this.bucketName !== null;
  }

  /**
   * Generate a unique S3 key for a file
   * @param {string} originalName - Original filename
   * @param {string} [subfolder] - Optional subfolder
   * @returns {string} S3 key
   */
  static generateS3Key(originalName, subfolder = '') {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const fileExtension = originalName.split('.').pop();
    const baseName = originalName.split('.').slice(0, -1).join('.');
    const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9-_]/g, '_');

    const fileName = `${timestamp}_${randomSuffix}_${sanitizedBaseName}.${fileExtension}`;

    const keyParts = [this.folderPrefix];
    if (subfolder) {
      keyParts.push(subfolder);
    }
    keyParts.push(fileName);

    return keyParts.join('/');
  }

  /**
   * Generate a secure file identifier for public use
   * @param {string} s3Key - S3 object key
   * @param {string} originalName - Original filename
   * @returns {string} Secure file identifier
   */
  static generateSecureFileId(s3Key, originalName) {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const fileExtension = originalName.split('.').pop();
    const baseName = originalName.split('.').slice(0, -1).join('.');
    const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9-_]/g, '_');

    // Create a secure identifier that doesn't expose S3 structure
    return `${timestamp}_${randomSuffix}_${sanitizedBaseName}.${fileExtension}`;
  }

  /**
   * Upload file to S3
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} originalName - Original filename
   * @param {string} mimeType - File MIME type
   * @param {string} [subfolder] - Optional subfolder
   * @returns {Promise<Object>} Upload result with S3 URL and key
   */
  static async uploadFile(fileBuffer, originalName, mimeType, subfolder = '') {
    if (!this.isAvailable()) {
      throw new Error('S3 service is not available');
    }

    try {
      const s3Key = this.generateS3Key(originalName, subfolder);
      
      const uploadParams = {
        Bucket: this.bucketName,
        Key: s3Key,
        Body: fileBuffer,
        ContentType: mimeType,
        ContentDisposition: `attachment; filename="${originalName}"`,
        Metadata: {
          originalName: originalName,
          uploadedAt: new Date().toISOString(),
        },
      };

      const command = new PutObjectCommand(uploadParams);
      await this.client.send(command);

      const s3Url = `https://${this.bucketName}.s3.${this.region}.amazonaws.com/${s3Key}`;

      logger.info(`File uploaded to S3 successfully: ${originalName} -> ${s3Key}`);

      // Generate secure file identifier
      const secureFileId = this.generateSecureFileId(s3Key, originalName);

      return {
        s3Url,
        s3Key,
        secureFileId,
        bucket: this.bucketName,
        region: this.region,
      };
    } catch (error) {
      logger.error(`Failed to upload file to S3: ${originalName}`, error);
      throw new Error(`S3 upload failed: ${error.message}`);
    }
  }

  /**
   * Download file from S3
   * @param {string} s3Key - S3 object key
   * @returns {Promise<Buffer>} File buffer
   */
  static async downloadFile(s3Key) {
    if (!this.isAvailable()) {
      throw new Error('S3 service is not available');
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
      });

      const response = await this.client.send(command);
      const chunks = [];
      
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      
      return Buffer.concat(chunks);
    } catch (error) {
      logger.error(`Failed to download file from S3: ${s3Key}`, error);
      throw new Error(`S3 download failed: ${error.message}`);
    }
  }

  /**
   * Generate presigned URL for file access
   * @param {string} s3Key - S3 object key
   * @param {number} [expiresIn] - URL expiration time in seconds (default: 1 hour)
   * @returns {Promise<string>} Presigned URL
   */
  static async generatePresignedUrl(s3Key, expiresIn = 3600) {
    if (!this.isAvailable()) {
      throw new Error('S3 service is not available');
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
      });

      const presignedUrl = await getSignedUrl(this.client, command, { expiresIn });
      return presignedUrl;
    } catch (error) {
      logger.error(`Failed to generate presigned URL for S3 key: ${s3Key}`, error);
      throw new Error(`Presigned URL generation failed: ${error.message}`);
    }
  }

  /**
   * Store secure file mapping (in-memory cache for now)
   * In production, this should be stored in Redis or database
   */
  static secureFileMap = new Map();

  /**
   * Store secure file identifier mapping to S3 key
   * @param {string} secureFileId - Secure file identifier
   * @param {string} s3Key - S3 object key
   * @param {string} originalName - Original filename
   * @param {string} mimeType - File MIME type
   */
  static storeSecureFileMapping(secureFileId, s3Key, originalName, mimeType) {
    this.secureFileMap.set(secureFileId, {
      s3Key,
      originalName,
      mimeType,
      createdAt: new Date()
    });
  }

  /**
   * Get S3 key from secure file identifier
   * @param {string} secureFileId - Secure file identifier
   * @returns {Object|null} File mapping or null if not found
   */
  static getSecureFileMapping(secureFileId) {
    return this.secureFileMap.get(secureFileId) || null;
  }

  /**
   * Generate presigned URL using secure file identifier
   * @param {string} secureFileId - Secure file identifier
   * @param {number} [expiresIn] - URL expiration time in seconds (default: 1 hour)
   * @returns {Promise<Object>} Presigned URL with file metadata
   */
  static async generatePresignedUrlBySecureId(secureFileId, expiresIn = 3600) {
    const fileMapping = this.getSecureFileMapping(secureFileId);

    if (!fileMapping) {
      throw new Error('File not found or access denied');
    }

    const presignedUrl = await this.generatePresignedUrl(fileMapping.s3Key, expiresIn);

    return {
      url: presignedUrl,
      originalName: fileMapping.originalName,
      mimeType: fileMapping.mimeType,
      expiresIn
    };
  }

  /**
   * Delete file from S3
   * @param {string} s3Key - S3 object key
   * @returns {Promise<void>}
   */
  static async deleteFile(s3Key) {
    if (!this.isAvailable()) {
      throw new Error('S3 service is not available');
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
      });

      await this.client.send(command);
      logger.info(`File deleted from S3: ${s3Key}`);
    } catch (error) {
      logger.error(`Failed to delete file from S3: ${s3Key}`, error);
      throw new Error(`S3 delete failed: ${error.message}`);
    }
  }

  /**
   * Check if file exists in S3
   * @param {string} s3Key - S3 object key
   * @returns {Promise<boolean>} Whether file exists
   */
  static async fileExists(s3Key) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
      });

      await this.client.send(command);
      return true;
    } catch (error) {
      if (error.name === 'NotFound') {
        return false;
      }
      logger.error(`Error checking file existence in S3: ${s3Key}`, error);
      return false;
    }
  }

  /**
   * Extract S3 key from S3 URL
   * @param {string} s3Url - S3 URL
   * @returns {string|null} S3 key or null if invalid URL
   */
  static extractS3KeyFromUrl(s3Url) {
    try {
      if (!s3Url || typeof s3Url !== 'string') {
        return null;
      }

      // Handle different S3 URL formats
      const url = new URL(s3Url);
      
      // Format: https://bucket.s3.region.amazonaws.com/key
      if (url.hostname.includes('.s3.') && url.hostname.includes('.amazonaws.com')) {
        return url.pathname.substring(1); // Remove leading slash
      }
      
      // Format: https://s3.region.amazonaws.com/bucket/key
      if (url.hostname.startsWith('s3.') && url.hostname.includes('.amazonaws.com')) {
        const pathParts = url.pathname.split('/');
        if (pathParts.length >= 3) {
          return pathParts.slice(2).join('/'); // Remove empty string and bucket name
        }
      }
      
      return null;
    } catch (error) {
      logger.warn(`Invalid S3 URL format: ${s3Url}`);
      return null;
    }
  }

  /**
   * Clean up old files in S3 (files older than specified hours)
   * @param {number} [maxAgeHours] - Maximum age in hours (default: 24)
   * @returns {Promise<number>} Number of files deleted
   */
  static async cleanupOldFiles(maxAgeHours = 24) {
    if (!this.isAvailable()) {
      logger.warn('S3 service not available for cleanup');
      return 0;
    }

    try {
      // Note: This is a simplified cleanup. In production, you might want to use S3 lifecycle policies
      // or implement a more sophisticated cleanup mechanism with S3 list operations
      logger.info(`S3 cleanup initiated for files older than ${maxAgeHours} hours`);
      
      // For now, we'll log that cleanup should be handled by S3 lifecycle policies
      logger.info('Consider setting up S3 lifecycle policies for automatic cleanup of old files');
      
      return 0;
    } catch (error) {
      logger.error('Error during S3 cleanup:', error);
      return 0;
    }
  }
}
