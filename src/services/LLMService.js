import { Chat<PERSON><PERSON>A<PERSON> } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { HumanMessage, SystemMessage, AIMessage } from '@langchain/core/messages';
import { LLM_MODELS, DEFAULT_LLM_CONFIG } from '../utils/constants.js';
import { LLMModel } from '../models/chat/LLMModel.js';
import logger from '../config/logger.js';

export class LLMService {
  static models = new Map();

  /**
   * Initialize LLM models
   */
  static initialize() {
    try {
      // Initialize OpenAI models
      if (process.env.OPENAI_API_KEY) {
        this.initializeOpenAIModels();
      }

      // Initialize Anthropic models
      if (process.env.ANTHROPIC_API_KEY) {
        this.initializeAnthropicModels();
      }

      // Initialize Google models
      if (process.env.GOOGLE_API_KEY) {
        this.initializeGoogleModels();
      }

      // Initialize DeepSeek models
      if (process.env.DEEPSEEK_API_KEY) {
        this.initializeDeepSeekModels();
      }

      // Initialize Meta models (via third-party providers)
      if (process.env.META_API_KEY || process.env.TOGETHER_API_KEY) {
        this.initializeMetaModels();
      }

      logger.info('LLM Service initialized successfully');
    } catch (error) {
      logger.error('Error initializing LLM Service:', error);
      throw error;
    }
  }

  /**
   * Initialize OpenAI models
   */
  static initializeOpenAIModels() {
    const openAIModels = Object.values(LLM_MODELS.OPENAI);

    openAIModels.forEach(modelName => {
      const model = new ChatOpenAI({
        modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens,
        openAIApiKey: process.env.OPENAI_API_KEY,
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized OpenAI model: ${modelName}`);
    });
  }

  /**
   * Initialize Anthropic models
   */
  static initializeAnthropicModels() {
    const anthropicModels = Object.values(LLM_MODELS.ANTHROPIC);

    anthropicModels.forEach(modelName => {
      const model = new ChatAnthropic({
        modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens,
        anthropicApiKey: process.env.ANTHROPIC_API_KEY,
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized Anthropic model: ${modelName}`);
    });
  }

  /**
   * Initialize Google models
   */
  static initializeGoogleModels() {
    const googleModels = [
      'gemini-2.5-pro',
      'gemini-2.5-flash',
      'gemini-2.0-flash',
      'gemini-1.5-pro',
      'gemini-1.5-flash',
    ];

    googleModels.forEach(modelName => {
      const model = new ChatGoogleGenerativeAI({
        model: modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxOutputTokens: DEFAULT_LLM_CONFIG.maxTokens,
        apiKey: process.env.GOOGLE_API_KEY,
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized Google model: ${modelName}`);
    });
  }

  /**
   * Initialize DeepSeek models
   */
  static initializeDeepSeekModels() {
    const deepSeekModels = [
      'deepseek-reasoner',
      'deepseek-chat',
    ];

    deepSeekModels.forEach(modelName => {
      // DeepSeek uses OpenAI-compatible API
      const model = new ChatOpenAI({
        modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens,
        openAIApiKey: process.env.DEEPSEEK_API_KEY,
        configuration: {
          baseURL: 'https://api.deepseek.com/v1',
        },
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized DeepSeek model: ${modelName}`);
    });
  }

  /**
   * Initialize Meta models (via third-party providers)
   */
  static initializeMetaModels() {
    const metaModels = [
      'llama-3.3-70b-instruct',
      'llama-3.2-90b-vision-instruct',
      'llama-3.2-11b-vision-instruct',
      'llama-3.1-405b-instruct',
      'llama-3.1-70b-instruct',
      'llama-3.1-8b-instruct',
    ];

    metaModels.forEach(modelName => {
      // Meta models via Together AI or similar providers
      const model = new ChatOpenAI({
        modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens,
        openAIApiKey: process.env.TOGETHER_API_KEY || process.env.META_API_KEY,
        configuration: {
          baseURL: process.env.META_API_BASE_URL || 'https://api.together.xyz/v1',
        },
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized Meta model: ${modelName}`);
    });
  }

  /**
   * Get LLM model by name
   */
  static getModel(modelName) {
    const defaultModel = process.env.DEFAULT_LLM_MODEL || LLM_MODELS.OPENAI.GPT_3_5_TURBO;
    const targetModel = modelName || defaultModel;

    const model = this.models.get(targetModel);
    if (!model) {
      throw new Error(`LLM model '${targetModel}' not found or not initialized`);
    }

    return model;
  }

  /**
   * Get LLM model with database API key
   * @param {string} modelName - Model name
   * @returns {Promise<Object>} LLM model instance
   */
  static async getModelWithDatabaseApiKey(modelName) {
    try {
      const defaultModel = process.env.DEFAULT_LLM_MODEL || LLM_MODELS.OPENAI.GPT_3_5_TURBO;
      const targetModel = modelName || defaultModel;

      logger.debug(`Getting model with database API key: ${targetModel}`);

      // Get model configuration from database
      const modelConfig = await LLMModel.findOne({
        where: { modelId: targetModel }
      });

      if (!modelConfig) {
        throw new Error(`Model '${targetModel}' not found in database`);
      }

      logger.debug(`Found model config for ${targetModel}, provider: ${modelConfig.provider}`);

      // Use API key from database or fallback to environment
      const apiKey = modelConfig.apiKey || this.getEnvironmentApiKey(modelConfig.provider);

      if (!apiKey) {
        throw new Error(`No API key found for model '${targetModel}' (provider: ${modelConfig.provider}). Please configure API key in database or environment variables.`);
      }

      logger.debug(`Creating model instance for ${targetModel} with provider ${modelConfig.provider}`);

      // Create model instance with database API key
      return this.createModelInstance({
        model: targetModel,
        provider: modelConfig.provider,
        apiKey: apiKey,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens
      });
    } catch (error) {
      logger.error(`Error getting model with database API key: ${modelName}`, error);
      throw error;
    }
  }

  /**
   * Get all available models from database
   * @returns {Promise<Array>} Array of available models
   */
  static async getAvailableModels() {
    try {
      const models = await LLMModel.getActiveModels();
      return models.map(model => model.getSummary());
    } catch (error) {
      logger.error('Error fetching available models:', error);
      throw error;
    }
  }

  /**
   * Get models by provider
   * @param {string} provider - Provider name
   * @returns {Promise<Array>} Array of models for the provider
   */
  static async getModelsByProvider(provider) {
    try {
      const models = await LLMModel.getModelsByProvider(provider);
      return models.map(model => model.getSummary());
    } catch (error) {
      logger.error(`Error fetching models for provider ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Get models with specific capabilities
   * @param {Object} capabilities - Required capabilities
   * @returns {Promise<Array>} Array of matching models
   */
  static async getModelsByCapabilities(capabilities) {
    try {
      const models = await LLMModel.getModelsByCapabilities(capabilities);
      return models.map(model => model.getSummary());
    } catch (error) {
      logger.error('Error fetching models by capabilities:', error);
      throw error;
    }
  }

  /**
   * Get model information by ID
   * @param {string} modelId - Model ID
   * @returns {Promise<Object|null>} Model information or null
   */
  static async getModelInfo(modelId) {
    try {
      const model = await LLMModel.getModelById(modelId);
      return model ? model.getSummary() : null;
    } catch (error) {
      logger.error(`Error fetching model info for ${modelId}:`, error);
      throw error;
    }
  }

  /**
   * Get environment API key for provider (fallback)
   * @param {string} provider - Provider name
   * @returns {string|null} API key from environment
   */
  static getEnvironmentApiKey(provider) {
    switch (provider) {
      case 'OPENAI':
        return process.env.OPENAI_API_KEY;
      case 'ANTHROPIC':
        return process.env.ANTHROPIC_API_KEY;
      case 'GOOGLE':
        return process.env.GOOGLE_API_KEY;
      case 'DEEPSEEK':
        return process.env.DEEPSEEK_API_KEY;
      case 'META':
        return process.env.META_API_KEY || process.env.TOGETHER_API_KEY;
      default:
        return null;
    }
  }

  /**
   * Create model instance based on provider and configuration
   * @param {Object} config - Model configuration
   * @param {string} config.model - Model name
   * @param {string} config.provider - Provider name
   * @param {string} [config.apiKey] - API key
   * @param {number} [config.temperature] - Temperature setting
   * @param {number} [config.maxTokens] - Max tokens setting
   * @returns {Object} Model instance
   */
  static createModelInstance(config) {
    const provider = config.provider || this.detectProvider(config.model);

    // Validate API key for the provider
    const apiKey = config.apiKey || this.getEnvironmentApiKey(provider);
    if (!apiKey) {
      throw new Error(`No API key found for provider '${provider}' and model '${config.model}'`);
    }

    switch (provider) {
      case 'OPENAI':
        return new ChatOpenAI({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          openAIApiKey: apiKey,
        });

      case 'ANTHROPIC':
        return new ChatAnthropic({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          anthropicApiKey: apiKey,
        });

      case 'GOOGLE':
        return new ChatGoogleGenerativeAI({
          model: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxOutputTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          apiKey: apiKey,
        });

      case 'DEEPSEEK':
        return new ChatOpenAI({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          openAIApiKey: apiKey,
          configuration: {
            baseURL: 'https://api.deepseek.com/v1',
          },
        });

      case 'META':
        return new ChatOpenAI({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          openAIApiKey: apiKey,
          configuration: {
            baseURL: process.env.META_API_BASE_URL || 'https://api.together.xyz/v1',
          },
        });

      default:
        throw new Error(`Unsupported LLM provider '${provider}' for model: ${config.model}`);
    }
  }

  /**
   * Create a custom model with specific configuration
   * @param {Object} config - LLM configuration object
   * @param {string} config.model - Model name
   * @param {number} [config.temperature] - Temperature setting
   * @param {number} [config.maxTokens] - Max tokens setting
   * @param {string} [config.apiKey] - API key
   * @returns {Object} LLM model instance
   */
  static createCustomModel(config) {
    const provider = this.detectProvider(config.model);

    switch (provider) {
      case 'OPENAI':
        return new ChatOpenAI({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          openAIApiKey: config.apiKey || process.env.OPENAI_API_KEY,
        });

      case 'ANTHROPIC':
        return new ChatAnthropic({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          anthropicApiKey: config.apiKey || process.env.ANTHROPIC_API_KEY,
        });

      default:
        throw new Error(`Unsupported LLM provider for model: ${config.model}`);
    }
  }

  /**
   * Generate chat response (non-streaming)
   * @param {string} message - User message
   * @param {string} [modelName] - Model name to use
   * @param {string} [systemPrompt] - System prompt
   * @param {Array} [conversationHistory] - Previous conversation
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Promise<string>} Generated response
   */
  static async generateResponse(
    message,
    modelName,
    systemPrompt,
    conversationHistory,
    attachedFile
  ) {
    try {
      // Try to get model with database API key first, fallback to cached model
      let model;
      try {
        model = await this.getModelWithDatabaseApiKey(modelName);
      } catch (dbError) {
        logger.warn(`Failed to get model from database, using cached model: ${dbError.message}`);
        model = this.getModel(modelName);
      }

      const messages = this.buildMessages(message, systemPrompt, conversationHistory, attachedFile);

      const response = await model.invoke(messages);

      logger.info(`LLM response generated using model: ${modelName || 'default'}`);
      return response.content;
    } catch (error) {
      logger.error('Error generating LLM response:', error);
      throw new Error('Failed to generate response from LLM');
    }
  }

  /**
   * Generate multimodal chat response (non-streaming)
   * @param {string} message - User message
   * @param {Object} attachedFile - Attached file data
   * @param {string} [modelName] - Model name to use
   * @param {string} [systemPrompt] - System prompt
   * @param {Array} [conversationHistory] - Previous conversation
   * @returns {Promise<string>} Generated response
   */
  static async generateMultimodalResponse(
    message,
    attachedFile,
    modelName,
    systemPrompt,
    conversationHistory
  ) {
    try {
      // Use GPT-4 Vision for multimodal inputs by default
      const multimodalModel = modelName && this.supportsMultimodal(modelName)
        ? modelName
        : LLM_MODELS.OPENAI.GPT_4_VISION_PREVIEW;

      const model = this.getModel(multimodalModel);
      const messages = this.buildMessages(message, systemPrompt, conversationHistory, attachedFile);

      const response = await model.invoke(messages);

      logger.info(`Multimodal LLM response generated using model: ${multimodalModel}`);
      return response.content;
    } catch (error) {
      logger.error('Error generating multimodal LLM response:', error);
      throw new Error('Failed to generate multimodal response from LLM');
    }
  }

  /**
   * Generate streaming chat response
   * @param {string} message - User message
   * @param {Object} res - Express response object
   * @param {string} [modelName] - Model name to use
   * @param {string} [systemPrompt] - System prompt
   * @param {Array} [conversationHistory] - Previous conversation
   * @param {Object} [metadata] - Additional metadata
   * @param {Function} [onComplete] - Callback when response is complete
   * @param {Object} [attachedFile] - Attached file data
   * @param {boolean} [headersAlreadySet=false] - Whether HTTP headers are already set
   * @returns {Promise<string>} Full response text
   */
  static async generateStreamingResponse(
    message,
    res,
    modelName,
    systemPrompt,
    conversationHistory,
    metadata,
    onComplete,
    attachedFile,
    headersAlreadySet = false
  ) {
    try {
      // Use multimodal model if file is attached and contains images
      const shouldUseMultimodal = attachedFile && (attachedFile.type === 'image');
      const selectedModel = shouldUseMultimodal && modelName && this.supportsMultimodal(modelName)
        ? modelName
        : shouldUseMultimodal
          ? LLM_MODELS.OPENAI.GPT_4_VISION_PREVIEW
          : modelName;

      // Try to get model with database API key first, fallback to cached model
      let model;
      try {
        model = await this.getModelWithDatabaseApiKey(selectedModel);
      } catch (dbError) {
        logger.warn(`Failed to get model from database, using cached model: ${dbError.message}`);
        model = this.getModel(selectedModel);
      }
      const messages = this.buildMessages(message, systemPrompt, conversationHistory, attachedFile);

      // Set up SSE headers only if not already set
      if (!headersAlreadySet) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });
      }

      let fullResponse = '';

      // Send initial event with metadata
      res.write(`data: ${JSON.stringify({
        type: 'start',
        message: 'Response started',
        metadata: metadata || {},
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Stream the response
      const stream = await model.stream(messages);

      for await (const chunk of stream) {
        const content = chunk.content;
        if (content) {
          fullResponse += content;
          res.write(`data: ${JSON.stringify({
            type: 'chunk',
            content: content,
            timestamp: new Date().toISOString()
          })}\n\n`);
        }
      }

      // Call completion callback if provided
      let finalMetadata = metadata || {};
      if (onComplete) {
        try {
          const callbackResult = await onComplete(fullResponse);
          finalMetadata = { ...finalMetadata, ...callbackResult };
        } catch (error) {
          logger.error('Error in onComplete callback:', error);
        }

      }      // Send completion event with updated metadata
      res.write(`data: ${JSON.stringify({
        type: 'complete',
        fullResponse: fullResponse,
        metadata: finalMetadata,
        timestamp: new Date().toISOString()
      })}\n\n`);

      res.end();

      logger.info(`LLM streaming response completed using model: ${modelName || 'default'}`);
      return fullResponse;
    } catch (error) {
      logger.error('Error generating streaming LLM response:', error);

      // Send error event
      res.write(`data: ${JSON.stringify({
        type: 'error',
        error: 'Failed to generate response from LLM',
        timestamp: new Date().toISOString()
      })}\n\n`);
      res.end();

      throw new Error('Failed to generate streaming response from LLM');
    }
}

  /**
   * Build messages array for LLM input with multimodal support
   * @param {string} message - User message
   * @param {string} [systemPrompt] - System prompt
   * @param {Array} [conversationHistory] - Previous conversation
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Array} Messages array for LLM
   */
  static buildMessages(
    message,
    systemPrompt,
    conversationHistory,
    attachedFile
  ) {
    const messages = [];

    // Add system message if provided, with file context if applicable
    if (systemPrompt) {
      let enhancedSystemPrompt = systemPrompt;
      if (attachedFile) {
        enhancedSystemPrompt += this.getSystemPromptAddition(attachedFile);
      }
      messages.push(new SystemMessage(enhancedSystemPrompt));
    } else {
      let defaultPrompt = 'You are a helpful assistant. Provide accurate, helpful, and concise responses to user questions. Use proper delimiters for Latex Expressions($...$ for inline math and $$...$$ for block math).';
      if (attachedFile) {
        defaultPrompt += this.getSystemPromptAddition(attachedFile);
      }
      messages.push(new SystemMessage(defaultPrompt));
    }

    // Add conversation history
    if (conversationHistory && conversationHistory.length > 0) {
      conversationHistory.forEach(msg => {
        if (msg.role === 'user') {
          messages.push(new HumanMessage(msg.content));
        } else if (msg.role === 'assistant') {
          messages.push(new AIMessage(msg.content));
        } else {
          // For any other role, treat as AI message to avoid multiple system messages
          messages.push(new AIMessage(msg.content));
        }
      });
    }

    logger.info('Conversation history:', systemPrompt);

    // Add current user message with multimodal content if applicable
    if (attachedFile && attachedFile.type === 'image') {
      // For images, create multimodal message
      const textContent = {
        type: 'text',
        text: message
      };

      const imageContent = Array.isArray(attachedFile.content)
        ? attachedFile.content
        : [attachedFile.content];

      const content = [textContent, ...imageContent];
      messages.push(new HumanMessage({ content: content }));
    } else if (attachedFile && (attachedFile.type === 'document' || attachedFile.type === 'text')) {
      // For documents and text, append content to message
      const enhancedMessage = `${message}\n\n--- Attached File Content ---\n${attachedFile.content}`;
      messages.push(new HumanMessage(enhancedMessage));
    } else {
      // Regular text message
      messages.push(new HumanMessage(message));
    }

    return messages;
  }

  /**
   * Get system prompt addition based on file type
   * @param {Object} processedFile - Processed file object
   * @returns {string} System prompt addition
   */
  static getSystemPromptAddition(processedFile) {
    const { type, metadata } = processedFile;

    switch (type) {
      case 'image':
        return `\n\nThe user has attached an image file (${metadata.originalName}). Please analyze the image content and respond accordingly.`;

      case 'document':
        if (metadata.mimeType === 'application/pdf') {
          return `\n\nThe user has attached a PDF document (${metadata.originalName}) which has been converted to images. Please analyze the document content and respond accordingly.`;
        } else if (metadata.mimeType.includes('word')) {
          return `\n\nThe user has attached a Word document (${metadata.originalName}) with the following content converted to markdown format. Please analyze the document and respond accordingly.`;
        } else if (metadata.mimeType.includes('sheet')) {
          return `\n\nThe user has attached an Excel spreadsheet (${metadata.originalName}) with the following data converted to markdown table format. Please analyze the data and respond accordingly.`;
        }
        return `\n\nThe user has attached a document (${metadata.originalName}). Please analyze the content and respond accordingly.`;

      case 'text':
        return `\n\nThe user has attached a text file (${metadata.originalName}) with the following content. Please analyze the text and respond accordingly.`;

      case 'code':
        const language = metadata.language || 'Unknown';
        return `\n\nThe user has attached a ${language} code file (${metadata.originalName}) with the following content. Please analyze the code and respond accordingly. You can help with code review, explanation, debugging, optimization, or any other code-related questions.`;

      default:
        return `\n\nThe user has attached a file (${metadata.originalName}). Please analyze the content and respond accordingly.`;
    }
  }

  /**
   * Check if model supports multimodal inputs
   * @param {string} modelName - Model name to check
   * @returns {boolean} Whether model supports multimodal inputs
   */
  static supportsMultimodal(modelName) {
    const multimodalModels = [
      'gpt-4-vision-preview',
      'gpt-4o',
      'gpt-4o-mini'
    ];
    return multimodalModels.includes(modelName);
  }

  /**
   * Detect LLM provider from model name
   * @param {string} modelName - Model name
   * @returns {string} Provider name
   */
  static detectProvider(modelName) {
    const openAIModels = Object.values(LLM_MODELS.OPENAI);
    const anthropicModels = Object.values(LLM_MODELS.ANTHROPIC);
    const googleModels = Object.values(LLM_MODELS.GOOGLE);
    const deepSeekModels = Object.values(LLM_MODELS.DEEPSEEK);
    const metaModels = Object.values(LLM_MODELS.META);

    if (openAIModels.includes(modelName)) {
      return 'OPENAI';
    }

    if (anthropicModels.includes(modelName)) {
      return 'ANTHROPIC';
    }

    if (googleModels.includes(modelName)) {
      return 'GOOGLE';
    }

    if (deepSeekModels.includes(modelName)) {
      return 'DEEPSEEK';
    }

    if (metaModels.includes(modelName)) {
      return 'META';
    }

    // Default fallback based on model name patterns
    if (modelName.includes('gpt') || modelName.includes('openai')) {
      return 'OPENAI';
    }

    if (modelName.includes('claude') || modelName.includes('anthropic')) {
      return 'ANTHROPIC';
    }

    if (modelName.includes('gemini') || modelName.includes('google')) {
      return 'GOOGLE';
    }

    if (modelName.includes('deepseek')) {
      return 'DEEPSEEK';
    }

    if (modelName.includes('llama') || modelName.includes('meta')) {
      return 'META';
    }

    throw new Error(`Cannot detect provider for model: ${modelName}`);
  }

  /**
   * Get available model names from in-memory cache
   * @returns {Array<string>} Array of available model names
   */
  static getAvailableModelNames() {
    return Array.from(this.models.keys());
  }

  /**
   * Check if model is available
   * @param {string} modelName - Model name to check
   * @returns {boolean} Whether model is available
   */
  static isModelAvailable(modelName) {
    return this.models.has(modelName);
  }

  /**
   * Get model statistics
   * @returns {Object} Model statistics
   */
  static getModelStats() {
    return {
      totalModels: this.models.size,
      availableModels: this.getAvailableModelNames(),
      openAIModels: Object.values(LLM_MODELS.OPENAI),
      anthropicModels: Object.values(LLM_MODELS.ANTHROPIC),
    };
  }
}
