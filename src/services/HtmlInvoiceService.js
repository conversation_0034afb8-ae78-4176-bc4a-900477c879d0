import * as fs from 'fs/promises';
import * as path from 'path';
import handlebars from 'handlebars';
import logger from '../config/logger.js';

/**
 * Optimized HTML Invoice Service
 * Uses html-pdf-node instead of Puppeteer for lighter Docker images
 * Maintains all functionality while reducing dependencies
 */
export class HtmlInvoiceService {
  static INVOICE_TEMPLATE_PATH = path.join(process.cwd(), 'src', 'templates', 'invoice.hbs');
  static LOGO_PATH = path.join(process.cwd(), 'public', 'assets', 'infini-logo.png');

  /**
   * Generate invoice PDF from transaction data
   * @param {Object} transactionData - Transaction data for invoice
   * @returns {Promise<Buffer>} PDF buffer
   */
  static async generateInvoicePDF(transactionData) {
    try {
      logger.info(`Generating invoice PDF for transaction: ${transactionData.id}`);

      // Generate HTML content
      const htmlContent = await this.generateInvoiceHTML(transactionData);

      // Convert HTML to PDF using html-pdf-node
      const pdfBuffer = await this.htmlToPdfOptimized(htmlContent);

      logger.info(`Successfully generated invoice PDF for transaction: ${transactionData.id}`);
      return pdfBuffer;

    } catch (error) {
      logger.error('Error generating invoice PDF:', error);
      throw new Error(`Failed to generate invoice PDF: ${error.message}`);
    }
  }

  /**
   * Generate HTML content for invoice
   * @param {Object} transactionData - Transaction data
   * @returns {Promise<string>} HTML content
   */
  static async generateInvoiceHTML(transactionData) {
    try {
      // Read template file
      const templateContent = await fs.readFile(this.INVOICE_TEMPLATE_PATH, 'utf-8');

      // Compile template
      const template = handlebars.compile(templateContent);

      // Prepare template data
      const templateData = await this.prepareTemplateData(transactionData);

      // Generate HTML
      const html = template(templateData);

      return html;
    } catch (error) {
      logger.error('Error generating invoice HTML:', error);
      throw error;
    }
  }

  /**
   * Prepare data for template rendering
   * @param {Object} transactionData - Raw transaction data
   * @returns {Promise<Object>} Formatted template data
   */
  static async prepareTemplateData(transactionData) {
    try {
      // Get logo as base64 for embedding
      const logoBase64 = await this.getLogoBase64();

      // Format amounts
      const formattedAmount = this.formatCurrency(transactionData.amount, transactionData.currency);
      const taxAmount = this.calculateTax(transactionData.amount);
      const formattedTax = this.formatCurrency(taxAmount, transactionData.currency);
      const totalAmount = parseFloat(transactionData.amount) + taxAmount;
      const formattedTotal = this.formatCurrency(totalAmount, transactionData.currency);

      // Get user information
      const userInfo = this.extractUserInfo(transactionData);

      return {
        // Invoice details
        invoiceNumber: transactionData.invoiceNumber || `INV-${transactionData.id}`,
        invoiceDate: this.formatDate(transactionData.paidAt || transactionData.createdAt),
        dueDate: this.formatDate(new Date()), // Immediate payment

        // Company details
        companyName: 'The Infini AI',
        companyAddress: 'AI Innovation Hub',
        companyCity: 'Tech Valley, CA 94000',
        companyEmail: '<EMAIL>',
        companyPhone: '+****************',
        logoBase64: logoBase64,

        // Customer details
        customerName: userInfo.name,
        customerEmail: userInfo.email,
        customerId: transactionData.userId,

        // Transaction details
        transactionId: transactionData.id,
        transactionType: this.formatTransactionType(transactionData.transactionType),
        paymentMethod: transactionData.paymentMethod || 'Online Payment',
        paymentStatus: 'Paid',

        // Amounts
        subtotal: formattedAmount,
        taxRate: '0%', // Adjust as needed
        taxAmount: formattedTax,
        totalAmount: formattedTotal,

        // Items
        items: this.formatInvoiceItems(transactionData),

        // Additional info
        notes: this.generateInvoiceNotes(transactionData),
        terms: 'Payment processed successfully. Thank you for your business!',

        // Styling
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        accentColor: '#10b981'
      };
    } catch (error) {
      logger.error('Error preparing template data:', error);
      throw error;
    }
  }

  /**
   * Convert HTML to PDF using html-pdf-node (lighter alternative to Puppeteer)
   * @param {string} html - HTML content
   * @param {Object} options - PDF options
   * @returns {Promise<Buffer>} PDF buffer
   */
  static async htmlToPdfOptimized(html, options = {}) {
    try {
      // Dynamic import to avoid loading if not needed
      const htmlPdf = await import('html-pdf-node');

      const pdfOptions = {
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px'
        },
        ...options
      };

      const file = { content: html };
      const pdfBuffer = await htmlPdf.generatePdf(file, pdfOptions);

      return pdfBuffer;
    } catch (error) {
      logger.error('Error converting HTML to PDF with html-pdf-node:', error);

      // Fallback to basic PDF generation if html-pdf-node fails
      return await this.fallbackPdfGeneration(html);
    }
  }

  /**
   * Fallback PDF generation using jsPDF (client-side library that works server-side)
   * @param {string} html - HTML content
   * @returns {Promise<Buffer>} PDF buffer
   */
  static async fallbackPdfGeneration(html) {
    try {
      logger.warn('Using fallback PDF generation method');

      // Dynamic import of jsPDF
      const { jsPDF } = await import('jspdf');

      const doc = new jsPDF();

      // Simple text-based PDF generation
      const lines = this.htmlToText(html).split('\n');
      let yPosition = 20;

      lines.forEach((line, index) => {
        if (yPosition > 280) { // New page
          doc.addPage();
          yPosition = 20;
        }
        doc.text(line.substring(0, 80), 10, yPosition); // Limit line length
        yPosition += 7;
      });

      return Buffer.from(doc.output('arraybuffer'));
    } catch (error) {
      logger.error('Fallback PDF generation failed:', error);
      throw new Error('PDF generation failed with all methods');
    }
  }

  /**
   * Convert HTML to plain text for fallback
   * @param {string} html - HTML content
   * @returns {string} Plain text
   */
  static htmlToText(html) {
    return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/\s+/g, ' ')
        .trim();
  }

  /**
   * Get logo as base64 string
   * @returns {Promise<string>} Base64 encoded logo
   */
  static async getLogoBase64() {
    try {
      const logoBuffer = await fs.readFile(this.LOGO_PATH);
      return `data:image/png;base64,${logoBuffer.toString('base64')}`;
    } catch (error) {
      logger.warn('Could not load logo file, using placeholder');
      return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjUwIiB2aWV3Qm94PSIwIDAgMTAwIDUwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjx0ZXh0IHg9IjUwIiB5PSIzMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjMjU2M2ViIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5UaGVJbmZpbmkgQUk8L3RleHQ+PC9zdmc+';
    }
  }

  /**
   * Extract user information from transaction data
   * @param {Object} transactionData - Transaction data
   * @returns {Object} User information
   */
  static extractUserInfo(transactionData) {
    const user = transactionData.user || {};
    const profile = user.profile || {};

    return {
      name: profile.firstName && profile.lastName
          ? `${profile.firstName} ${profile.lastName}`
          : user.email || 'Valued Customer',
      email: user.email || '<EMAIL>'
    };
  }

  /**
   * Format currency amount
   * @param {number|string} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} Formatted currency
   */
  static formatCurrency(amount, currency = 'USD') {
    const numAmount = parseFloat(amount) || 0;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(numAmount);
  }

  /**
   * Calculate tax amount (placeholder - adjust based on business rules)
   * @param {number|string} amount - Base amount
   * @returns {number} Tax amount
   */
  static calculateTax(amount) {
    // For now, return 0 tax. Implement actual tax calculation as needed
    return 0;
  }

  /**
   * Format transaction type for display
   * @param {string} transactionType - Raw transaction type
   * @returns {string} Formatted transaction type
   */
  static formatTransactionType(transactionType) {
    const typeMap = {
      'SUBSCRIPTION': 'Subscription Payment',
      'ADDON': 'Add-on Purchase',
      'UPGRADE': 'Plan Upgrade',
      'RENEWAL': 'Subscription Renewal'
    };

    return typeMap[transactionType] || transactionType;
  }

  /**
   * Format invoice items
   * @param {Object} transactionData - Transaction data
   * @returns {Array} Formatted items
   */
  static formatInvoiceItems(transactionData) {
    return [{
      description: this.formatTransactionType(transactionData.transactionType),
      quantity: 1,
      unitPrice: this.formatCurrency(transactionData.amount, transactionData.currency),
      totalPrice: this.formatCurrency(transactionData.amount, transactionData.currency)
    }];
  }

  /**
   * Generate invoice notes
   * @param {Object} transactionData - Transaction data
   * @returns {string} Invoice notes
   */
  static generateInvoiceNotes(transactionData) {
    return `Transaction ID: ${transactionData.id}\nPayment processed on: ${this.formatDate(transactionData.paidAt || transactionData.createdAt)}`;
  }

  /**
   * Format date for display
   * @param {Date|string} date - Date to format
   * @returns {string} Formatted date
   */
  static formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
