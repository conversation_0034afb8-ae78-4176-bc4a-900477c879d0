import { PaymentTransaction, UserSubscription, SubscriptionPlan } from '../models/subscription/index.js';
import { User } from '../models/user/index.js';
import { RazorpayService } from './RazorpayService.js';
import { SubscriptionService } from './SubscriptionService.js';
import { EmailService } from './EmailService.js';
import { PAYMENT_SYSTEM } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * WebhookService
 * Handles Razorpay webhook events for payments and subscriptions
 */
export class WebhookService {
  /**
   * Process Razorpay webhook
   * @param {Object} payload - Webhook payload
   * @param {string} signature - Webhook signature
   * @returns {Promise<Object>} Processing result
   */
  static async processWebhook(payload, signature) {
    try {
      // Verify webhook signature
      const razorpayService = RazorpayService.getInstance();
      const isValidSignature = razorpayService.verifyWebhookSignature(
        JSON.stringify(payload),
        signature,
        process.env.RAZORPAY_WEBHOOK_SECRET
      );

      if (!isValidSignature) {
        throw new Error('Invalid webhook signature');
      }

      const { event, payload: eventPayload } = payload;
      
      logger.info(`Processing webhook event: ${event}`);

      // Route to appropriate handler based on event type
      switch (event) {
        case 'payment.authorized':
          return await this.handlePaymentAuthorized(eventPayload.payment.entity);
        
        case 'payment.captured':
          return await this.handlePaymentCaptured(eventPayload.payment.entity);
        
        case 'payment.failed':
          return await this.handlePaymentFailed(eventPayload.payment.entity);
        
        case 'subscription.activated':
          return await this.handleSubscriptionActivated(eventPayload.subscription.entity);
        
        case 'subscription.charged':
          return await this.handleSubscriptionCharged(eventPayload.subscription.entity, eventPayload.payment.entity);
        
        case 'subscription.cancelled':
          return await this.handleSubscriptionCancelled(eventPayload.subscription.entity);
        
        case 'subscription.completed':
          return await this.handleSubscriptionCompleted(eventPayload.subscription.entity);
        
        case 'subscription.halted':
          return await this.handleSubscriptionHalted(eventPayload.subscription.entity);
        
        case 'invoice.paid':
          return await this.handleInvoicePaid(eventPayload.invoice.entity);
        
        case 'invoice.payment_failed':
          return await this.handleInvoicePaymentFailed(eventPayload.invoice.entity);
        
        default:
          logger.warn(`Unhandled webhook event: ${event}`);
          return { success: true, message: 'Event not handled' };
      }
    } catch (error) {
      logger.error('Error processing webhook:', error);
      throw error;
    }
  }

  /**
   * Handle payment authorized event
   * @param {Object} payment - Payment entity
   * @returns {Promise<Object>} Processing result
   */
  static async handlePaymentAuthorized(payment) {
    try {
      logger.info(`Payment authorized: ${payment.id}`);
      
      // Find transaction by order ID
      const transaction = await PaymentTransaction.findByRazorpayOrderId(payment.order_id);
      if (transaction) {
        transaction.paymentDetails = {
          ...transaction.paymentDetails,
          authorized: payment,
        };
        await transaction.save();
      }

      return { success: true, message: 'Payment authorized processed' };
    } catch (error) {
      logger.error('Error handling payment authorized:', error);
      throw error;
    }
  }

  /**
   * Handle payment captured event
   * @param {Object} payment - Payment entity
   * @returns {Promise<Object>} Processing result
   */
  static async handlePaymentCaptured(payment) {
    try {
      logger.info(`Payment captured: ${payment.id}`);
      
      // Find transaction by order ID
      const transaction = await PaymentTransaction.findByRazorpayOrderId(payment.order_id);
      if (transaction && transaction.status === PAYMENT_SYSTEM.STATUS.PENDING) {
        await transaction.markAsSuccessful(payment.id, { captured: payment });
        
        // Process the payment based on transaction type
        if (transaction.transactionType === PAYMENT_SYSTEM.TRANSACTION_TYPES.SUBSCRIPTION) {
          // This will be handled by subscription.activated event
        } else if (transaction.transactionType === PAYMENT_SYSTEM.TRANSACTION_TYPES.ADDON) {
          // Process addon purchase
          await this.processAddonPurchase(transaction);
        }
      }

      return { success: true, message: 'Payment captured processed' };
    } catch (error) {
      logger.error('Error handling payment captured:', error);
      throw error;
    }
  }

  /**
   * Handle payment failed event
   * @param {Object} payment - Payment entity
   * @returns {Promise<Object>} Processing result
   */
  static async handlePaymentFailed(payment) {
    try {
      logger.info(`Payment failed: ${payment.id}`);

      // Find transaction by order ID
      const transaction = await PaymentTransaction.findByRazorpayOrderId(payment.order_id);
      if (transaction) {
        await transaction.markAsFailed(
          payment.error_description || 'Payment failed',
          { failed: payment }
        );

        // Send payment failure email
        try {
          const user = await User.findByPk(transaction.userId);
          const plan = await SubscriptionPlan.findByPk(transaction.planId);

          if (user && plan) {
            const isSubscription = transaction.transactionType === PAYMENT_SYSTEM.TRANSACTION_TYPES.SUBSCRIPTION;

            await EmailService.sendPaymentFailedEmail(user.email, {
              user,
              plan,
              transaction,
              isSubscription,
            });

            logger.info(`Payment failure email sent to: ${user.email}`);
          }
        } catch (emailError) {
          logger.error('Error sending payment failure email:', emailError);
          // Don't fail the webhook processing if email fails
        }
      }

      return { success: true, message: 'Payment failed processed' };
    } catch (error) {
      logger.error('Error handling payment failed:', error);
      throw error;
    }
  }

  /**
   * Handle subscription activated event
   * @param {Object} subscription - Subscription entity
   * @returns {Promise<Object>} Processing result
   */
  static async handleSubscriptionActivated(subscription) {
    try {
      logger.info(`Subscription activated: ${subscription.id}`);
      
      // Find user subscription by Razorpay subscription ID
      const userSubscription = await UserSubscription.findByRazorpayId(subscription.id);
      if (userSubscription) {
        userSubscription.status = 'ACTIVE';
        await userSubscription.save();
      }

      return { success: true, message: 'Subscription activated processed' };
    } catch (error) {
      logger.error('Error handling subscription activated:', error);
      throw error;
    }
  }

  /**
   * Handle subscription charged event
   * @param {Object} subscription - Subscription entity
   * @param {Object} payment - Payment entity
   * @returns {Promise<Object>} Processing result
   */
  static async handleSubscriptionCharged(subscription, payment) {
    try {
      logger.info(`Subscription charged: ${subscription.id}, payment: ${payment.id}`);
      
      // Find user subscription
      const userSubscription = await UserSubscription.findByRazorpayId(subscription.id);
      if (userSubscription) {
        // Update next billing date
        const nextBillingDate = new Date(subscription.current_end * 1000);
        userSubscription.nextBillingDate = nextBillingDate;
        await userSubscription.save();

        // Create payment transaction record for the renewal
        await PaymentTransaction.createTransaction({
          userId: userSubscription.userId,
          subscriptionId: userSubscription.id,
          planId: userSubscription.planId,
          transactionType: PAYMENT_SYSTEM.TRANSACTION_TYPES.RENEWAL,
          amount: payment.amount / 100, // Convert from paise
          currency: payment.currency.toUpperCase(),
          status: PAYMENT_SYSTEM.STATUS.SUCCESS,
          razorpayPaymentId: payment.id,
          paymentMethod: payment.method,
          paidAt: new Date(payment.created_at * 1000),
          paymentDetails: { renewal: payment },
        });

        // Reset credits for the new billing cycle
        await this.resetSubscriptionCredits(userSubscription);
      }

      return { success: true, message: 'Subscription charged processed' };
    } catch (error) {
      logger.error('Error handling subscription charged:', error);
      throw error;
    }
  }

  /**
   * Handle subscription cancelled event
   * @param {Object} subscription - Subscription entity
   * @returns {Promise<Object>} Processing result
   */
  static async handleSubscriptionCancelled(subscription) {
    try {
      logger.info(`Subscription cancelled: ${subscription.id}`);
      
      // Find user subscription
      const userSubscription = await UserSubscription.findByRazorpayId(subscription.id);
      if (userSubscription) {
        await userSubscription.cancel('Cancelled via webhook');
        
        // Downgrade to free plan
        await SubscriptionService.downgradeToFreePlan(userSubscription.userId);
      }

      return { success: true, message: 'Subscription cancelled processed' };
    } catch (error) {
      logger.error('Error handling subscription cancelled:', error);
      throw error;
    }
  }

  /**
   * Handle subscription completed event
   * @param {Object} subscription - Subscription entity
   * @returns {Promise<Object>} Processing result
   */
  static async handleSubscriptionCompleted(subscription) {
    try {
      logger.info(`Subscription completed: ${subscription.id}`);
      
      // Find user subscription
      const userSubscription = await UserSubscription.findByRazorpayId(subscription.id);
      if (userSubscription) {
        userSubscription.status = 'EXPIRED';
        await userSubscription.save();
        
        // Downgrade to free plan
        await SubscriptionService.downgradeToFreePlan(userSubscription.userId);
      }

      return { success: true, message: 'Subscription completed processed' };
    } catch (error) {
      logger.error('Error handling subscription completed:', error);
      throw error;
    }
  }

  /**
   * Handle subscription halted event
   * @param {Object} subscription - Subscription entity
   * @returns {Promise<Object>} Processing result
   */
  static async handleSubscriptionHalted(subscription) {
    try {
      logger.info(`Subscription halted: ${subscription.id}`);
      
      // Find user subscription
      const userSubscription = await UserSubscription.findByRazorpayId(subscription.id);
      if (userSubscription) {
        // Set grace period (3 days from now)
        const gracePeriodEnd = new Date();
        gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 3);
        
        await userSubscription.suspend(gracePeriodEnd);
      }

      return { success: true, message: 'Subscription halted processed' };
    } catch (error) {
      logger.error('Error handling subscription halted:', error);
      throw error;
    }
  }

  /**
   * Handle invoice paid event
   * @param {Object} invoice - Invoice entity
   * @returns {Promise<Object>} Processing result
   */
  static async handleInvoicePaid(invoice) {
    try {
      logger.info(`Invoice paid: ${invoice.id}`);
      
      // Additional processing for invoice payments if needed
      
      return { success: true, message: 'Invoice paid processed' };
    } catch (error) {
      logger.error('Error handling invoice paid:', error);
      throw error;
    }
  }

  /**
   * Handle invoice payment failed event
   * @param {Object} invoice - Invoice entity
   * @returns {Promise<Object>} Processing result
   */
  static async handleInvoicePaymentFailed(invoice) {
    try {
      logger.info(`Invoice payment failed: ${invoice.id}`);
      
      // Handle failed invoice payment
      // This could trigger retry logic or grace period
      
      return { success: true, message: 'Invoice payment failed processed' };
    } catch (error) {
      logger.error('Error handling invoice payment failed:', error);
      throw error;
    }
  }

  /**
   * Process addon purchase from webhook
   * @param {Object} transaction - Payment transaction
   * @returns {Promise<void>}
   */
  static async processAddonPurchase(transaction) {
    try {
      // This would be handled by PaymentService.processAddonPayment
      // but we can add webhook-specific logic here if needed
      logger.info(`Processing addon purchase from webhook: ${transaction.id}`);
    } catch (error) {
      logger.error('Error processing addon purchase from webhook:', error);
      throw error;
    }
  }

  /**
   * Reset subscription credits for new billing cycle
   * @param {Object} userSubscription - User subscription
   * @returns {Promise<void>}
   */
  static async resetSubscriptionCredits(userSubscription) {
    try {
      // This would be handled by the credit reset service
      // but we can trigger it from webhook events
      logger.info(`Resetting credits for subscription: ${userSubscription.id}`);
    } catch (error) {
      logger.error('Error resetting subscription credits:', error);
      throw error;
    }
  }
}
