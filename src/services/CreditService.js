import { UserCredit, UserCreditTransaction } from '../models/user/index.js';
import { CREDIT_SYSTEM } from '../utils/constants.js';
import logger from '../config/logger.js';

export class CreditService {
  /**
   * Initialize user credits (called when user is first verified)
   * @param {string} userId - User ID
   * @param {number} [initialCredits] - Initial credits to assign
   * @returns {Promise<Object>} User credit record
   */
  static async initializeUserCredits(userId, initialCredits = CREDIT_SYSTEM.INITIAL_CREDITS) {
    try {
      // Check if user already has credits
      const existingCredit = await UserCredit.findByUserId(userId);
      if (existingCredit) {
        logger.info(`User ${userId} already has credits: ${existingCredit.credits}`);
        return existingCredit;
      }

      // Create new credit record
      const userCredit = await UserCredit.createUserCredit(userId, initialCredits);

      // Create initial transaction record
      await UserCreditTransaction.createTransaction({
        userId,
        amount: initialCredits,
        type: CREDIT_SYSTEM.TRANSACTION_TYPES.CREDIT,
        description: CREDIT_SYSTEM.DESCRIPTIONS.INITIAL_CREDITS,
      });

      logger.info(`Initialized credits for user ${userId}: ${initialCredits} credits`);
      return userCredit;
    } catch (error) {
      logger.error('Error initializing user credits:', error);
      throw error;
    }
  }

  /**
   * Get user credits
   * @param {string} userId - User ID
   * @returns {Promise<number>} Current credit balance
   */
  static async getUserCredits(userId) {
    try {
      const userCredit = await UserCredit.findByUserId(userId);
      return userCredit ? userCredit.credits : 0;
    } catch (error) {
      logger.error('Error getting user credits:', error);
      throw error;
    }
  }

  /**
   * Check if user has enough credits
   * @param {string} userId - User ID
   * @param {number} requiredCredits - Required credit amount
   * @returns {Promise<boolean>} Whether user has enough credits
   */
  static async hasEnoughCredits(userId, requiredCredits) {
    try {
      const userCredit = await UserCredit.findByUserId(userId);
      if (!userCredit) {
        return false;
      }
      return userCredit.hasEnoughCredits(requiredCredits);
    } catch (error) {
      logger.error('Error checking user credits:', error);
      return false;
    }
  }

  /**
   * Deduct credits from user account (for chat messages)
   * @param {string} userId - User ID
   * @param {number} amount - Amount to deduct
   * @param {string} [description] - Transaction description
   * @returns {Promise<boolean>} Whether deduction was successful
   */
  static async deductCredits(
    userId,
    amount,
    description = CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
  ) {
    try {
      const userCredit = await UserCredit.findByUserId(userId);
      if (!userCredit) {
        throw new Error('User credit record not found');
      }

      // Check if user has enough credits
      if (!userCredit.hasEnoughCredits(amount)) {
        logger.warn(`User ${userId} has insufficient credits. Required: ${amount}, Available: ${userCredit.credits}`);
        return false;
      }

      // Deduct credits
      const success = await userCredit.deductCredits(amount);
      if (!success) {
        return false;
      }

      // Create transaction record
      await UserCreditTransaction.createTransaction({
        userId,
        amount,
        type: CREDIT_SYSTEM.TRANSACTION_TYPES.DEBIT,
        description,
      });

      logger.info(`Deducted ${amount} credits from user ${userId}. New balance: ${userCredit.credits}`);
      return true;
    } catch (error) {
      logger.error('Error deducting user credits:', error);
      throw error;
    }
  }

  /**
   * Add credits to user account
   * @param {string} userId - User ID
   * @param {number} amount - Amount to add
   * @param {string} [description] - Transaction description
   * @returns {Promise<void>}
   */
  static async addCredits(
    userId,
    amount,
    description = 'Credit added'
  ) {
    try {
      const [userCredit] = await UserCredit.findOrCreateByUserId(userId, 0);

      // Add credits
      await userCredit.addCredits(amount);

      // Create transaction record
      await UserCreditTransaction.createTransaction({
        userId,
        amount,
        type: CREDIT_SYSTEM.TRANSACTION_TYPES.CREDIT,
        description,
      });

      logger.info(`Added ${amount} credits to user ${userId}. New balance: ${userCredit.credits}`);
    } catch (error) {
      logger.error('Error adding user credits:', error);
      throw error;
    }
  }

  /**
   * Set user credits to specific amount
   * @param {string} userId - User ID
   * @param {number} amount - Amount to set
   * @param {string} [description] - Transaction description
   * @returns {Promise<void>}
   */
  static async setUserCredits(
    userId,
    amount,
    description = 'Credits set'
  ) {
    try {
      const [userCredit] = await UserCredit.findOrCreateByUserId(userId, 0);
      const previousCredits = userCredit.credits;

      // Set credits to specific amount
      userCredit.credits = amount;
      await userCredit.save();

      // Create transaction record for the difference
      const difference = amount - previousCredits;
      if (difference !== 0) {
        await UserCreditTransaction.createTransaction({
          userId,
          amount: Math.abs(difference),
          type: difference > 0 ? CREDIT_SYSTEM.TRANSACTION_TYPES.CREDIT : CREDIT_SYSTEM.TRANSACTION_TYPES.DEBIT,
          description,
        });
      }

      logger.info(`Set credits for user ${userId} to ${amount}. Previous: ${previousCredits}`);
    } catch (error) {
      logger.error('Error setting user credits:', error);
      throw error;
    }
  }

  /**
   * Get user credit transaction history
   * @param {string} userId - User ID
   * @param {number} [limit] - Number of transactions to return
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} Transaction history
   */
  static async getCreditHistory(userId, limit = 50, offset = 0) {
    try {
      return await UserCreditTransaction.getUserTransactions(userId, limit, offset);
    } catch (error) {
      logger.error('Error getting credit history:', error);
      throw error;
    }
  }

  /**
   * Get credit statistics for user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Credit statistics
   */
  static async getCreditStats(userId) {
    try {
      const [userCredit, transactions] = await Promise.all([
        UserCredit.findByUserId(userId),
        UserCreditTransaction.getUserTransactions(userId, 100, 0)
      ]);

      const currentCredits = userCredit ? userCredit.credits : 0;
      const totalEarned = transactions
        .filter(t => t.type === CREDIT_SYSTEM.TRANSACTION_TYPES.CREDIT)
        .reduce((sum, t) => sum + t.amount, 0);
      const totalSpent = transactions
        .filter(t => t.type === CREDIT_SYSTEM.TRANSACTION_TYPES.DEBIT)
        .reduce((sum, t) => sum + t.amount, 0);

      return {
        currentCredits,
        totalEarned,
        totalSpent,
        transactionCount: transactions.length,
      };
    } catch (error) {
      logger.error('Error getting credit stats:', error);
      throw error;
    }
  }

  /**
   * Reset user credits (admin function)
   * @param {string} userId - User ID
   * @param {number} newAmount - New credit amount
   * @param {string} [reason] - Reason for reset
   * @returns {Promise<void>}
   */
  static async resetCredits(userId, newAmount, reason = 'Admin reset') {
    try {
      const [userCredit] = await UserCredit.findOrCreateByUserId(userId, 0);
      const oldAmount = userCredit.credits;

      // Update credits
      await userCredit.update({ credits: newAmount });

      // Create transaction record
      const difference = newAmount - oldAmount;
      await UserCreditTransaction.createTransaction({
        userId,
        amount: Math.abs(difference),
        type: difference >= 0 ? CREDIT_SYSTEM.TRANSACTION_TYPES.CREDIT : CREDIT_SYSTEM.TRANSACTION_TYPES.DEBIT,
        description: `${reason} (${oldAmount} → ${newAmount})`,
      });

      logger.info(`Reset credits for user ${userId}: ${oldAmount} → ${newAmount}. Reason: ${reason}`);
    } catch (error) {
      logger.error('Error resetting user credits:', error);
      throw error;
    }
  }

  /**
   * Get low credit users (for notifications)
   * @param {number} [threshold] - Credit threshold
   * @returns {Promise<Array>} Users with low credits
   */
  static async getLowCreditUsers(threshold = CREDIT_SYSTEM.LOW_CREDIT_THRESHOLD) {
    try {
      return await UserCredit.findLowCreditUsers(threshold);
    } catch (error) {
      logger.error('Error getting low credit users:', error);
      throw error;
    }
  }

  /**
   * Bulk add credits to multiple users
   * @param {Array} userCredits - Array of {userId, amount, description}
   * @returns {Promise<void>}
   */
  static async bulkAddCredits(userCredits) {
    try {
      for (const { userId, amount, description } of userCredits) {
        await this.addCredits(userId, amount, description);
      }
      logger.info(`Bulk added credits to ${userCredits.length} users`);
    } catch (error) {
      logger.error('Error bulk adding credits:', error);
      throw error;
    }
  }

  /**
   * Get credit summary for user (combines stats and current balance)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Credit summary
   */
  static async getCreditSummary(userId) {
    try {
      const [userCredit, stats] = await Promise.all([
        UserCredit.findByUserId(userId),
        this.getCreditStats(userId)
      ]);

      return {
        currentCredits: userCredit ? userCredit.credits : 0,
        totalEarned: stats.totalEarned,
        totalSpent: stats.totalSpent,
        transactionCount: stats.transactionCount,
        lastUpdated: userCredit ? userCredit.updatedAt : null,
      };
    } catch (error) {
      logger.error('Error getting credit summary:', error);
      throw error;
    }
  }

  /**
   * Get credit transactions with pagination
   * @param {string} userId - User ID
   * @param {number} [limit] - Number of transactions to return
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Object>} Transactions and total count
   */
  static async getCreditTransactions(userId, limit = 50, offset = 0) {
    try {
      const transactions = await UserCreditTransaction.getUserTransactions(userId, limit, offset);
      const total = await UserCreditTransaction.count({ where: { userId } });

      return {
        transactions,
        total,
      };
    } catch (error) {
      logger.error('Error getting credit transactions:', error);
      throw error;
    }
  }
}
