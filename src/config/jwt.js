import jwt from 'jsonwebtoken';

/**
 * JWT Utility Class
 * Handles JWT token generation and verification
 */
class JWTUtil {
  static SECRET = process.env.JWT_SECRET || 'your_super_secret_jwt_key';
  static EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

  /**
   * Generate a JWT token
   * @param {Object} payload - The payload to encode (without iat and exp)
   * @returns {string} The generated JWT token
   */
  static generateToken(payload) {
    return jwt.sign(payload, this.SECRET, {
      expiresIn: this.EXPIRES_IN,
    });
  }

  /**
   * Verify and decode a JWT token
   * @param {string} token - The JWT token to verify
   * @returns {Object} The decoded payload
   * @throws {Error} If token is invalid or expired
   */
  static verifyToken(token) {
    try {
      return jwt.verify(token, this.SECRET);
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Decode a JWT token without verification (for debugging)
   * @param {string} token - The JWT token to decode
   * @returns {Object} The decoded payload
   */
  static decodeToken(token) {
    return jwt.decode(token);
  }

  /**
   * Check if a token is expired
   * @param {string} token - The JWT token to check
   * @returns {boolean} True if token is expired
   */
  static isTokenExpired(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) {
        return true;
      }
      return Date.now() >= decoded.exp * 1000;
    } catch (error) {
      return true;
    }
  }
}

export { JWTUtil  };
