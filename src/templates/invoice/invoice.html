<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{invoiceNumber}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #2d3748;
            background: white;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 30px;
            border-bottom: 3px solid #FCD469;
            margin-bottom: 40px;
            background: #1e1e1e;
        }

        .logo-section {
            display: flex;
            align-items: center;
        }

        .logo {
            width: 80px;
            height: 40px;
            margin-right: 15px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #FCD469;
            margin: 0;
        }

        .company-tagline {
            font-size: 12px;
            color: #4a5568;
            margin: 0;
        }

        .invoice-title {
            font-size: 36px;
            font-weight: bold;
            text-align: right;
            color: #fff;
        }

        .invoice-details {
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
        }

        .detail-label {
            font-weight: 500;
            color: #4a5568;
        }

        .detail-value {
            font-weight: 600;
            color: #2d3748;
        }

        .billing-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .billing-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
        }

        .billing-title {
            font-size: 16px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
            border-bottom: 2px solid #FCD469;
            padding-bottom: 8px;
        }

        .billing-info {
            line-height: 1.8;
        }

        .customer-name {
            font-weight: bold;
            font-size: 16px;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .items-table th {
            background: #FCD469;
            color: #2d3748;
            font-weight: bold;
            padding: 15px;
            text-align: left;
            font-size: 14px;
        }

        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #e2e8f0;
        }

        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .items-table tr:last-child td {
            border-bottom: none;
        }

        .text-right {
            text-align: right;
        }

        .totals-section {
            margin-left: auto;
            width: 300px;
            margin-bottom: 40px;
        }

        .totals-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .totals-row:last-child {
            background: #FCD469;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            color: #2d3748;
            border: none;
            margin-top: 10px;
        }

        .footer {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
            border-top: 3px solid #FCD469;
        }

        .footer-title {
            font-size: 18px;
            font-weight: bold;
            color: #FCD469;
            margin-bottom: 10px;
        }

        .footer-text {
            color: #4a5568;
            font-size: 12px;
        }

        .status-paid {
            background: #38a169;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                padding: 20px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <img src="{{logoBase64}}" alt="The Infini AI Logo" class="logo">
            </div>
            <div class="invoice-title">INVOICE</div>
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="details-grid">
                <div class="detail-item">
                    <span class="detail-label">Invoice Number:</span>
                    <span class="detail-value">{{invoiceNumber}}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Date:</span>
                    <span class="detail-value">{{date}}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Due Date:</span>
                    <span class="detail-value">{{dueDate}}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Status:</span>
                    <span class="status-paid">PAID</span>
                </div>
            </div>
        </div>

        <!-- Billing Information -->
        <div class="billing-section">
            <div class="billing-card">
                <div class="billing-title">Billed To</div>
                <div class="billing-info">
                    <div class="customer-name">{{customer.name}}</div>
                    <div>{{customer.email}}</div>
                </div>
            </div>
            <div class="billing-card">
                <div class="billing-title">From</div>
                <div class="billing-info">
                    <div class="customer-name">The Infini AI</div>
                    <div><EMAIL></div>
                    <div>theinfiniai.live</div>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th class="text-right">Price</th>
                    <th class="text-right">Qty</th>
                    <th class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                {{#each items}}
                <tr>
                    <td>{{description}}</td>
                    <td class="text-right">{{../currency}} {{formatPrice price}}</td>
                    <td class="text-right">{{quantity}}</td>
                    <td class="text-right">{{../currency}} {{formatPrice total}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="totals-row">
                <span>Subtotal:</span>
                <span>{{currency}} {{formatPrice subtotal}}</span>
            </div>
            {{#if tax}}
            <div class="totals-row">
                <span>Tax:</span>
                <span>{{currency}} {{formatPrice tax}}</span>
            </div>
            {{/if}}
            <div class="totals-row">
                <span>Total:</span>
                <span>{{currency}} {{formatPrice total}}</span>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-title">Thank you for your business!</div>
            <div class="footer-text">
                For support, contact <NAME_EMAIL><br>
                Transaction ID: {{transactionId}} | Order ID: {{orderId}}
            </div>
        </div>
    </div>
</body>
</html>
