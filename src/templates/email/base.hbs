<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8fafc;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
        }

        .logo {
            display: inline-block;
            margin-bottom: 15px;
        }

        .logo img {
            height: 40px;
            width: auto;
        }

        .tagline {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 400;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .otp-container {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }

        .otp-label {
            font-size: 14px;
            color: #718096;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }

        .otp-code {
            font-size: 36px;
            font-weight: 700;
            color: #2d3748;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }

        .otp-expiry {
            font-size: 14px;
            color: #e53e3e;
            margin-top: 15px;
            font-weight: 500;
        }

        .purchase-summary {
            background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
            border: 2px solid #38a169;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
        }

        .purchase-title {
            font-size: 20px;
            font-weight: 700;
            color: #2f855a;
            margin-bottom: 20px;
            text-align: center;
        }

        .purchase-details {
            margin: 20px 0;
        }

        .purchase-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #c6f6d5;
        }

        .purchase-row:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 18px;
            color: #2f855a;
        }

        .purchase-label {
            color: #4a5568;
            font-weight: 500;
        }

        .purchase-value {
            color: #2d3748;
            font-weight: 600;
        }

        .invoice-section {
            background-color: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .download-button {
            display: inline-block;
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
            color: #ffffff;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            margin: 10px 5px;
            transition: transform 0.2s ease;
        }

        .download-button:hover {
            transform: translateY(-2px);
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .security-notice {
            background-color: #fef5e7;
            border-left: 4px solid #f6ad55;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .security-notice h4 {
            color: #c05621;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .security-notice p {
            color: #744210;
            font-size: 14px;
            line-height: 1.6;
        }

        .cta-section {
            text-align: center;
            margin: 30px 0;
        }

        .cta-button.secondary {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
            color: #4a5568;
            border: 2px solid #a0aec0;
        }

        .cta-button.secondary:hover {
            background: linear-gradient(135deg, #cbd5e0 0%, #a0aec0 100%);
            color: #2d3748;
        }

        .footer {
            background-color: #f7fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            font-size: 14px;
            color: #718096;
            margin-bottom: 15px;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        .contact-info {
            font-size: 12px;
            color: #a0aec0;
            margin-top: 20px;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 30px 20px;
            }
            
            .otp-code {
                font-size: 28px;
                letter-spacing: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <img src="{{logoUrl}}" alt="The Infini AI" style="height: 40px; width: auto;" />
            </div>
            <div class="tagline">Infinite Possibilities, Intelligent Solutions</div>
        </div>
        
        <div class="content">
            {{{body}}}
        </div>
        
        <div class="footer">
            <div class="footer-text">
                Thank you for choosing The Infini AI
            </div>
            
            <div class="social-links">
                <a href="#">Website</a>
                <a href="#">Support</a>
                <a href="#">Privacy Policy</a>
            </div>
            
            <div class="contact-info">
                This email was <NAME_EMAIL><br>
                If you have any questions, please contact our support team.
            </div>
        </div>
    </div>
</body>
</html>
