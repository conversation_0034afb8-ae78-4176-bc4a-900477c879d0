<div class="greeting">
    ⚠️ Payment Issue Detected
</div>

<div class="message">
    We encountered an issue processing your payment for <strong>{{planName}}</strong>. Don't worry - we're here to help you resolve this quickly.
</div>

<div class="purchase-summary">
    <div class="purchase-title">❌ Payment Details</div>
    <div class="purchase-details">
        <div class="purchase-row">
            <span class="purchase-label">Plan/Add-on:</span>
            <span class="purchase-value">{{planName}}</span>
        </div>
        <div class="purchase-row">
            <span class="purchase-label">Amount:</span>
            <span class="purchase-value">{{currency}} {{amount}}</span>
        </div>
        <div class="purchase-row">
            <span class="purchase-label">Payment Date:</span>
            <span class="purchase-value">{{paymentDate}}</span>
        </div>
        <div class="purchase-row">
            <span class="purchase-label">Status:</span>
            <span class="purchase-value" style="color: #e53e3e;">Failed</span>
        </div>
        {{#if failureReason}}
        <div class="purchase-row">
            <span class="purchase-label">Reason:</span>
            <span class="purchase-value">{{failureReason}}</span>
        </div>
        {{/if}}
    </div>
</div>

<div class="message">
    <strong>🔧 What You Can Do:</strong><br>
    1. Check if your payment method has sufficient funds<br>
    2. Verify your card details are correct<br>
    3. Try a different payment method<br>
    4. Contact your bank if the issue persists
</div>

<div class="invoice-section">
    <h4>🔄 Retry Payment</h4>
    <p>You can retry your payment anytime from your account dashboard:</p>
    <a href="{{dashboardUrl}}" class="download-button">🔄 Retry Payment</a>
</div>

<div class="security-notice">
    <h4>⏰ Important Information</h4>
    <p>
        {{#if isSubscription}}
        • Your subscription will remain active for a grace period<br>
        • Please retry payment within 7 days to avoid service interruption<br>
        {{else}}
        • Your add-on purchase was not completed<br>
        • No charges were made to your account<br>
        {{/if}}
        • Contact support if you need assistance: <EMAIL>
    </p>
</div>

<div class="message">
    We apologize for the inconvenience. Our support team is ready to help if you need assistance with your payment.
</div>
