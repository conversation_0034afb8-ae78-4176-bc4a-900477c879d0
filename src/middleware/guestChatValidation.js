import { ResponseUtil } from '../utils/response.js';
import { ERROR_MESSAGES, VALIDATION_RULES } from '../utils/constants.js';
import { GuestSession } from '../models/chat/GuestSession.js';
import logger from '../config/logger.js';

/**
 * Middleware to validate guest chat session limits and restrictions
 */

/**
 * Middleware to check if guest user has reached message limit
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const checkGuestMessageLimit = async (req, res, next) => {
  try {
    // Skip if user is authenticated
    if (req.user) {
      next();
      return;
    }

    const sessionId = req.sessionId;
    
    if (!sessionId) {
      // No session ID means this is a new session, allow it to proceed
      next();
      return;
    }

    // Find existing guest session
    const guestSession = await GuestSession.findBySessionId(sessionId);
    
    if (!guestSession) {
      // No existing session found, allow new session creation
      next();
      return;
    }

    // Check if session has reached the message limit
    if (guestSession.hasReachedLimit(VALIDATION_RULES.GUEST_CHAT_LIMIT)) {
      logger.warn(`Guest session ${sessionId} has reached message limit (${guestSession.messageCount}/${VALIDATION_RULES.GUEST_CHAT_LIMIT})`);
      ResponseUtil.forbidden(res, ERROR_MESSAGES.GUEST_LIMIT_EXCEEDED);
      return;
    }

    // Add session info to request for later use
    req.guestSession = guestSession;
    next();
  } catch (error) {
    logger.error('Error checking guest message limit:', error);
    next(); // Continue on error to avoid blocking legitimate requests
  }
};

/**
 * Middleware to check IP-based restrictions for new guest sessions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const checkGuestIPRestriction = async (req, res, next) => {
  try {
    // Skip if user is authenticated
    if (req.user) {
      next();
      return;
    }

    const clientIP = req.clientIP;
    const sessionId = req.sessionId;
    
    if (!clientIP) {
      logger.warn('No client IP found for guest session validation');
      next(); // Continue without IP validation
      return;
    }

    // Check if this is a new session (no existing session ID or session not found)
    let isNewSession = false;
    
    if (!sessionId) {
      isNewSession = true;
    } else {
      const existingSession = await GuestSession.findBySessionId(sessionId);
      if (!existingSession) {
        isNewSession = true;
      }
    }

    // Only check IP restrictions for new sessions
    if (isNewSession) {
      const validation = await GuestSession.validateNewSession(clientIP, sessionId);
      
      if (!validation.canCreate) {
        if (validation.reason === 'IP_ALREADY_EXISTS') {
          logger.warn(`IP ${clientIP} already has an active guest session`);
          ResponseUtil.forbidden(res, ERROR_MESSAGES.GUEST_IP_ALREADY_EXISTS);
          return;
        }
      }
    }

    next();
  } catch (error) {
    logger.error('Error checking guest IP restriction:', error);
    next(); // Continue on error to avoid blocking legitimate requests
  }
};

/**
 * Combined middleware to check both message limits and IP restrictions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const validateGuestChatSession = async (req, res, next) => {
  try {
    // Skip if user is authenticated
    if (req.user) {
      next();
      return;
    }

    const sessionId = req.sessionId;
    const clientIP = req.clientIP;
    
    if (!clientIP) {
      logger.warn('No client IP found for guest session validation');
      next(); // Continue without IP validation
      return;
    }

    // Check existing session limits first
    if (sessionId) {
      const guestSession = await GuestSession.findBySessionId(sessionId);
      
      if (guestSession) {
        // Check message limit for existing session
        if (guestSession.hasReachedLimit(VALIDATION_RULES.GUEST_CHAT_LIMIT)) {
          logger.warn(`Guest session ${sessionId} has reached message limit (${guestSession.messageCount}/${VALIDATION_RULES.GUEST_CHAT_LIMIT})`);
          ResponseUtil.forbidden(res, ERROR_MESSAGES.GUEST_LIMIT_EXCEEDED);
          return;
        }
        
        // Add session info to request
        req.guestSession = guestSession;
        next();
        return;
      }
    }

    // This is a new session, check IP restrictions
    const validation = await GuestSession.validateNewSession(clientIP, sessionId);
    
    if (!validation.canCreate) {
      if (validation.reason === 'IP_ALREADY_EXISTS') {
        logger.warn(`IP ${clientIP} already has an active guest session`);
        ResponseUtil.forbidden(res, ERROR_MESSAGES.GUEST_IP_ALREADY_EXISTS);
        return;
      }
    }

    next();
  } catch (error) {
    logger.error('Error validating guest chat session:', error);
    next(); // Continue on error to avoid blocking legitimate requests
  }
};

/**
 * Middleware to increment guest session message count after successful message processing
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const incrementGuestMessageCount = async (req, res, next) => {
  try {
    // Skip if user is authenticated
    if (req.user) {
      next();
      return;
    }

    const sessionId = req.sessionId;
    
    if (sessionId && req.guestSession) {
      await req.guestSession.incrementMessageCount();
      logger.debug(`Incremented message count for guest session ${sessionId}: ${req.guestSession.messageCount}`);
    }

    next();
  } catch (error) {
    logger.error('Error incrementing guest message count:', error);
    next(); // Continue on error
  }
};

export {
  checkGuestMessageLimit,
  checkGuestIPRestriction,
  validateGuestChatSession,
  incrementGuestMessageCount,
};
