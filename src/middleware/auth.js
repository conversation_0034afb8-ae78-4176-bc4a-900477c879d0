import { AuthService  } from '../services/AuthService.js';
import { ResponseUtil  } from '../utils/response.js';
import { ERROR_MESSAGES  } from '../utils/constants.js';
import logger from '../config/logger.js';

import { EncryptionUtil  } from '../utils/encryption.js';

/**
 * Middleware to authenticate JWT token
 * @param {Object} req - Express request object (AuthenticatedRequest)
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    // Verify token
    const payload = await AuthService.verifyToken(token);
    req.user = payload;

    logger.debug(`User authenticated: ${payload.userId}`);
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token
 * @param {Object} req - Express request object (AuthenticatedRequest)
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      try {
        const payload = await AuthService.verifyToken(token);
        req.user = payload;
        logger.debug(`User optionally authenticated: ${payload.userId}`);
      } catch (error) {
        // Token is invalid, but we continue without authentication
        logger.debug('Invalid token in optional auth, continuing without auth');
      }
    }

    next();
  } catch (error) {
    logger.error('Optional authentication error:', error);
    next(); // Continue without authentication
  }
};

/**
 * Middleware to extract session ID from headers or generate new one
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const extractSessionId = (req, res, next) => {
  try {
    // Try to get session ID from headers
    let sessionId = req.headers['x-session-id'];
    
    // If no session ID provided, generate a new one
    if (!sessionId) {
      sessionId = EncryptionUtil.generateSessionId();
      logger.debug(`Generated new session ID: ${sessionId}`);
    } else {
      logger.debug(`Using existing session ID: ${sessionId}`);
    }

    req.sessionId = sessionId;
    
    // Set session ID in response header for client to use
    res.setHeader('X-Session-ID', sessionId);
    
    next();
  } catch (error) {
    logger.error('Error extracting session ID:', error);
    next();
  }
};

/**
 * Normalize IP address to handle IPv6 and local development scenarios
 * @param {string} ip - Raw IP address
 * @returns {string} Normalized IP address
 */
const normalizeIPAddress = (ip) => {
  if (!ip) return '127.0.0.1';

  // Remove any port numbers
  const cleanIP = ip.split(':').slice(0, -1).join(':') || ip;

  // Handle IPv6 loopback (::1) and convert to IPv4 equivalent
  if (cleanIP === '::1' || cleanIP === '::ffff:127.0.0.1') {
    return '127.0.0.1';
  }

  // Handle IPv6-mapped IPv4 addresses (::ffff:***********)
  if (cleanIP.startsWith('::ffff:')) {
    const ipv4Part = cleanIP.substring(7);
    // Validate if it's a valid IPv4 address
    if (/^(\d{1,3}\.){3}\d{1,3}$/.test(ipv4Part)) {
      return ipv4Part;
    }
  }

  // Handle other IPv6 addresses - for guest session purposes,
  // we'll use a simplified representation
  if (cleanIP.includes(':') && cleanIP !== '127.0.0.1') {
    // For IPv6, use the first 4 segments to create a unique identifier
    const segments = cleanIP.split(':').filter(segment => segment.length > 0);
    if (segments.length >= 2) {
      return `ipv6-${segments.slice(0, 4).join('-')}`;
    }
  }

  return cleanIP;
};

/**
 * Middleware to extract client IP address
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const extractClientIP = (req, res, next) => {
  try {
    // Get client IP from various possible headers
    let rawIP = req.headers['x-forwarded-for'] ||
                req.headers['x-real-ip'] ||
                req.connection?.remoteAddress ||
                req.socket?.remoteAddress ||
                req.ip ||
                '127.0.0.1';

    // Handle comma-separated IPs (from proxies)
    if (Array.isArray(rawIP)) {
      rawIP = rawIP[0];
    } else {
      rawIP = rawIP.split(',')[0].trim();
    }

    // Normalize the IP address
    req.clientIP = normalizeIPAddress(rawIP);

    logger.debug(`Client IP extracted: ${req.clientIP} (from raw: ${rawIP})`);
    next();
  } catch (error) {
    logger.error('Error extracting client IP:', error);
    req.clientIP = '127.0.0.1';
    next();
  }
};

/**
 * Middleware to check if user is active
 * @param {Object} req - Express request object (AuthenticatedRequest)
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const checkUserActive = async (req, res, next) => {
  try {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    // This check is already done in AuthService.verifyToken
    // but we can add additional checks here if needed
    next();
  } catch (error) {
    logger.error('Error checking user active status:', error);
    ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
  }
};

/**
 * Middleware to validate user permissions
 * @param {string} permission - Required permission
 * @returns {Function} Middleware function
 */
const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
        return;
      }

      // In a more complex system, you would check user roles/permissions here
      // For now, all authenticated users have all permissions
      next();
    } catch (error) {
      logger.error('Error checking user permissions:', error);
      ResponseUtil.forbidden(res, ERROR_MESSAGES.FORBIDDEN);
    }
  };
};

/**
 * Rate limiting middleware per user
 * @param {number} maxRequests - Maximum number of requests
 * @param {number} windowMs - Time window in milliseconds
 * @returns {Function} Middleware function
 */
const rateLimitPerUser = (maxRequests, windowMs) => {
  const userRequests = new Map();

  return (req, res, next) => {
    try {
      const userId = req.user?.userId || req.clientIP || 'anonymous';
      const now = Date.now();

      const userLimit = userRequests.get(userId);
      
      if (!userLimit || now > userLimit.resetTime) {
        // Reset or initialize limit
        userRequests.set(userId, {
          count: 1,
          resetTime: now + windowMs,
        });
        next();
        return;
      }

      if (userLimit.count >= maxRequests) {
        ResponseUtil.error(res, 'Rate limit exceeded', undefined, 429);
        return;
      }

      userLimit.count++;
      next();
    } catch (error) {
      logger.error('Error in rate limiting:', error);
      next();
    }
  };
};

export { authenticateToken,
  optionalAuth,
  extractSessionId,
  extractClientIP,
  checkUserActive,
  requirePermission,
  rateLimitPerUser
 };
