import { AddonService } from '../services/AddonService.js';
import { ResponseUtil } from '../utils/response.js';
import logger from '../config/logger.js';

/**
 * Middleware to check file upload limits before processing files
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const checkFileUploadLimits = async (req, res, next) => {
  try {
    // Skip limit check for guest users (they have their own limits)
    if (!req.user) {
      return next();
    }

    // Only check limits if a file is being uploaded
    if (!req.file) {
      return next();
    }

    const userId = req.user.userId;
    
    // Check if user can upload files
    const fileCheck = await AddonService.canUploadFiles(userId, 1);
    
    if (!fileCheck.canUpload) {
      logger.warn(`File upload limit reached for user ${userId}. Current: ${fileCheck.currentCount}, Limit: ${fileCheck.totalLimit}`);
      ResponseUtil.badRequest(res, `Daily file upload limit reached. You can upload up to ${fileCheck.totalLimit} files per day. Current: ${fileCheck.currentCount}`);
      return;
    }

    // Store file check result for later consumption
    req.fileUploadCheck = fileCheck;
    
    next();
  } catch (error) {
    logger.error('Error checking file upload limits:', error);
    ResponseUtil.serverError(res, 'Failed to check file upload limits');
  }
};

/**
 * Middleware to consume file upload limit after successful file processing
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const consumeFileUploadLimit = async (req, res, next) => {
  try {
    // Skip consumption for guest users
    if (!req.user) {
      return next();
    }

    // Only consume if a file was uploaded and processed
    if (!req.file || !req.fileUploadCheck) {
      return next();
    }

    const userId = req.user.userId;
    
    // Consume file upload limit
    const consumeResult = await AddonService.consumeFileLimit(userId, 1);
    
    if (!consumeResult.success) {
      logger.error(`Failed to consume file upload limit for user ${userId}`);
      // Don't fail the request, just log the error
    } else {
      logger.info(`Consumed file upload limit for user ${userId}. Consumed from addon: ${consumeResult.consumedFromAddon}`);
    }
    
    next();
  } catch (error) {
    logger.error('Error consuming file upload limit:', error);
    // Don't fail the request, just log the error
    next();
  }
};
