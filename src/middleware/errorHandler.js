import { ResponseUtil  } from '../utils/response.js';
import { ERROR_MESSAGES, HTTP_STATUS  } from '../utils/constants.js';
import logger from '../config/logger.js';
import { DatabaseManager } from '../config/database.js';

/**
 * Custom error class for application errors
 */
class AppError extends Error {
  constructor(message, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Global error handling middleware
 * @param {Error|AppError} error - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const globalErrorHandler = (error, req, res, next) => {
  let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
  let message = ERROR_MESSAGES.INTERNAL_ERROR;
  let errorDetails;

  // Handle different types of errors
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  } else if (error.name === 'ValidationError') {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    message = ERROR_MESSAGES.VALIDATION_ERROR;
    errorDetails = error.message;
  } else if (error.name === 'CastError') {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    message = 'Invalid data format';
  } else if (error.code === 11000) {
    statusCode = HTTP_STATUS.CONFLICT;
    message = 'Duplicate field value';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = HTTP_STATUS.UNAUTHORIZED;
    message = ERROR_MESSAGES.UNAUTHORIZED;
  } else if (error.name === 'TokenExpiredError') {
    statusCode = HTTP_STATUS.UNAUTHORIZED;
    message = 'Token has expired';
  } else if (error.name?.startsWith('Sequelize')) {
    const dbError = handleDatabaseError(error);
    statusCode = dbError.statusCode;
    message = dbError.message;
  }

  // Log error details
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    statusCode,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId,
  });

  // Send error response
  ResponseUtil.error(res, message, errorDetails, statusCode);
};

/**
 * Handle 404 errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const notFoundHandler = (req, res, next) => {
  const error = new AppError(`Route ${req.originalUrl} not found`, HTTP_STATUS.NOT_FOUND);
  next(error);
};

/**
 * Async handler wrapper to catch async errors
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Database error handler
 * @param {Object} error - Database error object
 * @returns {AppError} Formatted application error
 */
const handleDatabaseError = (error) => {
  if (error.name === 'SequelizeValidationError') {
    const message = error.errors.map((e) => e.message).join(', ');
    return new AppError(message, HTTP_STATUS.BAD_REQUEST);
  }

  if (error.name === 'SequelizeUniqueConstraintError') {
    const field = error.errors[0]?.path || 'field';
    return new AppError(`${field} already exists`, HTTP_STATUS.CONFLICT);
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return new AppError('Invalid reference to related resource', HTTP_STATUS.BAD_REQUEST);
  }

  if (error.name === 'SequelizeConnectionError') {
    return new AppError('Database connection failed', HTTP_STATUS.INTERNAL_SERVER_ERROR);
  }

  return new AppError('Database operation failed', HTTP_STATUS.INTERNAL_SERVER_ERROR);
};

/**
 * Authentication error handler
 * @param {Object} error - Authentication error object
 * @returns {AppError} Formatted application error
 */
const handleAuthError = (error) => {
  if (error.name === 'JsonWebTokenError') {
    return new AppError(ERROR_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED);
  }

  if (error.name === 'TokenExpiredError') {
    return new AppError('Token has expired', HTTP_STATUS.UNAUTHORIZED);
  }

  return new AppError(ERROR_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED);
};

/**
 * Validation error handler
 * @param {Object} error - Validation error object
 * @returns {AppError} Formatted application error
 */
const handleValidationError = (error) => {
  if (error.name === 'ValidationError') {
    const message = Object.values(error.errors).map(e => e.message).join(', ');
    return new AppError(message, HTTP_STATUS.BAD_REQUEST);
  }

  return new AppError(ERROR_MESSAGES.VALIDATION_ERROR, HTTP_STATUS.BAD_REQUEST);
};

/**
 * LLM service error handler
 * @param {Object} error - LLM service error object
 * @returns {AppError} Formatted application error
 */
const handleLLMError = (error) => {
  logger.error('LLM service error:', error);

  if (error.message?.includes('API key')) {
    return new AppError('LLM service configuration error', HTTP_STATUS.INTERNAL_SERVER_ERROR);
  }

  if (error.message?.includes('rate limit')) {
    return new AppError('LLM service rate limit exceeded', HTTP_STATUS.TOO_MANY_REQUESTS);
  }

  if (error.message?.includes('timeout')) {
    return new AppError('LLM service timeout', HTTP_STATUS.REQUEST_TIMEOUT);
  }

  return new AppError(ERROR_MESSAGES.LLM_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
};

/**
 * Process uncaught exceptions
 */
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

/**
 * Process unhandled promise rejections
 */
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

/**
 * Graceful shutdown handler
 * @param {Object} server - Express server instance
 */
const gracefulShutdown = (server) => {
  const shutdown = async (signal) => {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);

    // Stop accepting new connections
    server.close(async (err) => {
      if (err) {
        logger.error('Error during server shutdown:', err);
        process.exit(1);
      }

      logger.info('HTTP server closed');

      try {
        // Close database connections
        logger.info('Closing database connections...');
        await DatabaseManager.closeAllConnections();

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    });

    // Force shutdown after 30 seconds
    setTimeout(() => {
      logger.error('Forced shutdown after timeout');
      process.exit(1);
    }, 30000);
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon restarts
};

export { AppError,
  globalErrorHandler,
  notFoundHandler,
  asyncHandler,
  handleDatabaseError,
  handleAuthError,
  handleValidationError,
  handleLLMError,
  gracefulShutdown
 };
