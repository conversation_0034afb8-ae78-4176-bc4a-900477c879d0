import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { DatabaseManager } from './config/database.js';
import { LLMService } from './services/LLMService.js';
import { PineconeService } from './services/PineconeService.js';
import { WebSearchService } from './services/WebSearchService.js';
import { OTPService } from './services/OTPService.js';
import { EmailService } from './services/EmailService.js';
import { S3Service } from './services/S3Service.js';
import { RazorpayService } from './services/RazorpayService.js';
import { SubscriptionService } from './services/SubscriptionService.js';
import { InvoiceService } from './services/InvoiceService.js';
import logger from './config/logger.js';
import routes from './routes/index.js';
import { globalError<PERSON><PERSON><PERSON>, not<PERSON>ound<PERSON><PERSON><PERSON>, gracefulShutdown } from './middleware/errorHandler.js';
import { securityHeaders, sanitizeInput, RateLimiter } from './middleware/security.js';

// Load environment variables
dotenv.config();

/**
 * Main Application Class
 * Handles Express server setup, middleware configuration, and service initialization
 */
class App {
  /**
   * Initialize the App instance
   */
  constructor() {
    /** @type {express.Application} */
    this.app = express();
    /** @type {any} */
    this.server = null;
    
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  /**
   * Initialize all middleware
   * @private
   */
  initializeMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Session-ID', 'X-CSRF-Token'],
    }));

    // Rate limiting - More generous limits for development
    this.app.use(RateLimiter.limit(1000, 15 * 60 * 1000)); // 1000 requests per 15 minutes

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static file serving for assets (logos, etc.)
    this.app.use('/assets', express.static('public/assets', {
      maxAge: '1d', // Cache for 1 day
      etag: true,
      lastModified: true,
      setHeaders: (res, path) => {
        // Set CORS headers for assets
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

        // Set appropriate content type for different file types
        if (path.endsWith('.webp')) {
          res.setHeader('Content-Type', 'image/webp');
        } else if (path.endsWith('.png')) {
          res.setHeader('Content-Type', 'image/png');
        } else if (path.endsWith('.jpg') || path.endsWith('.jpeg')) {
          res.setHeader('Content-Type', 'image/jpeg');
        } else if (path.endsWith('.svg')) {
          res.setHeader('Content-Type', 'image/svg+xml');
        }
      }
    }));

    // Security headers and input sanitization
    this.app.use(securityHeaders);
    this.app.use(sanitizeInput);

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.originalUrl}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString(),
        userId: req.user?.userId,
        requestBody: req.body,
      });
      next();
    });
  }

  /**
   * Initialize all routes
   * @private
   */
  initializeRoutes() {
    // API routes
    this.app.use('/api', routes);

    // Root endpoint
    this.app.get('/', (req, res) => {
      res.json({
        message: 'The Infini AI Backend API',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/api/health',
          version: '/api/version',
        },
      });
    });
  }

  /**
   * Initialize error handling middleware
   * @private
   */
  initializeErrorHandling() {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(globalErrorHandler);
  }

  /**
   * Start the application server
   * @returns {Promise<void>}
   */
  async start() {
    try {
      // Load models to ensure associations are set up
      logger.info('Loading models and associations...');
      await import('./models/user/index.js');
      await import('./models/chat/index.js');
      await import('./models/subscription/index.js');
      await import('./models/support/index.js');

      // Initialize databases
      logger.info('Connecting to databases...');
      await DatabaseManager.connectAllDatabases();

      // Sync database schemas
      logger.info('Synchronizing database schemas...');
      await DatabaseManager.syncAllDatabases(false); // Set to true to force recreate tables

      // Initialize LLM service
      logger.info('Initializing LLM service...');
      LLMService.initialize();

      // Initialize Pinecone service
      logger.info('Initializing Pinecone service...');
      await PineconeService.initialize();

      // Initialize Web Search service
      logger.info('Initializing Web Search service...');
      await WebSearchService.initialize();

      // Initialize Email service
      logger.info('Initializing Email service...');
      await EmailService.initialize();

      // Initialize S3 service
      logger.info('Initializing S3 service...');
      await S3Service.initialize();

      // Initialize Invoice service
      logger.info('Initializing Invoice service...');
      await InvoiceService.initialize();

      // Initialize Razorpay service
      logger.info('Initializing Razorpay service...');
      RazorpayService.initialize();

      // Initialize subscription plans
      logger.info('Initializing subscription plans...');
      await SubscriptionService.initializeDefaultPlans();

      // Start OTP cleanup scheduler
      logger.info('Starting OTP cleanup scheduler...');
      OTPService.scheduleCleanup();

      // Start server
      const port = process.env.PORT || 5529;
      this.server = this.app.listen(port, () => {
        logger.info(`Server is running on port ${port}`);
        logger.info(`Application: http://localhost:${port}`);
        logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
        logger.info('The Infini AI Backend started successfully!');
      });

      // Setup graceful shutdown
      gracefulShutdown(this.server);

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * Get the Express application instance
   * @returns {express.Application}
   */
  getApp() {
    return this.app;
  }
}

// Start the application
const app = new App();
app.start().catch((error) => {
  logger.error('Application startup failed:', error);
  process.exit(1);
});

export default app;
