import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Logo Utilities for PDF Generation
 * Handles logo conversion and embedding for invoices
 */
export class LogoUtils {
  static logoSvgPath = path.join(process.cwd(), 'public', 'assets', 'infini-logo.svg');
  static logoPngPath = path.join(process.cwd(), 'public', 'assets', 'infini-logo.png');
  static cachedLogoData = null;
  static cachedPngLogoData = null;

  /**
   * Get logo as base64 data URL for embedding in PDFs
   * @returns {Promise<string>} Base64 data URL
   */
  static async getLogoAsBase64() {
    try {
      if (this.cachedLogoData) {
        return this.cachedLogoData;
      }

      const logoSvg = await fs.readFile(this.logoSvgPath, 'utf8');
      
      // Convert SVG to base64
      const base64Logo = Buffer.from(logoSvg).toString('base64');
      this.cachedLogoData = `data:image/svg+xml;base64,${base64Logo}`;
      
      return this.cachedLogoData;
    } catch (error) {
      console.warn('Could not load logo:', error.message);
      return null;
    }
  }

  /**
   * Get logo SVG content for direct embedding
   * @returns {Promise<string>} SVG content
   */
  static async getLogoSvg() {
    try {
      return await fs.readFile(this.logoSvgPath, 'utf8');
    } catch (error) {
      console.warn('Could not load logo SVG:', error.message);
      return null;
    }
  }

  /**
   * Get PNG logo as base64 data URL for embedding in HTML/PDF
   * @returns {Promise<string>} Base64 data URL
   */
  static async getPngLogoAsBase64() {
    try {
      if (this.cachedPngLogoData) {
        return this.cachedPngLogoData;
      }

      const logoBuffer = await fs.readFile(this.logoPngPath);

      // Convert PNG to base64
      const base64Logo = logoBuffer.toString('base64');
      this.cachedPngLogoData = `data:image/png;base64,${base64Logo}`;

      return this.cachedPngLogoData;
    } catch (error) {
      console.warn('Could not load PNG logo:', error.message);
      return null;
    }
  }

  /**
   * Get company branding colors
   * @returns {Object} Color scheme
   */
  static getBrandColors() {
    return {
      primary: '#FCD469', // Yellow from logo
      secondary: '#38a169', // Green from email templates
      dark: '#2d3748',
      light: '#f7fafc',
      text: '#4a5568',
      accent: '#2f855a'
    };
  }

  /**
   * Get logo dimensions for PDF layout
   * @returns {Object} Logo dimensions
   */
  static getLogoDimensions() {
    return {
      width: 101,
      height: 51,
      aspectRatio: 101 / 51
    };
  }
}
