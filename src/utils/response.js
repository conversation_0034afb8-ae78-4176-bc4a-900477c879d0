import { HTTP_STATUS  } from './constants.js';

/**
 * Response Utility Class
 * Provides standardized response methods for API endpoints
 */
class ResponseUtil {
  /**
   * Send a successful response
   * @param {Object} res - Express response object
   * @param {string} message - Success message
   * @param {*} [data] - Response data (optional)
   * @param {number} [statusCode=200] - HTTP status code
   * @returns {Object} Express response
   */
  static success(res, message, data, statusCode = HTTP_STATUS.OK) {
    return res.status(statusCode).json({
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send an error response
   * @param {Object} res - Express response object
   * @param {string} message - Error message
   * @param {string} [error] - Detailed error information (optional)
   * @param {number} [statusCode=500] - HTTP status code
   * @returns {Object} Express response
   */
  static error(res, message, error, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR) {
    return res.status(statusCode).json({
      success: false,
      message,
      error,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send a validation error response
   * @param {Object} res - Express response object
   * @param {string} message - Validation error message
   * @param {string} [error] - Detailed error information (optional)
   * @returns {Object} Express response
   */
  static validationError(res, message, error) {
    return this.error(res, message, error, HTTP_STATUS.BAD_REQUEST);
  }

  /**
   * Send a bad request response
   * @param {Object} res - Express response object
   * @param {string} message - Bad request message
   * @param {string} [error] - Detailed error information (optional)
   * @returns {Object} Express response
   */
  static badRequest(res, message, error) {
    return this.error(res, message, error, HTTP_STATUS.BAD_REQUEST);
  }

  /**
   * Send an unauthorized response
   * @param {Object} res - Express response object
   * @param {string} [message='Unauthorized'] - Unauthorized message
   * @returns {Object} Express response
   */
  static unauthorized(res, message = 'Unauthorized') {
    return this.error(res, message, undefined, HTTP_STATUS.UNAUTHORIZED);
  }

  /**
   * Send a forbidden response
   * @param {Object} res - Express response object
   * @param {string} [message='Forbidden'] - Forbidden message
   * @returns {Object} Express response
   */
  static forbidden(res, message = 'Forbidden') {
    return this.error(res, message, undefined, HTTP_STATUS.FORBIDDEN);
  }

  /**
   * Send a not found response
   * @param {Object} res - Express response object
   * @param {string} [message='Not Found'] - Not found message
   * @returns {Object} Express response
   */
  static notFound(res, message = 'Not Found') {
    return this.error(res, message, undefined, HTTP_STATUS.NOT_FOUND);
  }

  /**
   * Send a conflict response
   * @param {Object} res - Express response object
   * @param {string} message - Conflict message
   * @returns {Object} Express response
   */
  static conflict(res, message) {
    return this.error(res, message, undefined, HTTP_STATUS.CONFLICT);
  }

  /**
   * Send a created response
   * @param {Object} res - Express response object
   * @param {string} message - Success message
   * @param {*} [data] - Response data (optional)
   * @returns {Object} Express response
   */
  static created(res, message, data) {
    return this.success(res, message, data, HTTP_STATUS.CREATED);
  }

  /**
   * Send a no content response
   * @param {Object} res - Express response object
   * @returns {Object} Express response
   */
  static noContent(res) {
    return res.status(204).send();
  }

  /**
   * Send a rate limit exceeded response
   * @param {Object} res - Express response object
   * @param {string} [message='Rate limit exceeded'] - Rate limit message
   * @returns {Object} Express response
   */
  static rateLimitExceeded(res, message = 'Rate limit exceeded') {
    return this.error(res, message, undefined, HTTP_STATUS.TOO_MANY_REQUESTS);
  }

  /**
   * Send a server-sent events response
   * @param {Object} res - Express response object
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  static sendSSE(res, event, data) {
    res.write(`event: ${event}\n`);
    res.write(`data: ${JSON.stringify(data)}\n\n`);
  }

  /**
   * Initialize server-sent events response
   * @param {Object} res - Express response object
   */
  static initSSE(res) {
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });
  }
}

export { ResponseUtil  };
