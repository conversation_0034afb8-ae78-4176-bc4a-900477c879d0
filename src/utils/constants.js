/**
 * @fileoverview Application constants and configuration values
 */

/**
 * HTTP Status Codes
 * @readonly
 * @enum {number}
 */
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  REQUEST_TIMEOUT: 408,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
};

/**
 * Error Messages
 * @readonly
 * @enum {string}
 */
const ERROR_MESSAGES = {
  INVALID_CREDENTIALS: 'Invalid credentials provided',
  USER_NOT_FOUND: 'User not found',
  USER_ALREADY_EXISTS: 'User already exists',
  INVALID_OTP: 'Invalid or expired OTP',
  OTP_EXPIRED: 'O<PERSON> has expired',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  VALIDATION_ERROR: 'Validation error',
  INTERNAL_ERROR: 'Internal server error',
  GUEST_LIMIT_EXCEEDED: 'Sign up to continue',
  GUEST_IP_ALREADY_EXISTS: `Please sign up for unlimited access!🚀`,
  CHAT_NOT_FOUND: 'Chat not found',
  MESSAGE_NOT_FOUND: 'Message not found',
  INVALID_SESSION: 'Invalid session',
  LLM_ERROR: 'Error processing your request',
  INSUFFICIENT_CREDITS: 'Insufficient credits. Please purchase more credits to continue chatting.',
  CREDIT_DEDUCTION_FAILED: 'Failed to deduct credits',
};

/**
 * Success Messages
 * @readonly
 * @enum {string}
 */
const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful',
  OTP_SENT: 'OTP sent successfully',
  OTP_VERIFIED: 'OTP verified successfully',
  CHAT_CREATED: 'Chat created successfully',
  MESSAGE_SENT: 'Message sent successfully',
  USER_CREATED: 'User created successfully',
  REGISTRATION_INITIATED: 'Registration initiated. Please verify your account.',
  REGISTRATION_VERIFIED: 'Registration completed successfully',
};

/**
 * Validation Rules and Patterns
 * @readonly
 */
const VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  MOBILE_REGEX: /^[+]?[1-9][\d]{7,14}$/,
  PASSWORD_REGEX: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  PASSWORD_MIN_LENGTH: 8,
  OTP_LENGTH: 6,
  OTP_EXPIRY_MINUTES: 5,
  GUEST_CHAT_LIMIT: 5,
};

/**
 * Credit System Configuration
 * @readonly
 */
const CREDIT_SYSTEM = {
  INITIAL_CREDITS: 50,
  CHAT_MESSAGE_COST: 1,
  WEB_SEARCH_COST: 3,
  TRANSACTION_TYPES: {
    CREDIT: 'CREDIT',
    DEBIT: 'DEBIT',
  },
  DESCRIPTIONS: {
    INITIAL_SIGNUP: 'Initial signup bonus',
    CHAT_MESSAGE: 'Chat message',
    WEB_SEARCH: 'Web search with AI analysis',
    ADMIN_CREDIT: 'Admin credit adjustment',
    PURCHASE: 'Credit purchase',
  },
};

/**
 * User Plans
 * @readonly
 * @enum {string}
 */
const USER_PLANS = {
  EXPLORER: 'EXPLORER',
  CREATOR: 'CREATOR',
  PRO: 'PRO',
  ADDON: 'ADDON',
};

/**
 * LLM Models Configuration
 * @readonly
 */
const LLM_MODELS = {
  OPENAI: {
    GPT_4O: 'gpt-4o',
    GPT_4O_MINI: 'gpt-4o-mini',
    GPT_4_TURBO: 'gpt-4-turbo',
    GPT_3_5_TURBO: 'gpt-3.5-turbo',
    GPT_4_VISION_PREVIEW: 'gpt-4o-mini', // Legacy mapping
  },
  ANTHROPIC: {
    CLAUDE_4_OPUS: 'claude-opus-4-20250514',
    CLAUDE_4_SONNET: 'claude-sonnet-4-20250514',
    CLAUDE_3_7_SONNET: 'claude-3-7-sonnet-20250219',
    CLAUDE_3_5_SONNET: 'claude-3-5-sonnet-20241022',
    CLAUDE_3_5_HAIKU: 'claude-3-5-haiku-20241022',
    CLAUDE_3_HAIKU: 'claude-3-haiku-20240307',
  },
  GOOGLE: {
    GEMINI_2_5_PRO: 'gemini-2.5-pro',
    GEMINI_2_5_FLASH: 'gemini-2.5-flash',
    GEMINI_2_0_FLASH: 'gemini-2.0-flash',
    GEMINI_1_5_PRO: 'gemini-1.5-pro',
    GEMINI_1_5_FLASH: 'gemini-1.5-flash',
  },
  DEEPSEEK: {
    DEEPSEEK_R1: 'deepseek-reasoner',
    DEEPSEEK_V3: 'deepseek-chat',
  },
  META: {
    LLAMA_3_3_70B: 'llama-3.3-70b-instruct',
    LLAMA_3_2_90B_VISION: 'llama-3.2-90b-vision-instruct',
    LLAMA_3_2_11B_VISION: 'llama-3.2-11b-vision-instruct',
    LLAMA_3_1_405B: 'llama-3.1-405b-instruct',
    LLAMA_3_1_70B: 'llama-3.1-70b-instruct',
    LLAMA_3_1_8B: 'llama-3.1-8b-instruct',
  },
};

/**
 * Default LLM Configuration
 * @readonly
 */
const DEFAULT_LLM_CONFIG = {
  temperature: 0.7,
  maxTokens: 1000,
};

/**
 * Subscription System Configuration
 * @readonly
 */
const SUBSCRIPTION_SYSTEM = {
  PLANS: {
    EXPLORER: {
      type: 'EXPLORER',
      name: 'Explorer Plan',
      price: 0,
      credits: 30,
      billingCycle: 'WEEKLY',
      limits: {
        projects: 3,
        filesPerDay: 3,
        models: ['gpt-3.5-turbo', 'claude-3-haiku-20240307'],
      },
    },
    CREATOR: {
      type: 'CREATOR',
      name: 'Creator Plan',
      price: 1299,
      credits: 1500,
      billingCycle: 'MONTHLY',
      limits: {
        projects: 20,
        filesPerDay: 20,
        models: ['gpt-4o', 'gpt-4o-mini', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229'],
      },
    },
    PRO: {
      type: 'PRO',
      name: 'Pro Plan',
      price: 1899,
      credits: -1, // Unlimited
      billingCycle: 'MONTHLY',
      limits: {
        projects: 100,
        filesPerDay: 50,
        models: ['all'],
      },
    },
    ADDON: {
      type: 'ADDON',
      name: 'Infini Add Pack',
      price: 120,
      credits: 100,
      billingCycle: 'ONE_TIME',
      limits: {
        projects: 2, // Additional projects
        filesPerDay: 10, // Additional files
      },
    },
  },
  GRACE_PERIOD_DAYS: 3,
  PAYMENT_RETRY_ATTEMPTS: 3,
  WEBHOOK_RETRY_ATTEMPTS: 5,
};

/**
 * Payment System Configuration
 * @readonly
 */
const PAYMENT_SYSTEM = {
  CURRENCY: 'INR',
  TRANSACTION_TYPES: {
    SUBSCRIPTION: 'SUBSCRIPTION',
    ADDON: 'ADDON',
    RENEWAL: 'RENEWAL',
    UPGRADE: 'UPGRADE',
    DOWNGRADE: 'DOWNGRADE',
  },
  STATUS: {
    PENDING: 'PENDING',
    SUCCESS: 'SUCCESS',
    FAILED: 'FAILED',
    REFUNDED: 'REFUNDED',
  },
};

/**
 * Web Search System Configuration
 * @readonly
 */
const WEB_SEARCH_SYSTEM = {
  MAX_SEARCH_RESULTS: 3,
  MAX_CONTENT_LENGTH: 5000, // Max characters per search result
  CONTENT_EXTRACTION_TIMEOUT: 10000, // 10 seconds
  SEARCH_NAMESPACE_PREFIX: 'websearch',
  CHUNK_SIZE: 1000,
  SYSTEM_PROMPT: `You are an AI assistant with access to real-time web search results.
Use the provided search results to answer the user's question accurately and comprehensively.
Always cite your sources by mentioning the website names or URLs when referencing information from the search results.
If the search results don't contain enough information to answer the question, say so clearly.`,
};

export {
  HTTP_STATUS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  VALIDATION_RULES,
  CREDIT_SYSTEM,
  USER_PLANS,
  LLM_MODELS,
  DEFAULT_LLM_CONFIG,
  SUBSCRIPTION_SYSTEM,
  PAYMENT_SYSTEM,
  WEB_SEARCH_SYSTEM,
};
