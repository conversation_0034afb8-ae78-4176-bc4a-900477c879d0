import bcrypt from 'bcryptjs';
import crypto from 'crypto';

/**
 * Encryption and Security Utility Class
 * Handles password hashing, OTP generation, and other security-related operations
 */
class EncryptionUtil {
  static SALT_ROUNDS = 12;

  /**
   * Hash a password using bcrypt
   * @param {string} password - Plain text password
   * @returns {Promise<string>} Hashed password
   */
  static async hashPassword(password) {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  /**
   * Compare a plain password with a hashed password
   * @param {string} password - Plain text password
   * @param {string} hashedPassword - Hashed password
   * @returns {Promise<boolean>} True if passwords match
   */
  static async comparePassword(password, hashedPassword) {
    return bcrypt.compare(password, hashedPassword);
  }

  /**
   * Generate a random OTP
   * @param {number} length - Length of OTP (default: 6)
   * @returns {string} Generated OTP
   */
  static generateOTP(length = 6) {
    const digits = '0123456789';
    let otp = '';
    
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * digits.length)];
    }
    
    return otp;
  }

  /**
   * Generate a random session ID
   * @returns {string} Generated session ID
   */
  static generateSessionId() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate a random UUID-like string
   * @returns {string} Generated UUID
   */
  static generateUUID() {
    return crypto.randomUUID();
  }

  /**
   * Generate a secure random string
   * @param {number} length - Length of random string (default: 32)
   * @returns {string} Generated random string
   */
  static generateRandomString(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate a secure random token for CSRF protection
   * @returns {string} Generated CSRF token
   */
  static generateCSRFToken() {
    return crypto.randomBytes(32).toString('base64');
  }

  /**
   * Hash data using SHA-256
   * @param {string} data - Data to hash
   * @returns {string} SHA-256 hash
   */
  static hashSHA256(data) {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * Generate a secure API key
   * @param {number} length - Length of API key (default: 64)
   * @returns {string} Generated API key
   */
  static generateAPIKey(length = 64) {
    return crypto.randomBytes(length).toString('base64url');
  }

  /**
   * Validate password strength
   * @param {string} password - Password to validate
   * @returns {Object} Validation result with score and feedback
   */
  static validatePasswordStrength(password) {
    const result = {
      score: 0,
      feedback: [],
      isValid: false
    };

    if (password.length >= 8) {
      result.score += 1;
    } else {
      result.feedback.push('Password must be at least 8 characters long');
    }

    if (/[a-z]/.test(password)) {
      result.score += 1;
    } else {
      result.feedback.push('Password must contain lowercase letters');
    }

    if (/[A-Z]/.test(password)) {
      result.score += 1;
    } else {
      result.feedback.push('Password must contain uppercase letters');
    }

    if (/\d/.test(password)) {
      result.score += 1;
    } else {
      result.feedback.push('Password must contain numbers');
    }

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      result.score += 1;
    } else {
      result.feedback.push('Password must contain special characters');
    }

    result.isValid = result.score >= 4;
    return result;
  }
}

export { EncryptionUtil };
