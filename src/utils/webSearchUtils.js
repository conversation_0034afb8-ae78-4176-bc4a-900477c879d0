/**
 * @fileoverview Web search utility functions for content extraction and processing
 */

import axios from 'axios';
import * as cheerio from 'cheerio';
import logger from '../config/logger.js';
import { WEB_SEARCH_SYSTEM } from './constants.js';

/**
 * Extract clean text content from HTML
 * @param {string} html - HTML content
 * @param {string} url - Source URL for context
 * @returns {string} Clean text content
 */
export function extractTextFromHtml(html, url) {
  try {
    const $ = cheerio.load(html);
    
    // Remove script, style, nav, footer, and other non-content elements
    $('script, style, nav, footer, header, aside, .advertisement, .ads, .sidebar, .menu').remove();
    
    // Focus on main content areas
    let content = '';
    const contentSelectors = [
      'main',
      'article', 
      '.content',
      '.main-content',
      '.post-content',
      '.entry-content',
      '#content',
      '.article-body',
      '.story-body'
    ];
    
    // Try to find main content area
    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length > 0 && element.text().trim().length > 100) {
        content = element.text();
        break;
      }
    }
    
    // Fallback to body if no main content found
    if (!content || content.trim().length < 100) {
      content = $('body').text();
    }
    
    // Clean up the text
    content = content
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/\n+/g, '\n') // Replace multiple newlines with single newline
      .trim();
    
    // Limit content length
    if (content.length > WEB_SEARCH_SYSTEM.MAX_CONTENT_LENGTH) {
      content = content.substring(0, WEB_SEARCH_SYSTEM.MAX_CONTENT_LENGTH) + '...';
    }
    
    logger.debug(`Extracted ${content.length} characters from ${url}`);
    return content;
  } catch (error) {
    logger.error(`Error extracting text from HTML for ${url}:`, error);
    return '';
  }
}

/**
 * Fetch and extract content from a URL
 * @param {string} url - URL to fetch
 * @returns {Promise<Object>} Extracted content with metadata
 */
export async function fetchAndExtractContent(url) {
  try {
    logger.debug(`Fetching content from: ${url}`);
    
    const response = await axios.get(url, {
      timeout: WEB_SEARCH_SYSTEM.CONTENT_EXTRACTION_TIMEOUT,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; InfiniAI-Bot/1.0; +https://theinfiniai.live)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
      },
      maxRedirects: 3,
      validateStatus: (status) => status < 400, // Accept any status less than 400
    });
    
    const content = extractTextFromHtml(response.data, url);
    
    if (!content || content.trim().length < 50) {
      logger.warn(`Insufficient content extracted from ${url}`);
      return null;
    }
    
    return {
      url,
      content,
      title: extractTitleFromHtml(response.data),
      contentLength: content.length,
      extractedAt: new Date().toISOString(),
    };
  } catch (error) {
    logger.error(`Error fetching content from ${url}:`, error.message);
    return null;
  }
}

/**
 * Extract title from HTML
 * @param {string} html - HTML content
 * @returns {string} Page title
 */
function extractTitleFromHtml(html) {
  try {
    const $ = cheerio.load(html);
    
    // Try different title sources in order of preference
    const titleSelectors = [
      'meta[property="og:title"]',
      'meta[name="twitter:title"]',
      'title',
      'h1',
    ];
    
    for (const selector of titleSelectors) {
      const element = $(selector);
      if (element.length > 0) {
        const title = selector.includes('meta') 
          ? element.attr('content') 
          : element.text();
        
        if (title && title.trim().length > 0) {
          return title.trim().substring(0, 200); // Limit title length
        }
      }
    }
    
    return 'Untitled';
  } catch (error) {
    logger.error('Error extracting title from HTML:', error);
    return 'Untitled';
  }
}

/**
 * Process search results and extract content from URLs
 * @param {Array} searchResults - Array of search results from Tavily
 * @returns {Promise<Array>} Array of processed results with extracted content
 */
export async function processSearchResults(searchResults) {
  if (!searchResults || !Array.isArray(searchResults)) {
    logger.warn('Invalid search results provided to processSearchResults');
    return [];
  }
  
  logger.info(`Processing ${searchResults.length} search results for content extraction`);
  
  const processedResults = [];
  const contentExtractionPromises = searchResults.map(async (result, index) => {
    try {
      // Extract basic info from search result
      const basicInfo = {
        title: result.title || 'Untitled',
        url: result.url,
        snippet: result.content || result.snippet || '',
        score: result.score || 0,
        publishedDate: result.published_date || null,
      };
      
      // Fetch and extract full content
      const extractedContent = await fetchAndExtractContent(result.url);
      
      if (extractedContent) {
        return {
          ...basicInfo,
          fullContent: extractedContent.content,
          extractedTitle: extractedContent.title,
          contentLength: extractedContent.contentLength,
          extractedAt: extractedContent.extractedAt,
          hasFullContent: true,
        };
      } else {
        // Fallback to snippet if content extraction fails
        return {
          ...basicInfo,
          fullContent: basicInfo.snippet,
          extractedTitle: basicInfo.title,
          contentLength: basicInfo.snippet.length,
          extractedAt: new Date().toISOString(),
          hasFullContent: false,
        };
      }
    } catch (error) {
      logger.error(`Error processing search result ${index}:`, error);
      return null;
    }
  });
  
  const results = await Promise.allSettled(contentExtractionPromises);
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled' && result.value) {
      processedResults.push(result.value);
    } else {
      logger.warn(`Failed to process search result ${index}:`, result.reason);
    }
  });
  
  logger.info(`Successfully processed ${processedResults.length} out of ${searchResults.length} search results`);
  return processedResults;
}

/**
 * Format search results for LLM context
 * @param {Array} processedResults - Processed search results
 * @returns {string} Formatted context string
 */
export function formatSearchResultsForLLM(processedResults) {
  if (!processedResults || processedResults.length === 0) {
    return 'No search results available.';
  }
  
  let formattedContext = 'Here are the search results I found:\n\n';
  
  processedResults.forEach((result, index) => {
    formattedContext += `**Source ${index + 1}: ${result.extractedTitle}**\n`;
    formattedContext += `URL: ${result.url}\n`;
    if (result.publishedDate) {
      formattedContext += `Published: ${result.publishedDate}\n`;
    }
    formattedContext += `Content: ${result.fullContent}\n\n`;
    formattedContext += '---\n\n';
  });
  
  return formattedContext;
}

/**
 * Generate a unique namespace for web search results
 * @param {string} sessionId - User session ID
 * @param {string} query - Search query
 * @returns {string} Unique namespace
 */
export function generateWebSearchNamespace(sessionId, query) {
  const queryHash = Buffer.from(query).toString('base64').substring(0, 8);
  return `${WEB_SEARCH_SYSTEM.SEARCH_NAMESPACE_PREFIX}_${sessionId}_${queryHash}`;
}

/**
 * Validate search query
 * @param {string} query - Search query to validate
 * @returns {Object} Validation result
 */
export function validateSearchQuery(query) {
  if (!query || typeof query !== 'string') {
    return {
      isValid: false,
      error: 'Query must be a non-empty string',
    };
  }
  
  const trimmedQuery = query.trim();
  
  if (trimmedQuery.length < 3) {
    return {
      isValid: false,
      error: 'Query must be at least 3 characters long',
    };
  }
  
  if (trimmedQuery.length > 500) {
    return {
      isValid: false,
      error: 'Query must be less than 500 characters',
    };
  }
  
  return {
    isValid: true,
    query: trimmedQuery,
  };
}
