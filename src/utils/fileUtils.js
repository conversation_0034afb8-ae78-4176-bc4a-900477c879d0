/**
 * File utility functions for handling different file types
 */

/**
 * Check if file is a code file based on extension
 * @param {string} filename - Original filename
 * @returns {boolean} Whether file is a code file
 */
export const isCodeFile = (filename) => {
  const codeExtensions = [
    // JavaScript/TypeScript
    '.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs',
    // Python
    '.py', '.pyw', '.pyc', '.pyo', '.pyd',
    // Java
    '.java', '.class', '.jar',
    // C/C++
    '.c', '.cpp', '.cxx', '.cc', '.h', '.hpp', '.hxx',
    // C#/.NET
    '.cs', '.vb', '.fs', '.fsx',
    // PHP
    '.php', '.php3', '.php4', '.php5', '.phtml',
    // Ruby
    '.rb', '.rbw', '.gem',
    // Go
    '.go', '.mod', '.sum',
    // Rust
    '.rs', '.rlib',
    // Swift
    '.swift',
    // Kotlin
    '.kt', '.kts',
    // Scala
    '.scala', '.sc',
    // R
    '.r', '.R', '.rmd',
    // Objective-C
    '.m', '.mm',
    // Perl
    '.pl', '.pm', '.t', '.pod',
    // Shell scripts
    '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh',
    // PowerShell
    '.ps1', '.psm1', '.psd1',
    // Batch
    '.bat', '.cmd',
    // SQL
    '.sql', '.mysql', '.pgsql', '.sqlite',
    // Web
    '.html', '.htm', '.xhtml',
    '.css', '.scss', '.sass', '.less', '.styl',
    // Markup/Data
    '.xml', '.xsl', '.xslt', '.dtd', '.xsd',
    '.json', '.jsonl', '.json5',
    '.yaml', '.yml',
    '.toml', '.ini', '.cfg', '.conf',
    '.md', '.markdown', '.mdown', '.mkd',
    '.txt', '.text', '.log', '.csv',
    // DevOps/Config
    '.dockerfile', '.dockerignore',
    '.gitignore', '.gitattributes',
    '.env', '.env.local', '.env.development', '.env.production',
    '.makefile', '.cmake', '.gradle',
    // Frontend frameworks
    '.vue', '.svelte',
    // Other languages
    '.dart', '.lua', '.nim', '.zig', '.v', '.elm', '.clj', '.cljs', '.edn',
    '.ex', '.exs', '.erl', '.hrl', '.hs', '.lhs', '.ml', '.mli', '.ocaml'
  ];
  
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
  return codeExtensions.includes(extension) || 
         filename.toLowerCase().includes('makefile') ||
         filename.toLowerCase().includes('dockerfile') ||
         filename.toLowerCase().includes('rakefile') ||
         filename.toLowerCase().includes('gemfile') ||
         filename.toLowerCase().includes('podfile');
};

/**
 * Get programming language from file extension
 * @param {string} filename - Original filename
 * @returns {string} Programming language name
 */
export const getLanguageFromExtension = (filename) => {
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
  const languageMap = {
    '.js': 'JavaScript',
    '.jsx': 'JavaScript (React)',
    '.ts': 'TypeScript',
    '.tsx': 'TypeScript (React)',
    '.mjs': 'JavaScript (ES Module)',
    '.cjs': 'JavaScript (CommonJS)',
    '.py': 'Python',
    '.pyw': 'Python',
    '.java': 'Java',
    '.c': 'C',
    '.cpp': 'C++',
    '.cxx': 'C++',
    '.cc': 'C++',
    '.h': 'C/C++ Header',
    '.hpp': 'C++ Header',
    '.cs': 'C#',
    '.vb': 'Visual Basic',
    '.php': 'PHP',
    '.rb': 'Ruby',
    '.go': 'Go',
    '.rs': 'Rust',
    '.swift': 'Swift',
    '.kt': 'Kotlin',
    '.scala': 'Scala',
    '.r': 'R',
    '.sh': 'Shell Script',
    '.bash': 'Bash Script',
    '.ps1': 'PowerShell',
    '.bat': 'Batch Script',
    '.sql': 'SQL',
    '.html': 'HTML',
    '.css': 'CSS',
    '.scss': 'SCSS',
    '.sass': 'Sass',
    '.xml': 'XML',
    '.json': 'JSON',
    '.yaml': 'YAML',
    '.yml': 'YAML',
    '.md': 'Markdown',
    '.vue': 'Vue.js',
    '.svelte': 'Svelte',
    '.dart': 'Dart',
    '.lua': 'Lua'
  };

  // Handle special cases
  const lowerFilename = filename.toLowerCase();
  if (lowerFilename.includes('makefile')) return 'Makefile';
  if (lowerFilename.includes('dockerfile')) return 'Dockerfile';
  if (lowerFilename.includes('rakefile')) return 'Ruby (Rakefile)';
  if (lowerFilename.includes('gemfile')) return 'Ruby (Gemfile)';

  return languageMap[extension] || 'Text';
};

/**
 * Check if file should be processed as plain text
 * @param {string} filename - Original filename
 * @param {string} mimetype - File MIME type
 * @returns {boolean} Whether file should be processed as plain text
 */
export const shouldProcessAsPlainText = (filename, mimetype) => {
  // Always process code files as plain text
  if (isCodeFile(filename)) {
    return true;
  }

  // Process text MIME types as plain text
  const textMimeTypes = [
    'text/plain',
    'text/markdown',
    'application/json',
    'text/javascript',
    'application/javascript',
    'text/css',
    'text/html',
    'application/xml',
    'text/xml',
    'application/yaml',
    'text/yaml'
  ];

  return textMimeTypes.includes(mimetype);
};

/**
 * Detect file encoding and read as text
 * @param {Buffer} buffer - File buffer
 * @param {string} filename - Original filename
 * @returns {string} File content as text
 */
export const readFileAsText = (buffer, filename) => {
  try {
    // Try UTF-8 first
    const content = buffer.toString('utf-8');
    
    // Basic validation - check if content contains mostly printable characters
    const printableChars = content.replace(/[\r\n\t]/g, '').length;
    const totalChars = content.length;
    const printableRatio = printableChars > 0 ? printableChars / totalChars : 0;
    
    // If less than 80% printable characters, it might be binary
    if (printableRatio < 0.8 && totalChars > 100) {
      throw new Error('File appears to be binary');
    }
    
    return content;
  } catch (error) {
    // Fallback: try latin1 encoding
    try {
      return buffer.toString('latin1');
    } catch (fallbackError) {
      throw new Error(`Unable to read file as text: ${error.message}`);
    }
  }
};
