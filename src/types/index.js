/**
 * @fileoverview Type definitions converted to JSDoc for JavaScript
 * This file contains all the type definitions that were previously TypeScript interfaces
 */

/**
 * @typedef {Object} ApiResponse
 * @property {boolean} success - Whether the request was successful
 * @property {string} message - Response message
 * @property {*} [data] - Response data (optional)
 * @property {string} [error] - Error message (optional)
 * @property {string} timestamp - Response timestamp
 */

/**
 * @typedef {Object} LoginRequest
 * @property {string} identifier - Email or mobile number
 * @property {string} [password] - User password (optional)
 * @property {boolean} [requestOtp] - Whether to request OTP (optional)
 */

/**
 * @typedef {Object} OTPVerificationRequest
 * @property {string} identifier - Email or mobile number
 * @property {string} otp - OTP code
 */

/**
 * @typedef {Object} ChatRequest
 * @property {string} message - Chat message content
 * @property {string} [sessionId] - Session ID (optional)
 * @property {string} [llmModel] - LLM model to use (optional)
 */

/**
 * @typedef {Object} SimpleChatRequest
 * @property {string} message - Chat message content
 * @property {string} [sessionId] - Session ID (optional)
 * @property {string} [llmModel] - LLM model to use (optional)
 */

/**
 * @typedef {Object} RegenerateRequest
 * @property {string} messageId - Message ID to regenerate
 * @property {string} [llmModel] - LLM model to use (optional)
 */

/**
 * @typedef {Object} ChatResponse
 * @property {string} response - Chat response content
 * @property {string} sessionId - Session ID
 * @property {string} messageId - Message ID
 * @property {boolean} isGuest - Whether user is guest
 * @property {number} [remainingGuestChats] - Remaining guest chats (optional)
 */

/**
 * @typedef {Object} SimpleChatResponse
 * @property {string} sessionId - Session ID
 * @property {string} messageId - Message ID
 * @property {boolean} isGuest - Whether user is guest
 * @property {string} [userId] - User ID (optional)
 */

/**
 * @typedef {Object} UserAttributes
 * @property {string} id - User ID
 * @property {string} [email] - User email (optional)
 * @property {string} [mobile] - User mobile number (optional)
 * @property {string} [password] - User password (optional)
 * @property {boolean} isActive - Whether user is active
 * @property {boolean} isVerified - Whether user is verified
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Update timestamp
 */

/**
 * @typedef {Object} ChatAttributes
 * @property {string} id - Chat ID
 * @property {string} [userId] - User ID (optional)
 * @property {string} sessionId - Session ID
 * @property {string} [title] - Chat title (optional)
 * @property {boolean} isGuest - Whether chat is from guest
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Update timestamp
 */

/**
 * @typedef {Object} ChatMessageAttributes
 * @property {string} id - Message ID
 * @property {string} chatId - Chat ID
 * @property {string} message - Message content
 * @property {string} response - Response content
 * @property {string} llmModel - LLM model used
 * @property {boolean} isUserMessage - Whether message is from user
 * @property {string} [parentMessageId] - Parent message ID (optional)
 * @property {number} version - Message version
 * @property {boolean} isRegenerated - Whether message is regenerated
 * @property {string} [attachmentPath] - Attachment file path (optional)
 * @property {string} [attachmentName] - Attachment file name (optional)
 * @property {string} [attachmentType] - Attachment MIME type (optional)
 * @property {number} [attachmentSize] - Attachment file size (optional)
 * @property {Date} createdAt - Creation timestamp
 */

/**
 * @typedef {Object} UserOTPAttributes
 * @property {string} id - OTP ID
 * @property {string} userId - User ID
 * @property {string} otp - OTP code
 * @property {Date} expiresAt - Expiration timestamp
 * @property {boolean} isUsed - Whether OTP is used
 * @property {Date} createdAt - Creation timestamp
 */

/**
 * @typedef {Object} GuestSessionAttributes
 * @property {string} id - Session ID
 * @property {string} sessionId - Session identifier
 * @property {number} messageCount - Number of messages
 * @property {string} ipAddress - IP address
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Update timestamp
 */

/**
 * @typedef {Object} JWTPayload
 * @property {string} userId - User ID
 * @property {string} [email] - User email (optional)
 * @property {string} [mobile] - User mobile (optional)
 * @property {number} iat - Issued at timestamp
 * @property {number} exp - Expiration timestamp
 */

/**
 * @typedef {Object} UserCreditAttributes
 * @property {string} id - Credit record ID
 * @property {string} userId - User ID
 * @property {number} credits - Credit amount
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Update timestamp
 */

/**
 * @typedef {Object} UserCreditTransactionAttributes
 * @property {string} id - Transaction ID
 * @property {string} userId - User ID
 * @property {number} amount - Transaction amount
 * @property {'CREDIT'|'DEBIT'} type - Transaction type
 * @property {string} description - Transaction description
 * @property {Date} createdAt - Creation timestamp
 */

/**
 * @typedef {Object} UserProfileAttributes
 * @property {string} id - Profile ID
 * @property {string} userId - User ID
 * @property {string} [firstName] - First name (optional)
 * @property {string} [lastName] - Last name (optional)
 * @property {string} [profilePicture] - Profile picture URL (optional)
 * @property {'FREE'|'PREMIUM'|'ENTERPRISE'} plan - User plan
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Update timestamp
 */

/**
 * @typedef {Object} LLMConfig
 * @property {string} model - Model name
 * @property {number} [temperature] - Model temperature (optional)
 * @property {number} [maxTokens] - Maximum tokens (optional)
 * @property {string} [apiKey] - API key (optional)
 */

/**
 * LLM Provider enumeration
 * @readonly
 * @enum {string}
 */
const LLMProvider = {
  OPENAI: 'openai',
  ANTHROPIC: 'anthropic'
};

/**
 * @typedef {Object} ProjectAttributes
 * @property {string} id - Project ID
 * @property {string} userId - User ID
 * @property {string} name - Project name
 * @property {string} description - Project description
 * @property {string} rules - Project rules
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Update timestamp
 */

/**
 * @typedef {Object} ChatThreadAttributes
 * @property {string} id - Thread ID
 * @property {string} [userId] - User ID (optional)
 * @property {string} [projectId] - Project ID (optional)
 * @property {string} sessionId - Session ID
 * @property {string} [name] - Thread name (optional)
 * @property {boolean} isGuest - Whether thread is from guest
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Update timestamp
 */

export { LLMProvider,
  // Note: JSDoc typedefs are available globally once this file is imported
  // Individual typedef exports are not needed in JavaScript
 };
