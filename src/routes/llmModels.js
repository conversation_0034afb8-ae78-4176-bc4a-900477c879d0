import express from 'express';
import { LLMModelController } from '../controllers/LLMModelController.js';

const router = express.Router();

/**
 * @route GET /api/llm-models
 * @desc Get all available LLM models
 * @access Public
 */
router.get('/', LLMModelController.getAvailableModels);

/**
 * @route GET /api/llm-models/providers
 * @desc Get all available providers
 * @access Public
 */
router.get('/providers', LLMModelController.getProviders);

/**
 * @route GET /api/llm-models/capabilities
 * @desc Get models by capabilities
 * @query vision=true|false - Filter by vision support
 * @query audio=true|false - Filter by audio support
 * @query codeExecution=true|false - Filter by code execution support
 * @access Public
 */
router.get('/capabilities', LLMModelController.getModelsByCapabilities);

/**
 * @route GET /api/llm-models/summary
 * @desc Get capabilities summary
 * @access Public
 */
router.get('/summary', LLMModelController.getCapabilitiesSummary);

/**
 * @route GET /api/llm-models/provider/:provider
 * @desc Get models by provider
 * @param provider - Provider name (OPENAI, ANTHROPIC, GOOGLE, DEEPSEEK, META)
 * @access Public
 */
router.get('/provider/:provider', LLMModelController.getModelsByProvider);

/**
 * @route GET /api/llm-models/:modelId
 * @desc Get model information by ID
 * @param modelId - Model ID
 * @access Public
 */
router.get('/:modelId', LLMModelController.getModelInfo);

/**
 * @route POST /api/llm-models/initialize
 * @desc Initialize LLM models database (Admin only)
 * @access Admin
 */
router.post('/initialize', LLMModelController.initializeModels);

export default router;
