import { Router  } from 'express';
import { ProjectController  } from '../controllers/ProjectController.js';
import { validate, validationSchemas, validateQuery, validateParams  } from '../middleware/validation.js';
import { authenticateToken, rateLimitPerUser  } from '../middleware/auth.js';
import { CSRFProtection  } from '../middleware/security.js';
import { uploadSingleFile, handleFileUploadError  } from '../middleware/fileUpload.js';
import { checkFileUploadLimits, consumeFileUploadLimit } from '../middleware/fileUploadLimits.js';

const router = Router();

// All project routes require authentication
router.use(authenticateToken);

// Create new project
router.post('/',
  validate(validationSchemas.createProject),
  rateLimitPerUser(10, 60 * 1000), // 10 project creations per minute
  ProjectController.createProject
);

// Get user projects
router.get('/',
  validateQuery(validationSchemas.pagination),
  ProjectController.getUserProjects
);

// Search projects
router.get('/search',
  validateQuery(validationSchemas.searchQuery),
  ProjectController.searchProjects
);

// Get specific project
router.get('/:projectId',
  validateParams(validationSchemas.projectId),
  ProjectController.getProject
);

// Update project
router.put('/:projectId',
  validateParams(validationSchemas.projectId),
  validate(validationSchemas.updateProject),
  ProjectController.updateProject
);

// Delete project
router.delete('/:projectId',
  validateParams(validationSchemas.projectId),
  ProjectController.deleteProject
);

// Get project threads
router.get('/:projectId/threads',
  validateParams(validationSchemas.projectId),
  validateQuery(validationSchemas.pagination),
  ProjectController.getProjectThreads
);

// Send message in project thread
router.post('/:projectId/message',
  validateParams(validationSchemas.projectId),
  validate(validationSchemas.projectMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  ProjectController.sendProjectMessage
);

// Send streaming message in project thread
router.post('/:projectId/message/stream',
  validateParams(validationSchemas.projectId),
  validate(validationSchemas.projectMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  ProjectController.sendProjectStreamingMessage
);

// Send streaming message in project thread with file attachment support
router.post('/:projectId/message/stream/attachment',
  uploadSingleFile,
  handleFileUploadError,
  checkFileUploadLimits,
  validateParams(validationSchemas.projectId),
  validate(validationSchemas.projectMessage),
  rateLimitPerUser(15, 60 * 1000), // 15 messages per minute (lower limit for attachments)
  ProjectController.sendProjectStreamingMessageWithAttachment,
  consumeFileUploadLimit
);

// Get project statistics
router.get('/:projectId/stats',
  validateParams(validationSchemas.projectId),
  ProjectController.getProjectStats
);

export default router;
