import { Router  } from 'express';
import <PERSON><PERSON> from 'joi';
import { Chat<PERSON>ontroller  } from '../controllers/ChatController.js';
import { validate, validationSchemas, validateQuery, validateParams  } from '../middleware/validation.js';
import { authenticateToken, optionalAuth, extractSessionId, extractClientIP, rateLimitPerUser  } from '../middleware/auth.js';
import { CSRFProtection  } from '../middleware/security.js';
import { uploadSingleFile, handleFileUploadError, optionalFile  } from '../middleware/fileUpload.js';
import { checkFileUploadLimits, consumeFileUploadLimit } from '../middleware/fileUploadLimits.js';
import { validateGuestChatSession } from '../middleware/guestChatValidation.js';

const router = Router();

// Chat message endpoint (supports both authenticated and guest users)
router.post('/message',
  extractSessionId,
  extractClientIP,
  optionalAuth,
  validate(validationSchemas.chatMessage),
  rateLimitPerUser(100, 60 * 1000), // 100 messages per minute
  CSRFProtection.protect(),
  ChatController.sendMessage
);

// Streaming chat message endpoint (supports both authenticated and guest users)
// Simplified version without CSRF protection and complex session handling
router.post('/message/stream',
  extractSessionId,
  extractClientIP,
  optionalAuth,
  validateGuestChatSession,
  validate(validationSchemas.simpleChatMessage),
  rateLimitPerUser(100, 60 * 1000), // 100 messages per minute
  ChatController.sendSimpleStreamingMessage
);

// Streaming chat message endpoint with file attachment support
router.post('/message/stream/attachment',
  uploadSingleFile,
  handleFileUploadError,
  extractSessionId,
  extractClientIP,
  optionalAuth,
  validateGuestChatSession,
  checkFileUploadLimits,
  validate(validationSchemas.chatMessageWithAttachment),
  rateLimitPerUser(50, 60 * 1000), // 50 messages per minute (lower limit for attachments)
  ChatController.sendStreamingMessageWithAttachment,
  consumeFileUploadLimit
);

// Regenerate AI response endpoint (supports both authenticated and guest users)
router.post('/regenerate',
  extractSessionId,
  extractClientIP,
  optionalAuth,
  validateGuestChatSession,
  validate(validationSchemas.regenerateMessage),
  rateLimitPerUser(10, 60 * 1000), // 10 regenerations per minute (lower limit)
  ChatController.regenerateResponse
);

// Guest session info
router.get('/guest/:sessionId',
  validateParams(Joi.object({
    sessionId: Joi.string().required()
  })),
  ChatController.getGuestSessionInfo
);

router.get('/guest-info',
  ChatController.getGuestSessionInfo
);

// Protected routes (authentication required)
router.get('/chats',
  authenticateToken,
  validateQuery(validationSchemas.pagination),
  ChatController.getUserChats
);

router.get('/chats/search',
  authenticateToken,
  validateQuery(validationSchemas.searchQuery),
  ChatController.searchChats
);

router.get('/chats/:chatId/messages',
  authenticateToken,
  validateParams(validationSchemas.chatId),
  validateQuery(validationSchemas.pagination),
  ChatController.getChatMessages
);

router.put('/chats/:chatId/title',
  authenticateToken,
  validateParams(validationSchemas.chatId),
  validate(Joi.object({
    title: Joi.string().max(100).required()
  })),
  ChatController.updateChatTitle
);

router.delete('/chats/:chatId',
  authenticateToken,
  validateParams(validationSchemas.chatId),
  ChatController.deleteChat
);

router.get('/chats/:chatId/export',
  authenticateToken,
  validateParams(validationSchemas.chatId),
  ChatController.exportChat
);

// Convert guest chat to user chat (when user logs in)
router.post('/convert-guest-chat',
  authenticateToken,
  validate(Joi.object({
    sessionId: Joi.string().required()
  })),
  ChatController.convertGuestChat
);

// LLM model management
router.get('/models',
  ChatController.getAvailableModels
);

router.post('/models/test',
  rateLimitPerUser(20, 60 * 1000), // 20 tests per minute
  validate(Joi.object({
    model: Joi.string().optional(),
    message: Joi.string().required()
  })),
  ChatController.testModel
);

// Admin/Stats routes
router.get('/stats',
  authenticateToken,
  ChatController.getChatStats
);

// Debug namespace endpoint (for development/debugging)
router.get('/debug/namespace/:sessionId',
  extractSessionId,
  optionalAuth,
  validateParams(Joi.object({
    sessionId: Joi.string().required().min(1).max(200)
  })),
  ChatController.debugNamespace
);

// Debug semantic search endpoint (for development/debugging)
router.post('/debug/search/:sessionId',
  extractSessionId,
  optionalAuth,
  validateParams(Joi.object({
    sessionId: Joi.string().required().min(1).max(200)
  })),
  validate(Joi.object({
    query: Joi.string().required().min(1).max(1000),
    topK: Joi.number().optional().min(1).max(20).default(5)
  })),
  ChatController.debugSemanticSearch
);

export default router;
