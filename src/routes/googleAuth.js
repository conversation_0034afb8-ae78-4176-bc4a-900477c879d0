import { Router } from 'express';
import <PERSON><PERSON> from 'joi';
import { GoogleAuthController } from '../controllers/GoogleAuthController.js';
import { validate, validateParams } from '../middleware/validation.js';
import { authenticateToken, rateLimitPerUser } from '../middleware/auth.js';

const router = Router();

// Validation schemas for Google OAuth
const googleAuthSchemas = {
  initiateAuth: Joi.object({
    scopes: Joi.array()
      .items(Joi.string().uri())
      .optional()
      .messages({
        'array.base': 'Scopes must be an array',
        'string.uri': 'Each scope must be a valid URI'
      })
  }),

  checkScopes: Joi.object({
    scopes: Joi.array()
      .items(Joi.string().uri())
      .required()
      .messages({
        'any.required': 'Scopes array is required',
        'array.base': 'Scopes must be an array',
        'string.uri': 'Each scope must be a valid URI'
      })
  }),

  serviceParam: Joi.object({
    service: Joi.string()
      .valid('drive', 'sheets', 'docs', 'calendar', 'slides')
      .required()
      .messages({
        'any.required': 'Service parameter is required',
        'any.only': 'Service must be one of: drive, sheets, docs, calendar, slides'
      })
  })
};

// Public routes (no authentication required)

/**
 * Handle OAuth callback from Google
 * GET /api/google-auth/callback
 */
router.get('/callback',
  rateLimitPerUser(10, 15 * 60 * 1000), // 10 attempts per 15 minutes
  GoogleAuthController.handleCallback
);

/**
 * Get available Google services and scopes
 * GET /api/google-auth/services
 */
router.get('/services',
  GoogleAuthController.getAvailableServices
);

// Protected routes (authentication required)

/**
 * Initiate Google OAuth flow
 * POST /api/google-auth/initiate
 */
router.post('/initiate',
  authenticateToken,
  validate(googleAuthSchemas.initiateAuth),
  rateLimitPerUser(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  GoogleAuthController.initiateAuth
);

/**
 * Get user's Google authentication status
 * GET /api/google-auth/status
 */
router.get('/status',
  authenticateToken,
  rateLimitPerUser(20, 60 * 1000), // 20 requests per minute
  GoogleAuthController.getAuthStatus
);

/**
 * Revoke Google authentication
 * POST /api/google-auth/revoke
 */
router.post('/revoke',
  authenticateToken,
  rateLimitPerUser(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
  GoogleAuthController.revokeAuth
);

/**
 * Refresh Google access token
 * POST /api/google-auth/refresh
 */
router.post('/refresh',
  authenticateToken,
  rateLimitPerUser(10, 15 * 60 * 1000), // 10 attempts per 15 minutes
  GoogleAuthController.refreshToken
);

/**
 * Test Google API access for a specific service
 * GET /api/google-auth/test/:service
 */
router.get('/test/:service',
  authenticateToken,
  validateParams(googleAuthSchemas.serviceParam),
  rateLimitPerUser(5, 60 * 1000), // 5 tests per minute
  GoogleAuthController.testApiAccess
);

/**
 * Check if user has required scopes
 * POST /api/google-auth/check-scopes
 */
router.post('/check-scopes',
  authenticateToken,
  validate(googleAuthSchemas.checkScopes),
  rateLimitPerUser(20, 60 * 1000), // 20 requests per minute
  GoogleAuthController.checkScopes
);

export default router;
