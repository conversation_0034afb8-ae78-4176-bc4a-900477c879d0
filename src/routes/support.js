import { Router } from 'express';
import Joi from 'joi';
import { SupportController } from '../controllers/SupportController.js';
import { validate, validateQuery, validateParams } from '../middleware/validation.js';
import { authenticateToken, rateLimitPerUser } from '../middleware/auth.js';
import { uploadSingleFile, handleFileUploadError, optionalFile } from '../middleware/fileUpload.js';
import { checkFileUploadLimits, consumeFileUploadLimit } from '../middleware/fileUploadLimits.js';

const router = Router();

// Validation schemas for support endpoints
const supportValidationSchemas = {
  createTicket: Joi.object({
    subject: Joi.string()
      .trim()
      .min(1)
      .max(255)
      .required()
      .messages({
        'string.empty': 'Subject is required',
        'string.min': 'Subject must not be empty',
        'string.max': 'Subject must be less than 255 characters',
        'any.required': 'Subject is required'
      }),

    description: Joi.string()
      .trim()
      .min(1)
      .max(5000)
      .required()
      .messages({
        'string.empty': 'Description is required',
        'string.min': 'Description must not be empty',
        'string.max': 'Description must be less than 5000 characters',
        'any.required': 'Description is required'
      }),

    techDetails: Joi.string()
      .max(5000)
      .optional()
      .allow('')
      .messages({
        'string.max': 'Technical details must be less than 5000 characters'
      }),

    priority: Joi.string()
      .valid('LOW', 'MEDIUM', 'HIGH', 'URGENT')
      .default('MEDIUM')
      .messages({
        'any.only': 'Priority must be one of: LOW, MEDIUM, HIGH, URGENT'
      })
  }),

  ticketQuery: Joi.object({
    page: Joi.number()
      .integer()
      .min(1)
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1'
      }),
    
    limit: Joi.number()
      .integer()
      .min(1)
      .max(50)
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit must be at most 50'
      }),
    
    status: Joi.string()
      .valid('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED')
      .optional()
      .messages({
        'any.only': 'Status must be one of: OPEN, IN_PROGRESS, RESOLVED, CLOSED'
      }),
    
    priority: Joi.string()
      .valid('LOW', 'MEDIUM', 'HIGH', 'URGENT')
      .optional()
      .messages({
        'any.only': 'Priority must be one of: LOW, MEDIUM, HIGH, URGENT'
      })
  }),

  ticketId: Joi.object({
    ticketId: Joi.string()
      .uuid()
      .required()
      .messages({
        'string.guid': 'Ticket ID must be a valid UUID',
        'any.required': 'Ticket ID is required'
      })
  })
};

// Public routes (no authentication required)

/**
 * @route GET /api/support/info
 * @desc Get support information and guidelines
 * @access Public
 */
router.get('/info', SupportController.getSupportInfo);

/**
 * @route GET /api/support/priorities
 * @desc Get available priority options
 * @access Public
 */
router.get('/priorities', SupportController.getPriorityOptions);

/**
 * @route GET /api/support/statuses
 * @desc Get available status options
 * @access Public
 */
router.get('/statuses', SupportController.getStatusOptions);

// Protected routes (authentication required)
router.use(authenticateToken);

/**
 * @route POST /api/support/tickets
 * @desc Create a new support ticket with optional file attachment
 * @access Private
 * @body {string} subject - Ticket subject (required)
 * @body {string} description - Ticket description (required)
 * @body {string} [techDetails] - Optional technical details
 * @body {string} [priority=MEDIUM] - Ticket priority (LOW, MEDIUM, HIGH, URGENT)
 * @file {File} [attachment] - Optional file attachment (max 5MB)
 */
router.post('/tickets',
  uploadSingleFile,
  handleFileUploadError,
  optionalFile,
  checkFileUploadLimits,
  validate(supportValidationSchemas.createTicket),
  rateLimitPerUser(5, 60 * 60 * 1000), // 5 tickets per hour
  SupportController.createTicket,
  consumeFileUploadLimit
);

/**
 * @route GET /api/support/tickets
 * @desc Get user's support tickets with pagination and filtering
 * @access Private
 * @query {number} [page=1] - Page number
 * @query {number} [limit=10] - Items per page (max 50)
 * @query {string} [status] - Filter by status
 * @query {string} [priority] - Filter by priority
 */
router.get('/tickets',
  validateQuery(supportValidationSchemas.ticketQuery),
  SupportController.getUserTickets
);

/**
 * @route GET /api/support/tickets/stats
 * @desc Get user's ticket statistics
 * @access Private
 */
router.get('/tickets/stats',
  SupportController.getUserTicketStats
);

/**
 * @route GET /api/support/tickets/:ticketId
 * @desc Get a specific ticket by ID
 * @access Private
 * @param {string} ticketId - Ticket UUID
 */
router.get('/tickets/:ticketId',
  validateParams(supportValidationSchemas.ticketId),
  SupportController.getTicketById
);

/**
 * @route POST /api/support/validate
 * @desc Validate ticket data before submission
 * @access Private
 * @body {Object} ticketData - Ticket data to validate
 */
router.post('/validate',
  validate(supportValidationSchemas.createTicket),
  SupportController.validateTicketData
);

export default router;
