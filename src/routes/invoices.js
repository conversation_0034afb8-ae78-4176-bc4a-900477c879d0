import { Router } from 'express';
import <PERSON><PERSON> from 'joi';
import { InvoiceController } from '../controllers/InvoiceController.js';
import { validateParams, validateQuery } from '../middleware/validation.js';
import { authenticateToken, rateLimitPerUser } from '../middleware/auth.js';

const router = Router();

// Validation schemas
const invoiceIdSchema = Joi.object({
  invoiceId: Joi.string().uuid().required()
});

const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10)
});

/**
 * Handle CORS preflight requests for invoice downloads
 * OPTIONS /api/invoices/:invoiceId/download
 */
router.options('/:invoiceId/download', InvoiceController.handleCorsOptions);

/**
 * Download invoice by ID (public endpoint - no auth required for invoice downloads)
 * GET /api/invoices/:invoiceId/download
 */
router.get('/:invoiceId/download',
  validateParams(invoiceIdSchema),
  rateLimitPerUser(20, 60 * 1000), // 20 downloads per minute
  InvoiceController.downloadInvoice
);

// Protected routes (require authentication)
router.use(authenticateToken);

/**
 * Get user's invoices list
 * GET /api/invoices
 */
router.get('/',
  validateQuery(paginationSchema),
  rateLimitPerUser(30, 60 * 1000), // 30 requests per minute
  InvoiceController.getUserInvoices
);

export default router;
