import { Router } from 'express';
import { PaymentController } from '../controllers/PaymentController.js';
import { validate, validateQuery, validateParams } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import Joi from 'joi';

const router = Router();

/**
 * Validation schemas for payment routes
 */
const validationSchemas = {
  verifyPayment: Joi.object({
    razorpay_payment_id: Joi.string().required(),
    razorpay_order_id: Joi.string().required(),
    razorpay_signature: Joi.string().required(),
  }),

  requestRefund: Joi.object({
    amount: Joi.number().positive().optional(),
    reason: Joi.string().max(500).optional(),
  }),

  transactionId: Joi.object({
    transactionId: Joi.string().uuid().required(),
  }),

  pagination: Joi.object({
    limit: Joi.number().integer().min(1).max(100).optional(),
    offset: Joi.number().integer().min(0).optional(),
  }),
};

// Public routes (no authentication required)

/**
 * @route POST /api/payment/verify
 * @desc Verify payment signature and process transaction
 * @access Public
 * @body {string} razorpay_payment_id - Razorpay payment ID
 * @body {string} razorpay_order_id - Razorpay order ID
 * @body {string} razorpay_signature - Payment signature
 */
router.post('/verify',
  validate(validationSchemas.verifyPayment),
  PaymentController.verifyPayment
);

/**
 * @route POST /api/payment/webhook
 * @desc Handle Razorpay webhook events
 * @access Public (but signature verified)
 */
router.post('/webhook', PaymentController.handleWebhook);

// Protected routes (authentication required)
router.use(authenticateToken);

/**
 * @route GET /api/payment/history
 * @desc Get user's payment history
 * @access Private
 * @query {number} [limit=10] - Number of transactions to return
 * @query {number} [offset=0] - Number of transactions to skip
 */
router.get('/history',
  validateQuery(validationSchemas.pagination),
  PaymentController.getPaymentHistory
);

/**
 * @route GET /api/payment/stats
 * @desc Get payment statistics for user
 * @access Private
 */
router.get('/stats', PaymentController.getPaymentStats);

/**
 * @route GET /api/payment/transaction/:transactionId
 * @desc Get transaction details
 * @access Private
 * @param {string} transactionId - Transaction ID
 */
router.get('/transaction/:transactionId',
  validateParams(validationSchemas.transactionId),
  PaymentController.getTransactionDetails
);

/**
 * @route POST /api/payment/transaction/:transactionId/refund
 * @desc Request refund for a transaction
 * @access Private
 * @param {string} transactionId - Transaction ID
 * @body {number} [amount] - Refund amount (full refund if not specified)
 * @body {string} [reason] - Refund reason
 */
router.post('/transaction/:transactionId/refund',
  validateParams(validationSchemas.transactionId),
  validate(validationSchemas.requestRefund),
  PaymentController.requestRefund
);

export default router;
