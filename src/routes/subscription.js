import { Router } from 'express';
import { SubscriptionController } from '../controllers/SubscriptionController.js';
import { validate, validateQuery } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import Joi from 'joi';

const router = Router();

/**
 * Validation schemas for subscription routes
 */
const validationSchemas = {
  createSubscription: Joi.object({
    planType: Joi.string().valid('CREATOR', 'PRO').required(),
  }),

  upgradePlan: Joi.object({
    planType: Joi.string().valid('CREATOR', 'PRO').required(),
  }),

  downgradePlan: Joi.object({
    planType: Joi.string().valid('EXPLORER', 'CREATOR', 'PRO').required(),
  }),

  cancelSubscription: Joi.object({
    reason: Joi.string().max(500).optional(),
  }),

  pagination: Joi.object({
    limit: Joi.number().integer().min(1).max(100).optional(),
    offset: Joi.number().integer().min(0).optional(),
  }),
};

// Public routes (no authentication required)

/**
 * @route GET /api/subscription/plans
 * @desc Get all available subscription plans
 * @access Public
 */
router.get('/plans', SubscriptionController.getPlans);

// Protected routes (authentication required)
router.use(authenticateToken);

/**
 * @route GET /api/subscription/current
 * @desc Get user's current subscription
 * @access Private
 */
router.get('/current', SubscriptionController.getCurrentSubscription);

/**
 * @route POST /api/subscription/create
 * @desc Create a new subscription order
 * @access Private
 * @body {string} planType - Plan type (CREATOR, PRO)
 */
router.post('/create',
  validate(validationSchemas.createSubscription),
  SubscriptionController.createSubscription
);

/**
 * @route POST /api/subscription/upgrade
 * @desc Upgrade user's subscription plan
 * @access Private
 * @body {string} planType - Plan type to upgrade to (CREATOR, PRO)
 */
router.post('/upgrade',
  validate(validationSchemas.upgradePlan),
  SubscriptionController.upgradePlan
);

/**
 * @route POST /api/subscription/downgrade
 * @desc Downgrade user's subscription plan
 * @access Private
 * @body {string} planType - Plan type to downgrade to (EXPLORER, CREATOR, PRO)
 */
router.post('/downgrade',
  validate(validationSchemas.downgradePlan),
  SubscriptionController.downgradePlan
);

/**
 * @route POST /api/subscription/cancel
 * @desc Cancel user's current subscription
 * @access Private
 * @body {string} [reason] - Cancellation reason
 */
router.post('/cancel',
  validate(validationSchemas.cancelSubscription),
  SubscriptionController.cancelSubscription
);

/**
 * @route POST /api/subscription/addon
 * @desc Create addon purchase order
 * @access Private
 */
router.post('/addon', SubscriptionController.createAddonOrder);

/**
 * @route GET /api/subscription/stats
 * @desc Get subscription statistics for user
 * @access Private
 */
router.get('/stats', SubscriptionController.getSubscriptionStats);

export default router;
