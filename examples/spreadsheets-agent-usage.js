/**
 * SpreadsheetsAgent Usage Examples - Single Endpoint Approach
 * This file demonstrates how to use the SpreadsheetsAgent through natural language queries
 */

import { SpreadsheetsAgentService } from '../src/services/SpreadsheetsAgentService.js';

// Initialize the service
const spreadsheetsAgent = new SpreadsheetsAgentService();

/**
 * Example 1: Create a new project spreadsheet using natural language
 */
async function createProjectSpreadsheet(userId) {
  console.log('📊 Creating a new project spreadsheet...');

  try {
    const result = await spreadsheetsAgent.processNaturalLanguageRequest(
      userId,
      "Create a new spreadsheet called 'Project Management Dashboard' with three sheets: Tasks (1000 rows, 10 columns), Resources (500 rows, 8 columns), and Timeline (200 rows, 6 columns)"
    );

    if (result.success) {
      console.log('✅ Spreadsheet created successfully!');
      console.log(`   Response: ${result.message}`);

      // Extract spreadsheet ID from the response data
      const spreadsheetData = result.data?.find(d => d.name === 'create_spreadsheet');
      if (spreadsheetData?.result?.spreadsheet) {
        const spreadsheetId = spreadsheetData.result.spreadsheet.spreadsheetId;
        console.log(`   Spreadsheet ID: ${spreadsheetId}`);
        console.log(`   URL: ${spreadsheetData.result.spreadsheet.spreadsheetUrl}`);
        return spreadsheetId;
      }
    } else {
      console.error('❌ Failed to create spreadsheet:', result.message);
    }
  } catch (error) {
    console.error('❌ Error creating spreadsheet:', error.message);
  }
}

/**
 * Example 2: Populate spreadsheet with initial data using natural language
 */
async function populateSpreadsheetWithData(userId, spreadsheetId) {
  console.log('📝 Populating spreadsheet with initial data...');

  try {
    const result = await spreadsheetsAgent.processNaturalLanguageRequest(
      userId,
      `Update the Tasks sheet in spreadsheet ${spreadsheetId} with the following data in range A1:F5:
      Row 1: Task ID, Task Name, Status, Assignee, Due Date, Priority
      Row 2: T001, Project Setup, Complete, John Doe, 2024-01-15, High
      Row 3: T002, Requirements Gathering, In Progress, Jane Smith, 2024-01-20, High
      Row 4: T003, UI Design, Not Started, Bob Johnson, 2024-01-25, Medium
      Row 5: T004, Backend Development, Not Started, Alice Brown, 2024-02-01, High`
    );

    if (result.success) {
      console.log('✅ Task data added successfully!');
      console.log(`   Response: ${result.message}`);
    } else {
      console.error('❌ Failed to add task data:', result.message);
    }
  } catch (error) {
    console.error('❌ Error adding task data:', error.message);
  }
}

/**
 * Example 3: Read and analyze spreadsheet data using natural language
 */
async function analyzeSpreadsheetData(userId, spreadsheetId) {
  console.log('📊 Analyzing spreadsheet data...');

  try {
    const result = await spreadsheetsAgent.processNaturalLanguageRequest(
      userId,
      `Read the data from the Tasks sheet in spreadsheet ${spreadsheetId}, specifically range A1:F10, and show me the content`
    );

    if (result.success) {
      console.log('✅ Data retrieved successfully!');
      console.log(`   Response: ${result.message}`);

      // Extract data from the response for analysis
      const readData = result.data?.find(d => d.name === 'read_spreadsheet_content');
      if (readData?.result?.content?.sheetData?.[0]?.values) {
        const data = readData.result.content.sheetData[0].values;
        const headers = data[0];
        const tasks = data.slice(1);

        console.log(`   Total tasks: ${tasks.length}`);

        // Count tasks by status
        const statusCount = {};
        tasks.forEach(task => {
          const status = task[2]; // Status column
          statusCount[status] = (statusCount[status] || 0) + 1;
        });

        console.log('   Task status breakdown:');
        Object.entries(statusCount).forEach(([status, count]) => {
          console.log(`     - ${status}: ${count} tasks`);
        });
      }
    } else {
      console.error('❌ Failed to read data:', result.message);
    }
  } catch (error) {
    console.error('❌ Error reading data:', error.message);
  }
}

/**
 * Example 4: Natural language spreadsheet operations
 */
async function naturalLanguageOperations(userId) {
  console.log('🧠 Using natural language for spreadsheet operations...');
  
  const requests = [
    "Show me all my spreadsheets",
    "Create a budget spreadsheet with monthly and yearly sheets",
    "Find my project spreadsheet and show me the task data",
    "Update the status of task T002 to 'Complete' in my project spreadsheet"
  ];

  for (const request of requests) {
    try {
      console.log(`\n🗣️  Request: "${request}"`);
      
      const result = await spreadsheetsAgent.processNaturalLanguageRequest(userId, request);
      
      if (result.success) {
        console.log('✅ Response:', result.message);
      } else {
        console.error('❌ Failed:', result.message);
      }
    } catch (error) {
      console.error('❌ Error:', error.message);
    }
  }
}

/**
 * Example 5: Batch operations using natural language
 */
async function batchOperations(userId) {
  console.log('🔄 Performing batch operations...');

  try {
    // Get all spreadsheets first
    const allSpreadsheetsResult = await spreadsheetsAgent.processNaturalLanguageRequest(
      userId,
      "Show me all my spreadsheets with their names and IDs"
    );

    if (allSpreadsheetsResult.success) {
      console.log('✅ Retrieved all spreadsheets');
      console.log(`   Response: ${allSpreadsheetsResult.message}`);

      // Extract spreadsheet data
      const spreadsheetsData = allSpreadsheetsResult.data?.find(d => d.name === 'get_all_spreadsheets');
      if (spreadsheetsData?.result?.spreadsheets) {
        const spreadsheets = spreadsheetsData.result.spreadsheets;
        console.log(`   Found ${spreadsheets.length} spreadsheets`);

        // Get details for first few spreadsheets
        for (const spreadsheet of spreadsheets.slice(0, 2)) { // Limit to first 2
          console.log(`\n📋 Getting details for: ${spreadsheet.name}`);

          const detailsResult = await spreadsheetsAgent.processNaturalLanguageRequest(
            userId,
            `Get detailed information about spreadsheet ${spreadsheet.id} including all sheets`
          );

          if (detailsResult.success) {
            console.log(`   Response: ${detailsResult.message}`);
          }
        }
      }
    }
  } catch (error) {
    console.error('❌ Error in batch operations:', error.message);
  }
}

/**
 * Example 6: Error handling demonstration
 */
async function demonstrateErrorHandling(userId) {
  console.log('🛡️  Demonstrating error handling...');
  
  // Test various error scenarios
  const errorTests = [
    {
      name: 'Invalid spreadsheet ID',
      operation: () => spreadsheetsAgent.getSpreadsheetDetails(userId, 'invalid-id')
    },
    {
      name: 'Empty message',
      operation: () => spreadsheetsAgent.processNaturalLanguageRequest(userId, '')
    },
    {
      name: 'Invalid range format',
      operation: () => spreadsheetsAgent.readSpreadsheetContent(userId, 'valid-id', 'Sheet1', 'invalid-range')
    }
  ];

  for (const test of errorTests) {
    try {
      console.log(`\n🧪 Testing: ${test.name}`);
      const result = await test.operation();
      
      if (result.success === false) {
        console.log(`✅ Error handled correctly: ${result.error}`);
        console.log(`   Message: ${result.message}`);
      } else {
        console.log('⚠️  Expected error but operation succeeded');
      }
    } catch (error) {
      console.log(`✅ Exception caught: ${error.message}`);
    }
  }
}

/**
 * Main example runner
 */
async function runExamples() {
  const userId = 'example-user-123'; // Replace with actual user ID
  
  console.log('🚀 SpreadsheetsAgent Usage Examples\n');
  console.log('=====================================\n');

  try {
    // Example 1: Create spreadsheet
    const spreadsheetId = await createProjectSpreadsheet(userId);
    
    if (spreadsheetId) {
      // Example 2: Populate with data
      await populateSpreadsheetWithData(userId, spreadsheetId);
      
      // Example 3: Analyze data
      await analyzeSpreadsheetData(userId, spreadsheetId);
    }
    
    // Example 4: Natural language operations
    await naturalLanguageOperations(userId);
    
    // Example 5: Batch operations
    await batchOperations(userId);
    
    // Example 6: Error handling
    await demonstrateErrorHandling(userId);
    
    console.log('\n🎉 All examples completed!');
    
  } catch (error) {
    console.error('\n💥 Example execution failed:', error.message);
  }
}

/**
 * Utility function to check agent status
 */
function checkAgentStatus() {
  console.log('📊 Checking SpreadsheetsAgent status...');
  
  const status = spreadsheetsAgent.getStatus();
  console.log(`Status: ${status.status}`);
  console.log(`Tools available: ${status.tools.length}`);
  console.log(`Graph initialized: ${status.graphInitialized}`);
  
  console.log('\nAvailable operations:');
  const operations = spreadsheetsAgent.getAvailableOperations();
  operations.forEach(op => {
    console.log(`  - ${op.name}: ${op.description}`);
  });
}

// Export functions for use in other modules
export {
  createProjectSpreadsheet,
  populateSpreadsheetWithData,
  analyzeSpreadsheetData,
  naturalLanguageOperations,
  batchOperations,
  demonstrateErrorHandling,
  runExamples,
  checkAgentStatus
};

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  checkAgentStatus();
  console.log('\n');
  runExamples();
}
