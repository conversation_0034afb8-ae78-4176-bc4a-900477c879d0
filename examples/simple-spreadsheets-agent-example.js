/**
 * Simple SpreadsheetsAgent Example - Single Endpoint Usage
 * This demonstrates the simplicity of using just one API endpoint for all spreadsheet operations
 */

import axios from 'axios';

// Configuration
const API_BASE_URL = 'http://localhost:3000/api';
const JWT_TOKEN = 'your-jwt-token-here'; // Replace with actual JWT token

/**
 * Helper function to make API calls to the single spreadsheet agent endpoint
 */
async function querySpreadsheetAgent(query, context = {}) {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/spreadsheets-agent/query`,
      {
        query,
        context
      },
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('API Error:', error.response?.data || error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Example usage of the single endpoint for various operations
 */
async function demonstrateSpreadsheetOperations() {
  console.log('🚀 SpreadsheetsAgent Single Endpoint Demo\n');

  // Example 1: List all spreadsheets
  console.log('1️⃣ Getting all spreadsheets...');
  const allSpreadsheets = await querySpreadsheetAgent(
    "Show me all my spreadsheets"
  );
  console.log('Response:', allSpreadsheets.success ? '✅ Success' : '❌ Failed');
  console.log('Message:', allSpreadsheets.message);
  console.log('');

  // Example 2: Create a new spreadsheet
  console.log('2️⃣ Creating a new spreadsheet...');
  const newSpreadsheet = await querySpreadsheetAgent(
    "Create a new spreadsheet called 'Team Project Tracker' with sheets for Tasks, Resources, and Timeline"
  );
  console.log('Response:', newSpreadsheet.success ? '✅ Success' : '❌ Failed');
  console.log('Message:', newSpreadsheet.message);
  
  // Extract spreadsheet ID for next operations
  let spreadsheetId = null;
  if (newSpreadsheet.success && newSpreadsheet.data) {
    const createData = newSpreadsheet.data.find(d => d.name === 'create_spreadsheet');
    if (createData?.result?.spreadsheet?.spreadsheetId) {
      spreadsheetId = createData.result.spreadsheet.spreadsheetId;
      console.log('Spreadsheet ID:', spreadsheetId);
    }
  }
  console.log('');

  // Example 3: Add data to the spreadsheet (if created successfully)
  if (spreadsheetId) {
    console.log('3️⃣ Adding task data to the spreadsheet...');
    const addData = await querySpreadsheetAgent(
      `Add the following task data to the Tasks sheet in spreadsheet ${spreadsheetId}:
      Headers in row 1: Task ID, Task Name, Status, Assignee, Due Date
      Task 1: T001, Setup Project, Complete, John Doe, 2024-01-15
      Task 2: T002, Design UI, In Progress, Jane Smith, 2024-01-20
      Task 3: T003, Backend API, Not Started, Bob Johnson, 2024-01-25`
    );
    console.log('Response:', addData.success ? '✅ Success' : '❌ Failed');
    console.log('Message:', addData.message);
    console.log('');

    // Example 4: Read the data back
    console.log('4️⃣ Reading the task data...');
    const readData = await querySpreadsheetAgent(
      `Read all the data from the Tasks sheet in spreadsheet ${spreadsheetId}`
    );
    console.log('Response:', readData.success ? '✅ Success' : '❌ Failed');
    console.log('Message:', readData.message);
    console.log('');

    // Example 5: Update specific data
    console.log('5️⃣ Updating task status...');
    const updateData = await querySpreadsheetAgent(
      `Update the status of task T002 to 'Complete' in the Tasks sheet of spreadsheet ${spreadsheetId}`
    );
    console.log('Response:', updateData.success ? '✅ Success' : '❌ Failed');
    console.log('Message:', updateData.message);
    console.log('');

    // Example 6: Get spreadsheet details
    console.log('6️⃣ Getting spreadsheet details...');
    const details = await querySpreadsheetAgent(
      `Show me detailed information about spreadsheet ${spreadsheetId} including all sheets and their properties`
    );
    console.log('Response:', details.success ? '✅ Success' : '❌ Failed');
    console.log('Message:', details.message);
    console.log('');
  }

  // Example 7: Complex query with multiple operations
  console.log('7️⃣ Complex operation - Create and populate a budget spreadsheet...');
  const complexOperation = await querySpreadsheetAgent(
    `Create a new spreadsheet called 'Monthly Budget 2024' with sheets for Income, Expenses, and Summary. 
    Then add budget categories to the Expenses sheet: Housing, Food, Transportation, Entertainment, Savings.
    Add headers: Category, Budgeted Amount, Actual Amount, Difference`
  );
  console.log('Response:', complexOperation.success ? '✅ Success' : '❌ Failed');
  console.log('Message:', complexOperation.message);
  console.log('');

  // Example 8: Natural language data analysis
  console.log('8️⃣ Data analysis query...');
  const analysis = await querySpreadsheetAgent(
    "Find all my project-related spreadsheets and tell me how many sheets each one has"
  );
  console.log('Response:', analysis.success ? '✅ Success' : '❌ Failed');
  console.log('Message:', analysis.message);
  console.log('');

  console.log('🎉 Demo completed!');
}

/**
 * Example of error handling
 */
async function demonstrateErrorHandling() {
  console.log('\n🛡️ Error Handling Examples\n');

  // Invalid query
  console.log('Testing empty query...');
  const emptyQuery = await querySpreadsheetAgent('');
  console.log('Response:', emptyQuery.success ? '✅ Success' : '❌ Failed (Expected)');
  console.log('Error:', emptyQuery.error || emptyQuery.message);
  console.log('');

  // Invalid spreadsheet ID
  console.log('Testing invalid spreadsheet ID...');
  const invalidId = await querySpreadsheetAgent(
    'Read data from spreadsheet invalid-id-123'
  );
  console.log('Response:', invalidId.success ? '✅ Success' : '❌ Failed (Expected)');
  console.log('Message:', invalidId.message);
  console.log('');
}

/**
 * Quick test function
 */
async function quickTest() {
  console.log('🧪 Quick Test - List Spreadsheets\n');
  
  const result = await querySpreadsheetAgent("Show me all my spreadsheets");
  
  if (result.success) {
    console.log('✅ Agent is working correctly!');
    console.log('Response:', result.message);
  } else {
    console.log('❌ Agent test failed');
    console.log('Error:', result.error || result.message);
  }
}

// Export functions for use in other modules
export {
  querySpreadsheetAgent,
  demonstrateSpreadsheetOperations,
  demonstrateErrorHandling,
  quickTest
};

// Run demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('Choose an option:');
  console.log('1. Quick Test (node examples/simple-spreadsheets-agent-example.js test)');
  console.log('2. Full Demo (node examples/simple-spreadsheets-agent-example.js demo)');
  console.log('3. Error Handling (node examples/simple-spreadsheets-agent-example.js errors)');
  
  const option = process.argv[2];
  
  switch (option) {
    case 'test':
      quickTest();
      break;
    case 'demo':
      demonstrateSpreadsheetOperations();
      break;
    case 'errors':
      demonstrateErrorHandling();
      break;
    default:
      console.log('\n🚀 Running full demonstration...\n');
      demonstrateSpreadsheetOperations()
        .then(() => demonstrateErrorHandling())
        .catch(console.error);
  }
}
