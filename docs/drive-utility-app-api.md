# DriveUtilityApp API Documentation

The `DriveUtilityApp` class provides utility methods for working with Google Sheets API. It handles spreadsheet creation, content management, and data retrieval operations.

## Overview

The DriveUtilityApp class is designed to work with the existing Google OAuth authentication system in the project. It provides a clean interface for Google Sheets operations while handling authentication, validation, and error management automatically.

## Prerequisites

Before using the DriveUtilityApp, ensure that:

1. The user has completed Google OAuth authentication
2. The user has granted Google Sheets permissions (`https://www.googleapis.com/auth/spreadsheets`)
3. The Google OAuth service is properly configured

## Class Methods

### createSpreadsheet(userId, title, options)

Creates a new Google Sheets spreadsheet.

**Parameters:**
- `userId` (string): User ID for authentication
- `title` (string): Title of the spreadsheet
- `options` (object, optional): Configuration options
  - `sheets` (array): Initial sheets configuration
  - `locale` (string): Spreadsheet locale (default: 'en_US')
  - `timeZone` (string): Spreadsheet timezone (default: 'America/New_York')

**Returns:** Promise<Object> - Created spreadsheet information

**Example:**
```javascript
import { DriveUtilityApp } from '../services/DriveUtilityApp.js';

// Create a simple spreadsheet
const result = await DriveUtilityApp.createSpreadsheet(
  'user-123',
  'My New Spreadsheet'
);

// Create a spreadsheet with custom sheets
const customResult = await DriveUtilityApp.createSpreadsheet(
  'user-123',
  'Project Data',
  {
    sheets: [
      { title: 'Tasks', rowCount: 500, columnCount: 10 },
      { title: 'Resources', rowCount: 200, columnCount: 8 }
    ],
    locale: 'en_US',
    timeZone: 'America/Los_Angeles'
  }
);

console.log('Spreadsheet created:', result.spreadsheetId);
console.log('Access URL:', result.spreadsheetUrl);
```

### updateSpreadsheetContent(userId, spreadsheetId, sheetName, range, values, options)

Updates content in a Google Sheets spreadsheet.

**Parameters:**
- `userId` (string): User ID for authentication
- `spreadsheetId` (string): ID of the spreadsheet
- `sheetName` (string): Name of the sheet to update
- `range` (string): Range to update (e.g., 'A1:C3')
- `values` (Array<Array>): 2D array of values to update
- `options` (object, optional): Configuration options
  - `valueInputOption` (string): How values should be interpreted ('RAW' or 'USER_ENTERED')
  - `includeValuesInResponse` (boolean): Whether to include updated values in response

**Returns:** Promise<Object> - Update result information

**Example:**
```javascript
// Update a range of cells
const updateResult = await DriveUtilityApp.updateSpreadsheetContent(
  'user-123',
  '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  'Sheet1',
  'A1:C3',
  [
    ['Name', 'Age', 'City'],
    ['John Doe', 30, 'New York'],
    ['Jane Smith', 25, 'Los Angeles']
  ],
  {
    valueInputOption: 'USER_ENTERED',
    includeValuesInResponse: true
  }
);

console.log('Updated cells:', updateResult.updatedCells);
console.log('Updated range:', updateResult.updatedRange);
```

### readSpreadsheetContent(userId, spreadsheetId, options)

Reads content from a Google Sheets spreadsheet.

**Parameters:**
- `userId` (string): User ID for authentication
- `spreadsheetId` (string): ID of the spreadsheet
- `options` (object, optional): Configuration options
  - `sheetName` (string): Specific sheet name to read
  - `range` (string): Specific range to read (e.g., 'A1:C10')
  - `valueRenderOption` (string): How values should be rendered
  - `dateTimeRenderOption` (string): How dates should be rendered

**Returns:** Promise<Object> - Spreadsheet content data

**Example:**
```javascript
// Read all sheets
const allData = await DriveUtilityApp.readSpreadsheetContent(
  'user-123',
  '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms'
);

console.log('Total sheets:', allData.totalSheets);
allData.sheetData.forEach(sheet => {
  console.log(`Sheet: ${sheet.sheetName}, Rows: ${sheet.values.length}`);
});

// Read specific sheet
const sheetData = await DriveUtilityApp.readSpreadsheetContent(
  'user-123',
  '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  {
    sheetName: 'Sheet1'
  }
);

// Read specific range
const rangeData = await DriveUtilityApp.readSpreadsheetContent(
  'user-123',
  '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  {
    sheetName: 'Sheet1',
    range: 'A1:C10'
  }
);
```

### getAvailableSheets(userId, spreadsheetId)

Gets all available sheets in a spreadsheet.

**Parameters:**
- `userId` (string): User ID for authentication
- `spreadsheetId` (string): ID of the spreadsheet

**Returns:** Promise<Array> - Array of sheet information

**Example:**
```javascript
const sheetsInfo = await DriveUtilityApp.getAvailableSheets(
  'user-123',
  '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms'
);

console.log('Available sheets:');
sheetsInfo.sheets.forEach(sheet => {
  console.log(`- ${sheet.title} (ID: ${sheet.sheetId})`);
  console.log(`  Rows: ${sheet.gridProperties.rowCount}`);
  console.log(`  Columns: ${sheet.gridProperties.columnCount}`);
});
```

### getSheetData(userId, spreadsheetId, sheetName)

Gets detailed data about a specific sheet.

**Parameters:**
- `userId` (string): User ID for authentication
- `spreadsheetId` (string): ID of the spreadsheet
- `sheetName` (string): Name of the sheet

**Returns:** Promise<Object> - Detailed sheet information

**Example:**
```javascript
const sheetDetails = await DriveUtilityApp.getSheetData(
  'user-123',
  '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  'Sheet1'
);

console.log('Sheet details:');
console.log('Title:', sheetDetails.sheetData.title);
console.log('Sheet ID:', sheetDetails.sheetData.sheetId);
console.log('Actual data rows:', sheetDetails.sheetData.actualDataDimensions.rowCount);
console.log('Actual data columns:', sheetDetails.sheetData.actualDataDimensions.columnCount);
console.log('Has data:', sheetDetails.sheetData.actualDataDimensions.hasData);
```

## Error Handling

The DriveUtilityApp class provides comprehensive error handling for common scenarios:

### Authentication Errors
```javascript
try {
  await DriveUtilityApp.createSpreadsheet('invalid-user', 'Test');
} catch (error) {
  // Error: User is not authenticated with Google
  console.error(error.message);
}
```

### Permission Errors
```javascript
try {
  await DriveUtilityApp.readSpreadsheetContent('user-123', 'spreadsheet-id');
} catch (error) {
  // Error: Insufficient permissions. Please grant Google Sheets access.
  console.error(error.message);
}
```

### Validation Errors
```javascript
try {
  await DriveUtilityApp.updateSpreadsheetContent(
    'user-123',
    'invalid-id',
    'Sheet1',
    'A1:B2',
    'not-an-array'
  );
} catch (error) {
  // Error: Values must be an array
  console.error(error.message);
}
```

## Best Practices

1. **Always handle errors**: Wrap DriveUtilityApp calls in try-catch blocks
2. **Validate user authentication**: Ensure users have completed Google OAuth before using these methods
3. **Use appropriate ranges**: Be specific with ranges to avoid unnecessary data transfer
4. **Batch operations**: When possible, update multiple cells in a single call
5. **Check permissions**: Verify users have granted Google Sheets access before attempting operations

## Integration Example

Here's a complete example of integrating DriveUtilityApp in a controller:

```javascript
import { DriveUtilityApp } from '../services/DriveUtilityApp.js';
import { ResponseUtil } from '../utils/responseUtil.js';

class SheetsController {
  static async createProjectSpreadsheet(req, res) {
    try {
      const userId = req.user.userId;
      const { projectName, initialData } = req.body;

      // Create spreadsheet
      const spreadsheet = await DriveUtilityApp.createSpreadsheet(
        userId,
        `${projectName} - Data`,
        {
          sheets: [
            { title: 'Tasks', rowCount: 1000, columnCount: 10 },
            { title: 'Resources', rowCount: 500, columnCount: 8 }
          ]
        }
      );

      // Add initial data if provided
      if (initialData && initialData.length > 0) {
        await DriveUtilityApp.updateSpreadsheetContent(
          userId,
          spreadsheet.spreadsheetId,
          'Tasks',
          'A1:J' + (initialData.length + 1),
          initialData
        );
      }

      ResponseUtil.success(res, 'Project spreadsheet created successfully', {
        spreadsheetId: spreadsheet.spreadsheetId,
        spreadsheetUrl: spreadsheet.spreadsheetUrl,
        sheets: spreadsheet.sheets
      });

    } catch (error) {
      ResponseUtil.error(res, 'Failed to create project spreadsheet', error.message);
    }
  }
}
```

## Testing

Run the test suite to verify DriveUtilityApp functionality:

```bash
npm run test-drive-utility
```

This will run comprehensive tests including validation, error handling, and method structure verification.
