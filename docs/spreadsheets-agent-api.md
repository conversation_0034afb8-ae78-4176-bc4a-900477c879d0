# SpreadsheetsAgent API Documentation

The SpreadsheetsAgent provides an intelligent, agentic workflow for Google Sheets operations using LangGraph. It allows users to interact with Google Sheets through natural language queries via a single, unified API endpoint.

## Overview

The SpreadsheetsAgent is built using:
- **LangGraph**: For creating the agentic workflow
- **<PERSON><PERSON><PERSON><PERSON>ls**: For structured tool calling
- **DriveUtilityApp**: For underlying Google Sheets operations
- **Credit System**: Integrated with the application's credit system

## Features

- 🤖 **Natural Language Processing**: Process spreadsheet requests in natural language
- 🔧 **5 Core Tools**: Complete CRUD operations for Google Sheets
- 💳 **Credit Integration**: Automatic credit checking and deduction
- 🛡️ **Error Handling**: Comprehensive error handling and validation
- 📊 **State Management**: Intelligent workflow state management
- 🔄 **Tool Chaining**: Ability to chain multiple operations
- 🎯 **Single Endpoint**: One API endpoint handles all operations

## API Endpoint

### Base URL
```
/api/spreadsheets-agent
```

### Authentication
The endpoint requires authentication via JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

---

## The Single Query Endpoint

### Process User Query
Process any spreadsheet operation using natural language through a single endpoint.

**Endpoint:** `POST /api/spreadsheets-agent/query`

**Request Body:**
```json
{
  "query": "Create a new spreadsheet called 'Project Tasks' with two sheets: Tasks and Resources",
  "context": {
    "projectId": "optional-project-id"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "I have completed your spreadsheet operations:\n\n✅ create_spreadsheet: Success\n   Created spreadsheet: Project Tasks",
  "data": [
    {
      "tool_call_id": "call_123",
      "name": "create_spreadsheet",
      "result": {
        "success": true,
        "spreadsheet": {
          "spreadsheetId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
          "title": "Project Tasks",
          "spreadsheetUrl": "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit"
        }
      }
    }
  ]
}
```

## Supported Query Types

The single endpoint can handle all types of spreadsheet operations through natural language:

### 1. List Spreadsheets
```json
{
  "query": "Show me all my spreadsheets"
}
```

### 2. Get Spreadsheet Details
```json
{
  "query": "Get details of spreadsheet 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
}
```

### 3. Read Content
```json
{
  "query": "Read data from my budget spreadsheet"
}
```

```json
{
  "query": "Show me cells A1 to C10 from the Tasks sheet in my project tracker"
}
```

### 4. Update Content
```json
{
  "query": "Update cell A1 in my project spreadsheet to 'Task Name'"
}
```

```json
{
  "query": "Add a new row with data ['Task 4', 'In Progress', 'Alice'] to my task sheet"
}
```

### 5. Create Spreadsheets
```json
{
  "query": "Create a new spreadsheet called 'Budget 2024' with monthly and yearly sheets"
}
```

### 6. Complex Operations
```json
{
  "query": "Create a project tracker, add initial task data with headers, and tell me the spreadsheet URL"
}
```

## Error Responses

All endpoints may return the following error types:

### Authentication Error
```json
{
  "success": false,
  "error": "AUTHENTICATION_ERROR",
  "message": "User is not authenticated with Google. Please authenticate first."
}
```

### Permission Error
```json
{
  "success": false,
  "error": "PERMISSION_ERROR",
  "message": "Insufficient permissions. Please grant Google Sheets access."
}
```

### Validation Error
```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Invalid input parameters.",
  "details": "Message is required and must be a non-empty string"
}
```

### Insufficient Credits
```json
{
  "success": false,
  "error": "INSUFFICIENT_CREDITS",
  "message": "Insufficient credits to process this request. Please upgrade your plan or purchase credits.",
  "creditsRequired": 1
}
```

### API Error
```json
{
  "success": false,
  "error": "API_ERROR",
  "message": "Google Sheets API error. Please try again later."
}
```

## Credit System

Each spreadsheet agent request costs **1 credit**. Credits are automatically checked before processing and deducted upon successful completion.

## Rate Limiting

API endpoints are subject to rate limiting. The default limits are:
- 100 requests per minute per user
- 1000 requests per hour per user

## Prerequisites

Before using the SpreadsheetsAgent, ensure:

1. **Google OAuth Authentication**: User must be authenticated with Google
2. **Google Sheets Permissions**: User must have granted Google Sheets access (`https://www.googleapis.com/auth/spreadsheets`)
3. **Google Drive Permissions**: User must have granted Google Drive access (`https://www.googleapis.com/auth/drive.readonly`) for listing spreadsheets
4. **Sufficient Credits**: User must have enough credits in their account

## Natural Language Examples

The agent can understand various natural language requests:

### Creating Spreadsheets
- "Create a new spreadsheet called 'Budget 2024'"
- "Make a spreadsheet for project tracking with sheets for tasks and resources"
- "Create a budget spreadsheet with monthly and yearly sheets"

### Reading Data
- "Show me all my spreadsheets"
- "Get the data from the 'Sales' sheet in my revenue spreadsheet"
- "Read cells A1 to C10 from the Tasks sheet"

### Updating Data
- "Update cell A1 in the Tasks sheet to 'Project Name'"
- "Add a new row with task data to my project spreadsheet"
- "Update the status column in my task tracker"

### Complex Operations
- "Create a project tracker, add initial task data, and share the link"
- "Find my budget spreadsheet and update this month's expenses"
- "Create a team roster spreadsheet and populate it with member information"
