# Google OAuth API Documentation

This document describes the Google OAuth 2.0 integration for accessing Google Workspace APIs (Drive, Sheets, Docs, Calendar, Slides).

## Overview

The Google OAuth integration allows users to authenticate with their Google accounts and grant permission to access their Google Workspace data. The system supports:

- **Google Drive**: File management and access
- **Google Sheets**: Spreadsheet creation and editing
- **Google Docs**: Document creation and editing
- **Google Calendar**: Calendar and event management
- **Google Slides**: Presentation creation and editing

## Setup Requirements

### 1. Google Cloud Console Setup

1. Create a project in [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the required APIs:
   - Google Drive API
   - Google Sheets API
   - Google Docs API
   - Google Calendar API
   - Google Slides API
3. Create OAuth 2.0 credentials:
   - Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client ID"
   - Choose "Web application"
   - Add authorized redirect URI: `http://localhost:5529/api/google-auth/callback`

### 2. Environment Configuration

Add the following to your `.env` file:

```env
GOOGLE_CLIENT_ID=your_google_oauth_client_id_here
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:5529/api/google-auth/callback
```

## API Endpoints

### Public Endpoints

#### Get Available Services
```http
GET /api/google-auth/services
```

Returns information about available Google services and their OAuth scopes.

**Response:**
```json
{
  "success": true,
  "message": "Available Google services",
  "data": {
    "services": {
      "drive": {
        "name": "Google Drive",
        "description": "Access and manage files in Google Drive",
        "scopes": {
          "full": "https://www.googleapis.com/auth/drive",
          "file": "https://www.googleapis.com/auth/drive.file",
          "readonly": "https://www.googleapis.com/auth/drive.readonly"
        }
      },
      "sheets": {
        "name": "Google Sheets",
        "description": "Create and edit spreadsheets",
        "scopes": {
          "full": "https://www.googleapis.com/auth/spreadsheets",
          "readonly": "https://www.googleapis.com/auth/spreadsheets.readonly"
        }
      }
      // ... other services
    },
    "defaultScopes": [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/spreadsheets",
      "https://www.googleapis.com/auth/documents",
      "https://www.googleapis.com/auth/calendar",
      "https://www.googleapis.com/auth/presentations",
      "https://www.googleapis.com/auth/userinfo.email",
      "https://www.googleapis.com/auth/userinfo.profile"
    ]
  }
}
```

#### Handle OAuth Callback
```http
GET /api/google-auth/callback?code={code}&state={userId}
```

Handles the OAuth callback from Google. This endpoint is called automatically by Google after user authorization.

**Query Parameters:**
- `code`: Authorization code from Google
- `state`: User ID (passed during authorization)

**Response:**
```json
{
  "success": true,
  "message": "Google authentication successful",
  "data": {
    "success": true,
    "userInfo": {
      "id": "google-user-id",
      "email": "<EMAIL>",
      "name": "User Name",
      "picture": "https://..."
    },
    "grantedScopes": [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/sheets"
    ],
    "message": "Your Google account has been successfully connected"
  }
}
```

### Protected Endpoints (Require Authentication)

All protected endpoints require a valid JWT token in the Authorization header:
```http
Authorization: Bearer {your_jwt_token}
```

#### Initiate OAuth Flow
```http
POST /api/google-auth/initiate
```

Generates a Google OAuth authorization URL for the user.

**Request Body:**
```json
{
  "scopes": [
    "https://www.googleapis.com/auth/drive",
    "https://www.googleapis.com/auth/sheets"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Google OAuth URL generated successfully",
  "data": {
    "authUrl": "https://accounts.google.com/oauth/authorize?...",
    "message": "Please visit the URL to authorize access to your Google account"
  }
}
```

#### Get Authentication Status
```http
GET /api/google-auth/status
```

Returns the user's current Google authentication status.

**Response:**
```json
{
  "success": true,
  "message": "Google authentication status retrieved",
  "data": {
    "isAuthenticated": true,
    "isAccessTokenExpired": false,
    "hasRefreshToken": true,
    "grantedScopes": [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/sheets"
    ],
    "lastAuthenticatedAt": "2023-12-15T10:30:00Z",
    "userInfo": {
      "id": "google-user-id",
      "email": "<EMAIL>",
      "name": "User Name"
    }
  }
}
```

#### Refresh Access Token
```http
POST /api/google-auth/refresh
```

Refreshes the user's Google access token using the stored refresh token.

**Response:**
```json
{
  "success": true,
  "message": "Access token refreshed successfully",
  "data": {
    "expiresAt": "2023-12-15T11:30:00Z",
    "message": "Your Google access token has been refreshed"
  }
}
```

#### Revoke Authentication
```http
POST /api/google-auth/revoke
```

Revokes the user's Google authentication and removes stored tokens.

**Response:**
```json
{
  "success": true,
  "message": "Google authentication revoked successfully",
  "data": {
    "message": "Your Google account has been disconnected"
  }
}
```

#### Test API Access
```http
GET /api/google-auth/test/{service}
```

Tests access to a specific Google service.

**Path Parameters:**
- `service`: One of `drive`, `sheets`, `docs`, `calendar`, `slides`

**Response:**
```json
{
  "success": true,
  "message": "drive API access confirmed",
  "data": {
    "service": "drive",
    "success": true,
    "data": {
      "user": {
        "displayName": "User Name",
        "emailAddress": "<EMAIL>"
      }
    }
  }
}
```

#### Check Scopes
```http
POST /api/google-auth/check-scopes
```

Checks if the user has granted specific OAuth scopes.

**Request Body:**
```json
{
  "scopes": [
    "https://www.googleapis.com/auth/drive",
    "https://www.googleapis.com/auth/sheets"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Scope check completed",
  "data": {
    "hasRequiredScopes": true,
    "requestedScopes": [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/sheets"
    ]
  }
}
```

## Integration Guide

### Frontend Integration

1. **Check Authentication Status**
   ```javascript
   const response = await fetch('/api/google-auth/status', {
     headers: {
       'Authorization': `Bearer ${userToken}`
     }
   });
   const { data } = await response.json();
   ```

2. **Initiate OAuth Flow**
   ```javascript
   const response = await fetch('/api/google-auth/initiate', {
     method: 'POST',
     headers: {
       'Authorization': `Bearer ${userToken}`,
       'Content-Type': 'application/json'
     },
     body: JSON.stringify({
       scopes: ['https://www.googleapis.com/auth/drive']
     })
   });
   const { data } = await response.json();
   
   // Redirect user to the authorization URL
   window.location.href = data.authUrl;
   ```

3. **Handle OAuth Callback**
   The callback is handled automatically by the backend. After successful authentication, redirect the user back to your application.

### Backend Service Usage

```javascript
import { GoogleAuthService } from './services/GoogleAuthService.js';

// Get authenticated API client
const driveClient = await GoogleAuthService.getApiClient(userId, 'drive');

// Use the client to access Google APIs
const files = await driveClient.files.list({
  pageSize: 10,
  fields: 'nextPageToken, files(id, name)'
});
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

Common error scenarios:
- **401 Unauthorized**: Invalid or missing JWT token
- **400 Bad Request**: Invalid request parameters
- **403 Forbidden**: Insufficient permissions
- **500 Internal Server Error**: Server-side error

## Rate Limits

- OAuth initiation: 5 requests per 15 minutes
- Token refresh: 10 requests per 15 minutes
- Status checks: 20 requests per minute
- API tests: 5 requests per minute

## Security Considerations

1. **Token Storage**: Access and refresh tokens are encrypted and stored securely in the database
2. **Scope Validation**: Always verify that users have granted required scopes before API calls
3. **Token Expiry**: Access tokens are automatically refreshed when expired
4. **Revocation**: Users can revoke access at any time through the API or Google Account settings

## Next Steps

After implementing Google OAuth authentication, you can:

1. Build specific Google Workspace integrations (Drive file management, Sheets data processing, etc.)
2. Implement webhook handlers for real-time updates
3. Add batch operations for efficient data processing
4. Implement caching strategies for frequently accessed data
