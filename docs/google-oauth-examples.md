# Google OAuth Integration Examples

This document provides practical examples for integrating with the Google OAuth API.

## Frontend Examples

### React Integration

```jsx
import React, { useState, useEffect } from 'react';

const GoogleAuthComponent = () => {
  const [authStatus, setAuthStatus] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/google-auth/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        }
      });
      const result = await response.json();
      setAuthStatus(result.data);
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setLoading(false);
    }
  };

  const initiateGoogleAuth = async () => {
    try {
      const response = await fetch('/api/google-auth/initiate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scopes: [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/sheets',
            'https://www.googleapis.com/auth/documents'
          ]
        })
      });
      const result = await response.json();
      
      if (result.success) {
        // Redirect to Google OAuth
        window.location.href = result.data.authUrl;
      }
    } catch (error) {
      console.error('Error initiating Google auth:', error);
    }
  };

  const revokeGoogleAuth = async () => {
    try {
      const response = await fetch('/api/google-auth/revoke', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        }
      });
      const result = await response.json();
      
      if (result.success) {
        setAuthStatus({ isAuthenticated: false });
      }
    } catch (error) {
      console.error('Error revoking Google auth:', error);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div className="google-auth-component">
      <h3>Google Workspace Integration</h3>
      
      {authStatus?.isAuthenticated ? (
        <div className="auth-success">
          <p>✅ Connected to Google Account</p>
          <p><strong>Email:</strong> {authStatus.userInfo?.email}</p>
          <p><strong>Name:</strong> {authStatus.userInfo?.name}</p>
          
          <div className="granted-scopes">
            <h4>Granted Permissions:</h4>
            <ul>
              {authStatus.grantedScopes?.map((scope, index) => (
                <li key={index}>{scope.split('/').pop()}</li>
              ))}
            </ul>
          </div>
          
          <button onClick={revokeGoogleAuth} className="btn-danger">
            Disconnect Google Account
          </button>
        </div>
      ) : (
        <div className="auth-required">
          <p>Connect your Google account to access Drive, Sheets, Docs, Calendar, and Slides.</p>
          <button onClick={initiateGoogleAuth} className="btn-primary">
            Connect Google Account
          </button>
        </div>
      )}
    </div>
  );
};

export default GoogleAuthComponent;
```

### JavaScript (Vanilla) Integration

```javascript
class GoogleAuthManager {
  constructor(apiBaseUrl, userToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.userToken = userToken;
  }

  async checkAuthStatus() {
    const response = await fetch(`${this.apiBaseUrl}/google-auth/status`, {
      headers: {
        'Authorization': `Bearer ${this.userToken}`
      }
    });
    return await response.json();
  }

  async initiateAuth(scopes = null) {
    const defaultScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/sheets',
      'https://www.googleapis.com/auth/documents',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/presentations'
    ];

    const response = await fetch(`${this.apiBaseUrl}/google-auth/initiate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        scopes: scopes || defaultScopes
      })
    });

    const result = await response.json();
    if (result.success) {
      window.location.href = result.data.authUrl;
    }
    return result;
  }

  async testApiAccess(service) {
    const response = await fetch(`${this.apiBaseUrl}/google-auth/test/${service}`, {
      headers: {
        'Authorization': `Bearer ${this.userToken}`
      }
    });
    return await response.json();
  }

  async refreshToken() {
    const response = await fetch(`${this.apiBaseUrl}/google-auth/refresh`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.userToken}`
      }
    });
    return await response.json();
  }

  async revokeAuth() {
    const response = await fetch(`${this.apiBaseUrl}/google-auth/revoke`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.userToken}`
      }
    });
    return await response.json();
  }
}

// Usage
const authManager = new GoogleAuthManager('/api', userToken);

// Check if user is authenticated
authManager.checkAuthStatus().then(result => {
  if (result.data.isAuthenticated) {
    console.log('User is authenticated with Google');
  } else {
    console.log('User needs to authenticate with Google');
  }
});
```

## Backend Service Examples

### Using Google Drive API

```javascript
import { GoogleAuthService } from '../services/GoogleAuthService.js';

class DriveService {
  static async listFiles(userId, options = {}) {
    try {
      // Check if user has required scopes
      const hasScopes = await GoogleAuthService.hasRequiredScopes(userId, [
        'https://www.googleapis.com/auth/drive'
      ]);

      if (!hasScopes) {
        throw new Error('User has not granted Drive access');
      }

      // Get authenticated API client
      const drive = await GoogleAuthService.getApiClient(userId, 'drive');

      // List files
      const response = await drive.files.list({
        pageSize: options.pageSize || 10,
        fields: 'nextPageToken, files(id, name, mimeType, modifiedTime)',
        q: options.query || null
      });

      return {
        success: true,
        files: response.data.files,
        nextPageToken: response.data.nextPageToken
      };
    } catch (error) {
      console.error('Error listing Drive files:', error);
      throw error;
    }
  }

  static async createFolder(userId, name, parentId = null) {
    try {
      const drive = await GoogleAuthService.getApiClient(userId, 'drive');

      const fileMetadata = {
        name: name,
        mimeType: 'application/vnd.google-apps.folder',
        parents: parentId ? [parentId] : undefined
      };

      const response = await drive.files.create({
        resource: fileMetadata,
        fields: 'id, name'
      });

      return {
        success: true,
        folder: response.data
      };
    } catch (error) {
      console.error('Error creating Drive folder:', error);
      throw error;
    }
  }
}
```

### Using Google Sheets API

```javascript
import { GoogleAuthService } from '../services/GoogleAuthService.js';

class SheetsService {
  static async createSpreadsheet(userId, title) {
    try {
      const sheets = await GoogleAuthService.getApiClient(userId, 'sheets');

      const response = await sheets.spreadsheets.create({
        resource: {
          properties: {
            title: title
          }
        }
      });

      return {
        success: true,
        spreadsheet: response.data
      };
    } catch (error) {
      console.error('Error creating spreadsheet:', error);
      throw error;
    }
  }

  static async readRange(userId, spreadsheetId, range) {
    try {
      const sheets = await GoogleAuthService.getApiClient(userId, 'sheets');

      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: spreadsheetId,
        range: range
      });

      return {
        success: true,
        values: response.data.values || []
      };
    } catch (error) {
      console.error('Error reading spreadsheet range:', error);
      throw error;
    }
  }

  static async writeRange(userId, spreadsheetId, range, values) {
    try {
      const sheets = await GoogleAuthService.getApiClient(userId, 'sheets');

      const response = await sheets.spreadsheets.values.update({
        spreadsheetId: spreadsheetId,
        range: range,
        valueInputOption: 'RAW',
        resource: {
          values: values
        }
      });

      return {
        success: true,
        updatedCells: response.data.updatedCells
      };
    } catch (error) {
      console.error('Error writing to spreadsheet:', error);
      throw error;
    }
  }
}
```

### Controller Integration Example

```javascript
import { GoogleAuthService } from '../services/GoogleAuthService.js';
import { DriveService } from '../services/DriveService.js';

class DriveController {
  static async listFiles(req, res) {
    try {
      const userId = req.user.id;
      const { pageSize, query } = req.query;

      // Check authentication status
      const authStatus = await GoogleAuthService.getAuthStatus(userId);
      if (!authStatus.isAuthenticated) {
        return res.status(401).json({
          success: false,
          message: 'Google Drive access not authorized',
          error: 'Please connect your Google account first'
        });
      }

      // List files
      const result = await DriveService.listFiles(userId, {
        pageSize: parseInt(pageSize) || 10,
        query: query
      });

      res.json({
        success: true,
        message: 'Files retrieved successfully',
        data: result
      });
    } catch (error) {
      console.error('Error in listFiles:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve files',
        error: error.message
      });
    }
  }

  static async createFolder(req, res) {
    try {
      const userId = req.user.id;
      const { name, parentId } = req.body;

      if (!name) {
        return res.status(400).json({
          success: false,
          message: 'Folder name is required'
        });
      }

      const result = await DriveService.createFolder(userId, name, parentId);

      res.json({
        success: true,
        message: 'Folder created successfully',
        data: result
      });
    } catch (error) {
      console.error('Error in createFolder:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create folder',
        error: error.message
      });
    }
  }
}

export { DriveController };
```

## Error Handling Examples

### Frontend Error Handling

```javascript
const handleGoogleApiCall = async (apiCall) => {
  try {
    const result = await apiCall();
    return result;
  } catch (error) {
    if (error.response?.status === 401) {
      // Token expired or invalid
      console.log('Google authentication required');
      // Redirect to auth flow
      window.location.href = '/google-auth';
    } else if (error.response?.status === 403) {
      // Insufficient permissions
      console.log('Insufficient Google permissions');
      // Show permission request dialog
    } else {
      console.error('Google API error:', error);
    }
    throw error;
  }
};
```

### Backend Error Handling

```javascript
const handleGoogleApiError = (error, res) => {
  if (error.code === 401) {
    return res.status(401).json({
      success: false,
      message: 'Google authentication expired',
      error: 'Please re-authenticate with Google'
    });
  } else if (error.code === 403) {
    return res.status(403).json({
      success: false,
      message: 'Insufficient Google permissions',
      error: 'Additional permissions required'
    });
  } else {
    return res.status(500).json({
      success: false,
      message: 'Google API error',
      error: error.message
    });
  }
};
```

## Testing Examples

### Unit Test Example

```javascript
import { GoogleAuthService } from '../services/GoogleAuthService.js';

describe('GoogleAuthService', () => {
  test('should generate valid auth URL', () => {
    const userId = 'test-user-123';
    const authUrl = GoogleAuthService.generateAuthUrl(userId);
    
    expect(authUrl).toContain('accounts.google.com');
    expect(authUrl).toContain(`state=${userId}`);
    expect(authUrl).toContain('scope=');
  });

  test('should validate scopes correctly', async () => {
    const userId = 'test-user-123';
    const requiredScopes = ['https://www.googleapis.com/auth/drive'];
    
    // Mock user with granted scopes
    const hasScopes = await GoogleAuthService.hasRequiredScopes(userId, requiredScopes);
    expect(hasScopes).toBe(true);
  });
});
```

This completes the Google OAuth integration with comprehensive documentation and examples!
