import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { LLMService } from '../src/services/LLMService.js';
import { LLMModelDataService } from '../src/services/LLMModelDataService.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config({ path: '.env' });

/**
 * Test LLM Models Implementation
 * This script tests the LLM models functionality
 */
async function testLLMModels() {
  try {
    logger.info('Starting LLM models test...');

    // Connect to databases
    await DatabaseManager.connectChatDatabase();
    logger.info('Connected to chat database');

    // Test 1: Initialize models (if not already done)
    logger.info('\n=== Test 1: Initialize Models ===');
    await LLMModelDataService.initializeModels();
    logger.info('✓ Models initialized successfully');

    // Test 2: Get all available models
    logger.info('\n=== Test 2: Get All Models ===');
    const allModels = await LLMService.getAvailableModels();
    logger.info(`✓ Retrieved ${allModels.length} models`);

    // Test 3: Get models by provider
    logger.info('\n=== Test 3: Get Models by Provider ===');
    const providers = ['OPENAI', 'ANTHROPIC', 'GOOGLE', 'DEEPSEEK', 'META'];
    
    for (const provider of providers) {
      try {
        const providerModels = await LLMService.getModelsByProvider(provider);
        logger.info(`✓ ${provider}: ${providerModels.length} models`);
        
        // Show first model details
        if (providerModels.length > 0) {
          const firstModel = providerModels[0];
          logger.info(`  - ${firstModel.displayName} (${firstModel.modelId})`);
          logger.info(`    Vision: ${firstModel.supportsVision}, Audio: ${firstModel.supportsAudio}, Code: ${firstModel.supportsCodeExecution}`);
        }
      } catch (error) {
        logger.warn(`⚠ ${provider}: No models found or error - ${error.message}`);
      }
    }

    // Test 4: Get models by capabilities
    logger.info('\n=== Test 4: Get Models by Capabilities ===');
    
    const visionModels = await LLMService.getModelsByCapabilities({ vision: true });
    logger.info(`✓ Vision-capable models: ${visionModels.length}`);
    
    const audioModels = await LLMService.getModelsByCapabilities({ audio: true });
    logger.info(`✓ Audio-capable models: ${audioModels.length}`);
    
    const codeModels = await LLMService.getModelsByCapabilities({ codeExecution: true });
    logger.info(`✓ Code execution models: ${codeModels.length}`);

    // Test 5: Get specific model info
    logger.info('\n=== Test 5: Get Specific Model Info ===');
    const testModelIds = ['gpt-4o', 'claude-sonnet-4-20250514', 'gemini-2.5-pro'];
    
    for (const modelId of testModelIds) {
      try {
        const modelInfo = await LLMService.getModelInfo(modelId);
        if (modelInfo) {
          logger.info(`✓ ${modelInfo.displayName}: ${modelInfo.description.substring(0, 50)}...`);
          logger.info(`  Context: ${modelInfo.contextWindow}, Max Output: ${modelInfo.maxOutputTokens}`);
        } else {
          logger.warn(`⚠ Model not found: ${modelId}`);
        }
      } catch (error) {
        logger.warn(`⚠ Error getting model ${modelId}: ${error.message}`);
      }
    }

    // Test 6: Summary statistics
    logger.info('\n=== Test 6: Summary Statistics ===');
    const summary = {
      totalModels: allModels.length,
      providers: {},
      capabilities: {
        vision: allModels.filter(m => m.supportsVision).length,
        audio: allModels.filter(m => m.supportsAudio).length,
        codeExecution: allModels.filter(m => m.supportsCodeExecution).length,
        multimodal: allModels.filter(m => m.isMultimodal).length,
      },
    };

    // Group by provider
    allModels.forEach(model => {
      if (!summary.providers[model.provider]) {
        summary.providers[model.provider] = 0;
      }
      summary.providers[model.provider]++;
    });

    logger.info('Summary:');
    logger.info(`  Total Models: ${summary.totalModels}`);
    logger.info('  By Provider:');
    Object.entries(summary.providers).forEach(([provider, count]) => {
      logger.info(`    ${provider}: ${count}`);
    });
    logger.info('  Capabilities:');
    Object.entries(summary.capabilities).forEach(([capability, count]) => {
      logger.info(`    ${capability}: ${count}`);
    });

    logger.info('\n✅ All tests completed successfully!');
    process.exit(0);
  } catch (error) {
    logger.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testLLMModels();
