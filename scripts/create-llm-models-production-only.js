import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { LLMModelDataService } from '../src/services/LLMModelDataService.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config({ path: '.env' });

/**
 * Create LLM Models on Production Server
 * This script uses the existing LLMModelDataService to create all models
 * Use this when you can't access your local database from production
 */
async function createLLMModelsOnProduction() {
  try {
    logger.info('=== Creating LLM Models on Production Server ===\n');

    // Connect to production database
    await DatabaseManager.connectChatDatabase();
    logger.info('Connected to production database');

    // Check if models already exist
    const { LLMService } = await import('../src/services/LLMService.js');
    const existingModels = await LLMService.getAvailableModels();
    
    if (existingModels.length > 0) {
      logger.info(`Found ${existingModels.length} existing models in database`);
      logger.info('Do you want to:');
      logger.info('1. Skip creation (models already exist)');
      logger.info('2. Update existing models with latest data');
      logger.info('3. Clear and recreate all models');
      
      // For automated execution, we'll update existing models
      logger.info('Proceeding with option 2: Update existing models');
    }

    // Initialize/update LLM models using the service
    await LLMModelDataService.initializeModels();
    logger.info('LLM models initialized/updated successfully');

    // Get summary of models
    const models = await LLMService.getAvailableModels();
    
    logger.info(`\nTotal models in database: ${models.length}`);
    
    // Group by provider
    const providerCounts = {};
    models.forEach(model => {
      providerCounts[model.provider] = (providerCounts[model.provider] || 0) + 1;
    });

    logger.info('\nModels by provider:');
    Object.entries(providerCounts).forEach(([provider, count]) => {
      logger.info(`  ${provider}: ${count} models`);
    });

    // Show sample models
    logger.info('\nSample models created:');
    const sampleProviders = ['OPENAI', 'ANTHROPIC', 'GOOGLE'];
    for (const provider of sampleProviders) {
      const providerModels = models.filter(m => m.provider === provider);
      if (providerModels.length > 0) {
        const sample = providerModels[0];
        logger.info(`  ${provider}: ${sample.displayName} (${sample.modelId})`);
        logger.info(`    - Vision: ${sample.supportsVision}, Audio: ${sample.supportsAudio}, Code: ${sample.supportsCodeExecution}`);
      }
    }

    logger.info('\n=== Production LLM Models Setup Completed Successfully! ===');
    
    // Important notes
    logger.info('\n⚠️  IMPORTANT POST-SETUP TASKS:');
    logger.info('1. Update API keys for each provider in the database');
    logger.info('2. Update logo URLs to use your production domain');
    logger.info('3. Test model functionality with your API keys');
    logger.info('4. Verify model capabilities work as expected');
    
    process.exit(0);
  } catch (error) {
    logger.error('Error creating LLM models on production:', error);
    process.exit(1);
  }
}

// Run the creation
createLLMModelsOnProduction();
