#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to run subscription system migration
 * This script will create the subscription tables and insert default plans
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';
import dotenv from 'dotenv';
import mysql from 'mysql2/promise';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '..', '.env') });

class SubscriptionMigrator {
  constructor() {
    this.connection = null;
  }

  /**
   * Initialize database connection
   */
  async initialize() {
    try {
      this.connection = await mysql.createConnection({
        host: process.env.USER_DB_HOST || 'localhost',
        port: parseInt(process.env.USER_DB_PORT || '3306'),
        user: process.env.USER_DB_USERNAME || 'inf_ai_user',
        password: process.env.USER_DB_PASSWORD || 'inf_ai_user',
        database: process.env.USER_DB_NAME || 'infini_ai_users',
        multipleStatements: true,
      });

      console.log('✅ Connected to database successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to connect to database:', error.message);
      return false;
    }
  }

  /**
   * Run the migration
   */
  async migrate() {
    try {
      console.log('🚀 Starting subscription system migration...\n');

      // Read migration SQL file
      const migrationPath = join(__dirname, '..', 'migrations', 'create_subscription_tables.sql');
      const migrationSQL = readFileSync(migrationPath, 'utf8');

      console.log('📄 Executing migration SQL...');
      
      // Execute the migration
      const [results] = await this.connection.execute(migrationSQL);
      
      console.log('✅ Migration executed successfully\n');

      // Verify tables were created
      await this.verifyMigration();

      console.log('\n🎉 Subscription system migration completed successfully!');
      
    } catch (error) {
      console.error('💥 Migration failed:', error.message);
      throw error;
    }
  }

  /**
   * Verify migration was successful
   */
  async verifyMigration() {
    try {
      console.log('🔍 Verifying migration...');

      // Check if tables exist
      const tables = ['subscription_plans', 'user_subscriptions', 'payment_transactions', 'credit_resets'];
      
      for (const table of tables) {
        const [rows] = await this.connection.execute(
          'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
          [process.env.USER_DB_NAME || 'infini_ai_users', table]
        );
        
        if (rows[0].count > 0) {
          console.log(`  ✅ Table '${table}' created successfully`);
        } else {
          throw new Error(`Table '${table}' was not created`);
        }
      }

      // Check if default plans were inserted
      const [planRows] = await this.connection.execute('SELECT COUNT(*) as count FROM subscription_plans');
      console.log(`  ✅ ${planRows[0].count} subscription plans inserted`);

      // Show plan details
      const [plans] = await this.connection.execute(
        'SELECT plan_type, name, price, billing_cycle FROM subscription_plans ORDER BY sort_order'
      );
      
      console.log('\n📋 Subscription Plans:');
      plans.forEach(plan => {
        console.log(`  • ${plan.name} (${plan.plan_type}): ₹${plan.price}/${plan.billing_cycle.toLowerCase()}`);
      });

      // Check user profile updates
      const [profileRows] = await this.connection.execute(
        'SELECT plan, COUNT(*) as count FROM user_profiles GROUP BY plan'
      );
      
      if (profileRows.length > 0) {
        console.log('\n👥 User Profile Plan Distribution:');
        profileRows.forEach(row => {
          console.log(`  • ${row.plan}: ${row.count} users`);
        });
      }

    } catch (error) {
      console.error('❌ Verification failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    if (this.connection) {
      await this.connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

/**
 * Main function
 */
async function main() {
  const migrator = new SubscriptionMigrator();

  try {
    const initialized = await migrator.initialize();
    if (!initialized) {
      process.exit(1);
    }

    await migrator.migrate();
    console.log('\n🏁 Migration script completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Migration script failed:', error);
    process.exit(1);
  } finally {
    await migrator.cleanup();
  }
}

// Run the migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { SubscriptionMigrator };
