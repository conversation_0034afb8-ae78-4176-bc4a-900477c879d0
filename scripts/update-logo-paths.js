import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { LLMModel } from '../src/models/chat/LLMModel.js';
import logger from '../src/config/logger.js';
import fs from 'fs/promises';
import path from 'path';

// Load environment variables
dotenv.config({ path: '.env' });

/**
 * Update LLM Logo Paths Script
 * This script updates the database to serve logos from public/assets/logos folder
 */

// Logo file mapping based on the files in public/assets/logos
const LOGO_FILE_MAPPING = {
  OPENAI: 'http://localhost:5529/assets/logos/gpt-35.webp',      // Using gpt-35.webp for all OpenAI models
  ANTHROPIC: 'http://localhost:5529/assets/logos/claude.webp',   // Using claude.webp for all Anthropic models
  GOOGLE: 'http://localhost:5529/assets/logos/gemini.png',       // Using gemini.png for all Google models
  DEEPSEEK: 'http://localhost:5529/assets/logos/deepseek.png',   // Using deepseek.png for all DeepSeek models
  META: 'http://localhost:5529/assets/logos/llama.png'           // Using llama.png for all Meta models
};

/**
 * Check if logo files exist
 */
async function checkLogoFiles() {
  const publicDir = path.join(process.cwd(), 'public');
  const missingFiles = [];

  for (const [provider, logoPath] of Object.entries(LOGO_FILE_MAPPING)) {
    const fullPath = path.join(publicDir, logoPath);
    try {
      await fs.access(fullPath);
      logger.info(`✓ Found logo file for ${provider}: ${logoPath}`);
    } catch (error) {
      logger.warn(`✗ Missing logo file for ${provider}: ${logoPath}`);
      missingFiles.push({ provider, path: logoPath });
    }
  }

  return missingFiles;
}

/**
 * Update LLM models with new logo paths
 * @param {string} provider - Provider name
 * @param {string} logoPath - Logo file path
 */
async function updateProviderLogos(provider, logoPath) {
  try {
    const models = await LLMModel.findAll({
      where: { provider }
    });

    if (models.length === 0) {
      logger.warn(`No models found for provider: ${provider}`);
      return;
    }

    let updatedCount = 0;
    for (const model of models) {
      await model.update({
        logoUrl: logoPath,
        logoAttachmentSecureId: null // Clear the secure ID since we're using direct paths
      });
      updatedCount++;
      logger.info(`Updated model ${model.modelId} with logo path: ${logoPath}`);
    }

    logger.info(`✅ Updated ${updatedCount} models for provider ${provider}`);
  } catch (error) {
    logger.error(`❌ Failed to update models for provider ${provider}:`, error);
    throw error;
  }
}

/**
 * Update all logo paths in the database
 */
async function updateLogoPaths() {
  try {
    logger.info('Starting LLM logo path update process...');

    // Connect to database
    await DatabaseManager.connectChatDatabase();
    logger.info('Connected to chat database');

    // Check if logo files exist
    logger.info('Checking logo files...');
    const missingFiles = await checkLogoFiles();
    
    if (missingFiles.length > 0) {
      logger.warn(`Found ${missingFiles.length} missing logo files:`);
      missingFiles.forEach(({ provider, path }) => {
        logger.warn(`  ${provider}: ${path}`);
      });
      logger.warn('Continuing with available files...');
    }

    // Process each provider
    for (const [provider, logoPath] of Object.entries(LOGO_FILE_MAPPING)) {
      try {
        logger.info(`Updating ${provider} logo path...`);
        await updateProviderLogos(provider, logoPath);
        logger.info(`✅ Successfully updated ${provider} logo path`);
      } catch (error) {
        logger.error(`❌ Failed to update ${provider} logo path:`, error);
        // Continue with other providers
      }
    }

    logger.info('🎉 LLM logo path update process completed!');
    
    // Show summary
    const { Op } = await import('sequelize');
    const modelsWithLogos = await LLMModel.findAll({
      where: {
        logoUrl: { [Op.ne]: null }
      }
    });
    
    logger.info(`📊 Summary: ${modelsWithLogos.length} models now have logo paths`);
    
    // Group by provider for summary
    const providerCounts = {};
    modelsWithLogos.forEach(model => {
      providerCounts[model.provider] = (providerCounts[model.provider] || 0) + 1;
    });

    Object.entries(providerCounts).forEach(([provider, count]) => {
      const logoPath = LOGO_FILE_MAPPING[provider];
      logger.info(`  ${provider}: ${count} models → ${logoPath}`);
    });

    logger.info('\n📝 Next steps:');
    logger.info('1. Test logo access via the updated paths');
    logger.info('2. Update frontend to use the new logoUrl paths');
    logger.info('3. Verify all logo files are accessible from the web server');
    
    // Show example URLs
    logger.info('\n🌐 Example logo URLs:');
    Object.entries(LOGO_FILE_MAPPING).forEach(([provider, path]) => {
      logger.info(`  ${provider}: http://localhost:3000${path}`);
    });
    
    process.exit(0);
  } catch (error) {
    logger.error('Error in logo path update process:', error);
    process.exit(1);
  }
}

// Run the update
updateLogoPaths();
