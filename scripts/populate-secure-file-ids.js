#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to populate secure file IDs for existing chat messages
 * This script should be run after the database migration to add the attachment_secure_id column
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '..', '.env') });

import { ChatMessage } from '../src/models/chat/ChatMessage.js';
import { S3Service } from '../src/services/S3Service.js';
import logger from '../src/config/logger.js';

class SecureFileIdPopulator {
  constructor() {
    this.processedCount = 0;
    this.skippedCount = 0;
    this.errorCount = 0;
  }

  /**
   * Run the population process
   */
  async run() {
    try {
      console.log('🚀 Starting secure file ID population...');
      
      // Initialize S3 service
      await S3Service.initialize();
      
      // Find all chat messages with attachments but no secure file ID
      const messages = await ChatMessage.findAll({
        where: {
          attachmentName: { [ChatMessage.sequelize.Sequelize.Op.ne]: null },
          attachmentSecureId: null
        },
        order: [['createdAt', 'ASC']]
      });

      console.log(`📊 Found ${messages.length} messages to process`);

      if (messages.length === 0) {
        console.log('✅ No messages need secure file ID population');
        return;
      }

      // Process messages in batches
      const batchSize = 100;
      for (let i = 0; i < messages.length; i += batchSize) {
        const batch = messages.slice(i, i + batchSize);
        await this.processBatch(batch);
        
        console.log(`📈 Progress: ${Math.min(i + batchSize, messages.length)}/${messages.length} messages processed`);
      }

      console.log('\n🎉 Population completed!');
      console.log(`✅ Processed: ${this.processedCount}`);
      console.log(`⏭️  Skipped: ${this.skippedCount}`);
      console.log(`❌ Errors: ${this.errorCount}`);

    } catch (error) {
      console.error('💥 Fatal error during population:', error);
      process.exit(1);
    }
  }

  /**
   * Process a batch of messages
   */
  async processBatch(messages) {
    const promises = messages.map(message => this.processMessage(message));
    await Promise.allSettled(promises);
  }

  /**
   * Process a single message
   */
  async processMessage(message) {
    try {
      // Generate secure file ID based on storage type
      let secureFileId;
      
      if (message.attachmentStorageType === 's3' && message.attachmentS3Key) {
        // For S3 files, generate secure ID and store mapping
        secureFileId = S3Service.generateSecureFileId(message.attachmentS3Key, message.attachmentName);
        
        // Store the mapping in S3Service
        S3Service.storeSecureFileMapping(
          secureFileId,
          message.attachmentS3Key,
          message.attachmentName,
          message.attachmentType
        );
      } else if (message.attachmentStorageType === 'local' && message.attachmentName) {
        // For local files, generate secure ID
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        const fileExtension = message.attachmentName.split('.').pop();
        const baseName = message.attachmentName.split('.').slice(0, -1).join('.');
        const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9-_]/g, '_');
        secureFileId = `${timestamp}_${randomSuffix}_${sanitizedBaseName}.${fileExtension}`;
      } else {
        console.log(`⚠️  Skipping message ${message.id}: No valid attachment storage info`);
        this.skippedCount++;
        return;
      }

      // Update the message with secure file ID
      await message.update({
        attachmentSecureId: secureFileId
      });

      console.log(`✅ Updated message ${message.id} with secure file ID: ${secureFileId}`);
      this.processedCount++;

    } catch (error) {
      console.error(`❌ Error processing message ${message.id}:`, error.message);
      this.errorCount++;
    }
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const populator = new SecureFileIdPopulator();
  populator.run()
    .then(() => {
      console.log('🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { SecureFileIdPopulator };
