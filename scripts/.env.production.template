# Database Configuration for LLM Models Migration
# Copy this file to .env.production and fill in your database details

# === OPTION 1: Running from Local Machine ===
# Source Database (your local database with existing models)
SOURCE_DB_HOST=localhost
SOURCE_DB_USER=root
SOURCE_DB_PASSWORD=your-local-password
SOURCE_DB_NAME=infini_ai_user_chat_recs
SOURCE_DB_PORT=3306

# Target Database (production database where models will be created)
TARGET_DB_HOST=your-production-db-host
TARGET_DB_USER=your-production-db-user
TARGET_DB_PASSWORD=your-production-db-password
TARGET_DB_NAME=infini_ai_user_chat_recs
TARGET_DB_PORT=3306

# === OPTION 2: Running on Production Server ===
# If both databases are accessible from production server:
# SOURCE_DB_HOST=your-local-db-external-ip
# SOURCE_DB_USER=your-local-db-user
# SOURCE_DB_PASSWORD=your-local-db-password
# SOURCE_DB_NAME=infini_ai_user_chat_recs
# SOURCE_DB_PORT=3306

# TARGET_DB_HOST=localhost
# TARGET_DB_USER=root
# TARGET_DB_PASSWORD=your-production-password
# TARGET_DB_NAME=infini_ai_user_chat_recs
# TARGET_DB_PORT=3306

# === OPTION 3: Legacy Environment Variables ===
# The script also supports the old format:
# CHAT_DB_HOST=source-db-host (for source)
# PROD_CHAT_DB_HOST=target-db-host (for target)
