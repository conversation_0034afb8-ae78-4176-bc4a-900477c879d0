#!/usr/bin/env node

/**
 * Test script for subscription system
 * This script tests the subscription system functionality
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '..', '.env') });

import { DatabaseManager } from '../src/config/database.js';
import { SubscriptionService } from '../src/services/SubscriptionService.js';
import { RazorpayService } from '../src/services/RazorpayService.js';
import logger from '../src/config/logger.js';

class SubscriptionSystemTester {
  constructor() {
    this.testResults = [];
  }

  /**
   * Initialize the test environment
   */
  async initialize() {
    try {
      console.log('🚀 Initializing subscription system test...\n');

      // Load models
      await import('../src/models/user/index.js');
      await import('../src/models/subscription/index.js');

      // Connect to database
      await DatabaseManager.connectAllDatabases();
      
      // Sync database (create tables)
      await DatabaseManager.syncAllDatabases(false);

      // Initialize services
      RazorpayService.initialize();
      
      console.log('✅ Test environment initialized successfully\n');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize test environment:', error.message);
      return false;
    }
  }

  /**
   * Test subscription plan initialization
   */
  async testPlanInitialization() {
    try {
      console.log('📋 Testing subscription plan initialization...');

      await SubscriptionService.initializeDefaultPlans();
      const plans = await SubscriptionService.getAvailablePlans();

      console.log(`✅ ${plans.length} subscription plans initialized:`);
      plans.forEach(plan => {
        console.log(`  • ${plan.name} (${plan.planType}): ₹${plan.price}/${plan.billingCycle.toLowerCase()}`);
      });

      this.testResults.push({
        test: 'Plan Initialization',
        status: 'PASSED',
        details: `${plans.length} plans created`
      });

      return true;
    } catch (error) {
      console.error('❌ Plan initialization test failed:', error.message);
      this.testResults.push({
        test: 'Plan Initialization',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test Razorpay service
   */
  async testRazorpayService() {
    try {
      console.log('\n💳 Testing Razorpay service...');

      const razorpayService = RazorpayService.getInstance();
      
      // Test amount conversion
      const amountInPaise = RazorpayService.convertToPaise(1299);
      const amountInRupees = RazorpayService.convertFromPaise(129900);

      console.log(`✅ Amount conversion test:`);
      console.log(`  • ₹1299 = ${amountInPaise} paise`);
      console.log(`  • 129900 paise = ₹${amountInRupees}`);

      // Test signature verification (with dummy data)
      const isValidSignature = razorpayService.verifyPaymentSignature({
        razorpay_order_id: 'order_test',
        razorpay_payment_id: 'pay_test',
        razorpay_signature: 'invalid_signature'
      });

      console.log(`✅ Signature verification test: ${isValidSignature ? 'Valid' : 'Invalid (expected)'}`);

      this.testResults.push({
        test: 'Razorpay Service',
        status: 'PASSED',
        details: 'Service initialized and utility functions working'
      });

      return true;
    } catch (error) {
      console.error('❌ Razorpay service test failed:', error.message);
      this.testResults.push({
        test: 'Razorpay Service',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test database models
   */
  async testDatabaseModels() {
    try {
      console.log('\n🗄️  Testing database models...');

      const { SubscriptionPlan, UserSubscription, PaymentTransaction, CreditReset } = 
        await import('../src/models/subscription/index.js');

      // Test SubscriptionPlan model
      const explorerPlan = await SubscriptionPlan.findByType('EXPLORER');
      if (explorerPlan) {
        console.log(`✅ SubscriptionPlan model: Found ${explorerPlan.name}`);
      }

      // Test model methods
      const plans = await SubscriptionPlan.getActivePlans();
      console.log(`✅ Model methods: Retrieved ${plans.length} active plans`);

      // Test plan features and limits
      const creatorPlan = await SubscriptionPlan.findByType('CREATOR');
      if (creatorPlan) {
        const features = creatorPlan.getPlanFeatures();
        const limits = creatorPlan.getPlanLimits();
        console.log(`✅ Plan data: Features and limits parsed successfully`);
      }

      this.testResults.push({
        test: 'Database Models',
        status: 'PASSED',
        details: 'All models and methods working correctly'
      });

      return true;
    } catch (error) {
      console.error('❌ Database models test failed:', error.message);
      this.testResults.push({
        test: 'Database Models',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test API endpoints (mock)
   */
  async testAPIEndpoints() {
    try {
      console.log('\n🌐 Testing API endpoint structure...');

      // Import controllers to verify they exist
      const { SubscriptionController } = await import('../src/controllers/SubscriptionController.js');
      const { PaymentController } = await import('../src/controllers/PaymentController.js');

      console.log('✅ SubscriptionController imported successfully');
      console.log('✅ PaymentController imported successfully');

      // Import routes to verify they exist
      const subscriptionRoutes = await import('../src/routes/subscription.js');
      const paymentRoutes = await import('../src/routes/payment.js');

      console.log('✅ Subscription routes configured');
      console.log('✅ Payment routes configured');

      this.testResults.push({
        test: 'API Endpoints',
        status: 'PASSED',
        details: 'All controllers and routes properly configured'
      });

      return true;
    } catch (error) {
      console.error('❌ API endpoints test failed:', error.message);
      this.testResults.push({
        test: 'API Endpoints',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test environment configuration
   */
  async testEnvironmentConfig() {
    try {
      console.log('\n⚙️  Testing environment configuration...');

      const requiredEnvVars = [
        'RAZORPAY_KEY_ID',
        'RAZORPAY_KEY_SECRET',
        'RAZORPAY_WEBHOOK_SECRET'
      ];

      const missingVars = [];
      requiredEnvVars.forEach(varName => {
        if (!process.env[varName] || process.env[varName].includes('your_')) {
          missingVars.push(varName);
        }
      });

      if (missingVars.length > 0) {
        console.log('⚠️  Missing or placeholder environment variables:');
        missingVars.forEach(varName => {
          console.log(`  • ${varName}`);
        });
        console.log('   Please update these in your .env file for production use');
      } else {
        console.log('✅ All Razorpay environment variables configured');
      }

      this.testResults.push({
        test: 'Environment Config',
        status: missingVars.length === 0 ? 'PASSED' : 'WARNING',
        details: missingVars.length === 0 ? 'All variables configured' : `${missingVars.length} variables need configuration`
      });

      return true;
    } catch (error) {
      console.error('❌ Environment config test failed:', error.message);
      this.testResults.push({
        test: 'Environment Config',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Subscription System Tests\n');
    console.log('=' .repeat(50));

    const tests = [
      () => this.testPlanInitialization(),
      () => this.testRazorpayService(),
      () => this.testDatabaseModels(),
      () => this.testAPIEndpoints(),
      () => this.testEnvironmentConfig(),
    ];

    for (const test of tests) {
      await test();
    }

    this.printTestResults();
  }

  /**
   * Print test results summary
   */
  printTestResults() {
    console.log('\n' + '=' .repeat(50));
    console.log('📊 Test Results Summary\n');

    let passed = 0;
    let failed = 0;
    let warnings = 0;

    this.testResults.forEach(result => {
      const icon = result.status === 'PASSED' ? '✅' : 
                   result.status === 'WARNING' ? '⚠️' : '❌';
      console.log(`${icon} ${result.test}: ${result.status}`);
      console.log(`   ${result.details}\n`);

      if (result.status === 'PASSED') passed++;
      else if (result.status === 'WARNING') warnings++;
      else failed++;
    });

    console.log('=' .repeat(50));
    console.log(`📈 Summary: ${passed} passed, ${warnings} warnings, ${failed} failed`);

    if (failed === 0) {
      console.log('🎉 Subscription system is ready for use!');
      console.log('\n📝 Next steps:');
      console.log('1. Configure Razorpay API keys in .env file');
      console.log('2. Set up webhook endpoint in Razorpay dashboard');
      console.log('3. Test with Razorpay test cards');
      console.log('4. Start accepting subscriptions!');
    } else {
      console.log('⚠️  Please fix the failed tests before using the subscription system');
    }
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    // Close database connections if needed
    console.log('\n🧹 Cleaning up test environment...');
  }
}

/**
 * Main function
 */
async function main() {
  const tester = new SubscriptionSystemTester();

  try {
    const initialized = await tester.initialize();
    if (!initialized) {
      process.exit(1);
    }

    await tester.runAllTests();
    
  } catch (error) {
    console.error('\n💥 Test script failed:', error);
    process.exit(1);
  } finally {
    await tester.cleanup();
  }
}

// Run the tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { SubscriptionSystemTester };
