#!/usr/bin/env node

/**
 * Test script for SpreadsheetsAgent
 * This script demonstrates the functionality of the SpreadsheetsAgent
 */

import { SpreadsheetsAgent } from '../src/services/SpreadsheetsAgent.js';
import { SpreadsheetsAgentService } from '../src/services/SpreadsheetsAgentService.js';
import { config } from 'dotenv';
import logger from '../src/config/logger.js';

// Load environment variables
config();

class SpreadsheetsAgentTester {
  constructor() {
    this.agent = new SpreadsheetsAgent();
    this.service = new SpreadsheetsAgentService();
    this.testUserId = 'test-user-123'; // Replace with actual user ID for testing
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting SpreadsheetsAgent Tests\n');

    try {
      await this.testAgentStatus();
      await this.testAgentStructure();
      await this.testToolsStructure();
      await this.testServiceMethods();
      await this.testNaturalLanguageProcessing();
      await this.testErrorHandling();

      console.log('\n✅ All tests completed successfully!');
    } catch (error) {
      console.error('\n❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Test agent status and health
   */
  async testAgentStatus() {
    console.log('📊 Testing agent status...');

    try {
      const status = this.agent.getStatus();
      
      console.log('✅ Agent status retrieved');
      console.log(`   - Status: ${status.status}`);
      console.log(`   - Tools count: ${status.tools.length}`);
      console.log(`   - Graph initialized: ${status.graphInitialized}`);
      
      if (status.tools.length !== 5) {
        throw new Error(`Expected 5 tools, got ${status.tools.length}`);
      }

      console.log('✅ Agent status test passed\n');
    } catch (error) {
      console.error('❌ Agent status test failed:', error.message);
      throw error;
    }
  }

  /**
   * Test agent structure and initialization
   */
  async testAgentStructure() {
    console.log('🏗️  Testing agent structure...');

    try {
      // Check if agent has required properties
      if (!this.agent.graph) {
        throw new Error('Agent graph not initialized');
      }

      if (!this.agent.tools || this.agent.tools.length === 0) {
        throw new Error('Agent tools not initialized');
      }

      // Check if all required tools exist
      const requiredTools = [
        'get_all_spreadsheets',
        'get_spreadsheet_details',
        'read_spreadsheet_content',
        'update_spreadsheet_content',
        'create_spreadsheet'
      ];

      const toolNames = this.agent.tools.map(tool => tool.name);
      
      requiredTools.forEach(toolName => {
        if (!toolNames.includes(toolName)) {
          throw new Error(`Required tool ${toolName} not found`);
        }
      });

      console.log('✅ Agent structure test passed\n');
    } catch (error) {
      console.error('❌ Agent structure test failed:', error.message);
      throw error;
    }
  }

  /**
   * Test tools structure and schemas
   */
  async testToolsStructure() {
    console.log('🔧 Testing tools structure...');

    try {
      this.agent.tools.forEach(tool => {
        if (!tool.name) {
          throw new Error('Tool missing name');
        }
        
        if (!tool.description) {
          throw new Error(`Tool ${tool.name} missing description`);
        }

        if (!tool.schema) {
          throw new Error(`Tool ${tool.name} missing schema`);
        }

        console.log(`   ✅ Tool ${tool.name} structure valid`);
      });

      console.log('✅ Tools structure test passed\n');
    } catch (error) {
      console.error('❌ Tools structure test failed:', error.message);
      throw error;
    }
  }

  /**
   * Test service methods
   */
  async testServiceMethods() {
    console.log('🔄 Testing service methods...');

    try {
      // Test service status
      const serviceStatus = this.service.getStatus();
      if (!serviceStatus.status) {
        throw new Error('Service status not available');
      }

      // Test available operations
      const operations = this.service.getAvailableOperations();
      if (!Array.isArray(operations) || operations.length === 0) {
        throw new Error('Service operations not available');
      }

      console.log(`   ✅ Service has ${operations.length} available operations`);
      
      operations.forEach(op => {
        console.log(`   - ${op.name}: ${op.description}`);
      });

      console.log('✅ Service methods test passed\n');
    } catch (error) {
      console.error('❌ Service methods test failed:', error.message);
      throw error;
    }
  }

  /**
   * Test natural language processing (mock test)
   */
  async testNaturalLanguageProcessing() {
    console.log('🧠 Testing natural language processing...');

    try {
      // Test input validation
      try {
        await this.agent.processRequest('', 'test message');
        throw new Error('Should have failed with empty user ID');
      } catch (error) {
        if (!error.message.includes('User ID is required')) {
          throw error;
        }
        console.log('   ✅ Input validation working');
      }

      try {
        await this.agent.processRequest('test-user', '');
        throw new Error('Should have failed with empty message');
      } catch (error) {
        if (!error.message.includes('Message is required')) {
          throw error;
        }
        console.log('   ✅ Message validation working');
      }

      console.log('✅ Natural language processing test passed\n');
    } catch (error) {
      console.error('❌ Natural language processing test failed:', error.message);
      throw error;
    }
  }

  /**
   * Test error handling
   */
  async testErrorHandling() {
    console.log('🛡️  Testing error handling...');

    try {
      // Test invalid input handling
      const result = await this.agent.processRequest(null, 'test message');
      
      if (result.success !== false) {
        throw new Error('Expected error response for invalid input');
      }

      if (!result.error || !result.message) {
        throw new Error('Error response missing required fields');
      }

      console.log('   ✅ Invalid input error handling working');
      console.log(`   - Error type: ${result.error}`);
      console.log(`   - Error message: ${result.message}`);

      console.log('✅ Error handling test passed\n');
    } catch (error) {
      console.error('❌ Error handling test failed:', error.message);
      throw error;
    }
  }

  /**
   * Test agent reset functionality
   */
  async testAgentReset() {
    console.log('🔄 Testing agent reset...');

    try {
      // Reset the agent
      this.agent.reset();
      
      // Check if agent is still functional after reset
      const status = this.agent.getStatus();
      if (!status.graphInitialized) {
        throw new Error('Agent not properly reinitialized after reset');
      }

      console.log('✅ Agent reset test passed\n');
    } catch (error) {
      console.error('❌ Agent reset test failed:', error.message);
      throw error;
    }
  }

  /**
   * Display test summary
   */
  displayTestSummary() {
    console.log('📋 Test Summary:');
    console.log('================');
    console.log('✅ Agent Status Test');
    console.log('✅ Agent Structure Test');
    console.log('✅ Tools Structure Test');
    console.log('✅ Service Methods Test');
    console.log('✅ Natural Language Processing Test');
    console.log('✅ Error Handling Test');
    console.log('\n🎉 SpreadsheetsAgent is ready for use!');
    console.log('\n📚 Next steps:');
    console.log('1. Ensure Google OAuth is configured');
    console.log('2. Test with real user authentication');
    console.log('3. Verify Google Sheets API permissions');
    console.log('4. Test with actual spreadsheet operations');
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new SpreadsheetsAgentTester();
  
  tester.runAllTests()
    .then(() => {
      tester.displayTestSummary();
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test execution failed:', error);
      process.exit(1);
    });
}

export { SpreadsheetsAgentTester };
