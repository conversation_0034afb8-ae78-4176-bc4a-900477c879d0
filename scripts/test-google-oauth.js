import dotenv from 'dotenv';
import { GoogleAuthService } from '../src/services/GoogleAuthService.js';
import { UserGoogleAuth, User } from '../src/models/user/index.js';
import { DatabaseManager } from '../src/config/database.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config();

/**
 * Test Google OAuth Implementation
 * This script tests the Google OAuth service without requiring actual Google credentials
 */
class GoogleOAuthTester {
  static async runTests() {
    try {
      console.log('🚀 Starting Google OAuth Implementation Tests...\n');

      // Connect to database
      await DatabaseManager.connectAllDatabases();
      await DatabaseManager.syncAllDatabases();

      // Test 1: Service Configuration
      await this.testServiceConfiguration();

      // Test 2: Database Model
      await this.testDatabaseModel();

      // Test 3: Auth URL Generation
      await this.testAuthUrlGeneration();

      // Test 4: Scope Validation
      await this.testScopeValidation();

      console.log('\n✅ All tests completed successfully!');
      console.log('\n📋 Next Steps:');
      console.log('1. Set up Google OAuth credentials in Google Cloud Console');
      console.log('2. Add GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, and GOOGLE_REDIRECT_URI to .env');
      console.log('3. Test the complete OAuth flow with real credentials');
      console.log('4. Use the API endpoints to integrate with your frontend');

    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      await DatabaseManager.closeAllConnections();
      process.exit(0);
    }
  }

  static async testServiceConfiguration() {
    console.log('🔧 Testing Service Configuration...');

    // Test scopes are properly defined
    const scopes = GoogleAuthService.SCOPES;
    console.log(`   ✓ Found ${Object.keys(scopes).length} OAuth scopes`);

    // Test default scopes
    const defaultScopes = GoogleAuthService.DEFAULT_SCOPES;
    console.log(`   ✓ Default scopes configured: ${defaultScopes.length} scopes`);

    // Verify all required scopes are present
    const requiredScopes = ['DRIVE', 'SHEETS', 'DOCS', 'CALENDAR', 'SLIDES'];
    for (const scope of requiredScopes) {
      if (!scopes[scope]) {
        throw new Error(`Missing required scope: ${scope}`);
      }
    }
    console.log('   ✓ All required scopes are configured');

    console.log('   ✅ Service configuration test passed\n');
  }

  static async testDatabaseModel() {
    console.log('🗄️  Testing Database Model...');

    // Create a test user first
    const testUserId = 'test-user-123';
    const testUser = await User.createUser({
      email: `test-${Date.now()}@example.com`,
      isVerified: true
    });
    const actualUserId = testUser.id;

    // Test model creation
    const testAuthData = {
      userId: actualUserId,
      accessToken: 'test-access-token',
      refreshToken: 'test-refresh-token',
      accessTokenExpiresAt: new Date(Date.now() + 3600000), // 1 hour from now
      grantedScopes: 'https://www.googleapis.com/auth/drive https://www.googleapis.com/auth/sheets',
      userInfo: {
        id: 'google-user-123',
        email: '<EMAIL>',
        name: 'Test User'
      }
    };

    // Create auth record
    const authRecord = await UserGoogleAuth.createOrUpdate(testAuthData);
    console.log('   ✓ Auth record created successfully');

    // Test retrieval
    const retrievedRecord = await UserGoogleAuth.findByUserId(actualUserId);
    if (!retrievedRecord) {
      throw new Error('Failed to retrieve auth record');
    }
    console.log('   ✓ Auth record retrieved successfully');

    // Test scope checking
    const hasScope = retrievedRecord.hasScope('https://www.googleapis.com/auth/drive');
    if (!hasScope) {
      throw new Error('Scope checking failed');
    }
    console.log('   ✓ Scope checking works correctly');

    // Test token expiry checking
    const isExpired = retrievedRecord.isAccessTokenExpired();
    if (isExpired) {
      throw new Error('Token should not be expired');
    }
    console.log('   ✓ Token expiry checking works correctly');

    // Test JSON serialization (should exclude tokens)
    const jsonData = retrievedRecord.toJSON();
    if (jsonData.accessToken || jsonData.refreshToken) {
      throw new Error('Tokens should be excluded from JSON output');
    }
    console.log('   ✓ JSON serialization excludes sensitive tokens');

    // Clean up test data
    await UserGoogleAuth.revokeAuth(actualUserId);
    await testUser.destroy();
    console.log('   ✓ Test data cleaned up');

    console.log('   ✅ Database model test passed\n');
  }

  static async testAuthUrlGeneration() {
    console.log('🔗 Testing Auth URL Generation...');

    // Mock environment variables for testing
    const originalClientId = process.env.GOOGLE_CLIENT_ID;
    const originalClientSecret = process.env.GOOGLE_CLIENT_SECRET;
    const originalRedirectUri = process.env.GOOGLE_REDIRECT_URI;

    process.env.GOOGLE_CLIENT_ID = 'test-client-id';
    process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
    process.env.GOOGLE_REDIRECT_URI = 'http://localhost:5529/api/google-auth/callback';

    try {
      const testUserId = 'test-user-456';
      const authUrl = GoogleAuthService.generateAuthUrl(testUserId);

      // Verify URL contains required parameters
      if (!authUrl.includes('client_id=test-client-id')) {
        throw new Error('Auth URL missing client_id');
      }
      if (!authUrl.includes('redirect_uri=')) {
        throw new Error('Auth URL missing redirect_uri');
      }
      if (!authUrl.includes('scope=')) {
        throw new Error('Auth URL missing scope');
      }
      if (!authUrl.includes(`state=${testUserId}`)) {
        throw new Error('Auth URL missing state parameter');
      }

      console.log('   ✓ Auth URL generated with correct parameters');
      console.log(`   ✓ URL length: ${authUrl.length} characters`);

      // Test custom scopes
      const customScopes = [GoogleAuthService.SCOPES.DRIVE_READONLY];
      const customAuthUrl = GoogleAuthService.generateAuthUrl(testUserId, customScopes);
      if (!customAuthUrl.includes('drive.readonly')) {
        throw new Error('Custom scopes not applied correctly');
      }
      console.log('   ✓ Custom scopes work correctly');

    } finally {
      // Restore original environment variables
      process.env.GOOGLE_CLIENT_ID = originalClientId;
      process.env.GOOGLE_CLIENT_SECRET = originalClientSecret;
      process.env.GOOGLE_REDIRECT_URI = originalRedirectUri;
    }

    console.log('   ✅ Auth URL generation test passed\n');
  }

  static async testScopeValidation() {
    console.log('🔍 Testing Scope Validation...');

    // Create a test user first
    const testUser = await User.createUser({
      email: `test2-${Date.now()}@example.com`,
      isVerified: true
    });
    const testUserId = testUser.id;

    // Test individual scope validation
    const testAuthData = {
      userId: testUserId,
      accessToken: 'test-token',
      refreshToken: 'test-refresh',
      accessTokenExpiresAt: new Date(Date.now() + 3600000),
      grantedScopes: 'https://www.googleapis.com/auth/drive https://www.googleapis.com/auth/sheets https://www.googleapis.com/auth/calendar',
      userInfo: { id: 'test', email: '<EMAIL>' }
    };

    const authRecord = await UserGoogleAuth.createOrUpdate(testAuthData);

    // Test hasScope method
    const hasDriveScope = authRecord.hasScope('https://www.googleapis.com/auth/drive');
    const hasDocsScope = authRecord.hasScope('https://www.googleapis.com/auth/documents');

    if (!hasDriveScope) {
      throw new Error('Should have drive scope');
    }
    if (hasDocsScope) {
      throw new Error('Should not have docs scope');
    }
    console.log('   ✓ Individual scope checking works correctly');

    // Test hasAllScopes method
    const requiredScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/sheets'
    ];
    const hasAllRequired = authRecord.hasAllScopes(requiredScopes);
    if (!hasAllRequired) {
      throw new Error('Should have all required scopes');
    }

    const missingScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/documents'
    ];
    const hasAllMissing = authRecord.hasAllScopes(missingScopes);
    if (hasAllMissing) {
      throw new Error('Should not have all missing scopes');
    }
    console.log('   ✓ Multiple scope checking works correctly');

    // Test service scope validation
    const hasRequiredScopes = await GoogleAuthService.hasRequiredScopes(testUserId, requiredScopes);
    if (!hasRequiredScopes) {
      throw new Error('Service scope validation failed');
    }
    console.log('   ✓ Service scope validation works correctly');

    // Clean up
    await UserGoogleAuth.revokeAuth(testUserId);
    await testUser.destroy();

    console.log('   ✅ Scope validation test passed\n');
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  GoogleOAuthTester.runTests();
}

export { GoogleOAuthTester };
