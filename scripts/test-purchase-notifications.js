import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { EmailService } from '../src/services/EmailService.js';
import { InvoiceService } from '../src/services/InvoiceService.js';
import { S3Service } from '../src/services/S3Service.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config();

/**
 * Test script for purchase notifications and invoice generation
 */
async function testPurchaseNotifications() {
  try {
    logger.info('Starting purchase notifications test...');

    // Initialize services
    await DatabaseManager.connectAllDatabases();
    await EmailService.initialize();
    await S3Service.initialize();
    await InvoiceService.initialize();

    // Test data
    const testUser = {
      id: 'test-user-id-12345',
      name: 'Test User',
      email: process.env.TEST_EMAIL || '<EMAIL>',
    };

    const testPlan = {
      id: 'test-plan-id-12345',
      name: 'Creator Plan',
      planType: 'CREATOR',
      price: 299.00,
      currency: 'INR',
      billingCycle: 'MONTHLY',
      credits: 1500,
      getPlanLimits: () => ({
        projects: 10,
        filesPerDay: 50,
      }),
    };

    const testTransaction = {
      id: 'test-transaction-id-12345',
      userId: testUser.id,
      planId: testPlan.id,
      transactionType: 'SUBSCRIPTION',
      amount: testPlan.price,
      currency: testPlan.currency,
      status: 'SUCCESS',
      razorpayPaymentId: 'pay_test12345',
      razorpayOrderId: 'order_test12345',
      paidAt: new Date(),
      createdAt: new Date(),
    };

    const testSubscription = {
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    };

    // Test 1: Generate subscription invoice
    logger.info('Testing subscription invoice generation...');
    const subscriptionInvoice = await InvoiceService.generateSubscriptionInvoice({
      transaction: testTransaction,
      user: testUser,
      plan: testPlan,
      subscription: testSubscription,
    });
    logger.info('Subscription invoice generated:', subscriptionInvoice);

    // Test 2: Send subscription purchase email
    logger.info('Testing subscription purchase email...');
    const subscriptionEmailSent = await EmailService.sendSubscriptionPurchaseEmail(testUser.email, {
      user: testUser,
      plan: testPlan,
      transaction: testTransaction,
      subscription: testSubscription,
      invoiceUrl: subscriptionInvoice.downloadUrl,
    });
    logger.info('Subscription email sent:', subscriptionEmailSent);

    // Test 3: Generate add-on invoice
    logger.info('Testing add-on invoice generation...');
    const addonTransaction = {
      ...testTransaction,
      id: 'test-addon-transaction-12345',
      transactionType: 'ADDON',
    };

    const addonPlan = {
      ...testPlan,
      name: 'Extra Credits Pack',
      planType: 'ADDON',
      price: 99.00,
      credits: 500,
    };

    const addonInvoice = await InvoiceService.generateAddonInvoice({
      transaction: addonTransaction,
      user: testUser,
      plan: addonPlan,
    });
    logger.info('Add-on invoice generated:', addonInvoice);

    // Test 4: Send add-on purchase email
    logger.info('Testing add-on purchase email...');
    const addonEmailSent = await EmailService.sendAddonPurchaseEmail(testUser.email, {
      user: testUser,
      plan: addonPlan,
      transaction: addonTransaction,
      invoiceUrl: addonInvoice.downloadUrl,
    });
    logger.info('Add-on email sent:', addonEmailSent);

    // Test 5: Send payment failed email
    logger.info('Testing payment failed email...');
    const failedTransaction = {
      ...testTransaction,
      id: 'test-failed-transaction-12345',
      status: 'FAILED',
      failureReason: 'Insufficient funds',
    };

    const paymentFailedEmailSent = await EmailService.sendPaymentFailedEmail(testUser.email, {
      user: testUser,
      plan: testPlan,
      transaction: failedTransaction,
      isSubscription: true,
    });
    logger.info('Payment failed email sent:', paymentFailedEmailSent);

    logger.info('All tests completed successfully!');

    // Print summary
    console.log('\n=== TEST SUMMARY ===');
    console.log(`✅ Subscription invoice: ${subscriptionInvoice.filename}`);
    console.log(`✅ Subscription email: ${subscriptionEmailSent ? 'Sent' : 'Failed'}`);
    console.log(`✅ Add-on invoice: ${addonInvoice.filename}`);
    console.log(`✅ Add-on email: ${addonEmailSent ? 'Sent' : 'Failed'}`);
    console.log(`✅ Payment failed email: ${paymentFailedEmailSent ? 'Sent' : 'Failed'}`);
    console.log('\nCheck your email and storage for generated files.');

  } catch (error) {
    logger.error('Error during purchase notifications test:', error);
    throw error;
  } finally {
    // Close database connections
    try {
      const { userDatabase, chatDatabase } = await import('../src/config/database.js');
      await userDatabase.close();
      await chatDatabase.close();
      logger.info('Database connections closed');
    } catch (closeError) {
      logger.warn('Error closing database connections:', closeError);
    }
  }
}

// Run test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testPurchaseNotifications()
    .then(() => {
      logger.info('Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Test failed:', error);
      process.exit(1);
    });
}

export { testPurchaseNotifications };
