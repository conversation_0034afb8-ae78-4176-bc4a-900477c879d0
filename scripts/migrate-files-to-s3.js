#!/usr/bin/env node

/**
 * Migration script to move existing local files to S3
 * This script will:
 * 1. Find all chat messages with local attachments
 * 2. Upload those files to S3
 * 3. Update the database records with S3 URLs
 * 4. Optionally remove local files after successful migration
 */

import dotenv from 'dotenv';
import * as fs from 'fs/promises';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { DatabaseManager } from '../src/config/database.js';
import { S3Service } from '../src/services/S3Service.js';
import { ChatMessage } from '../src/models/chat/ChatMessage.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FileToS3Migrator {
  constructor() {
    this.migratedCount = 0;
    this.errorCount = 0;
    this.skippedCount = 0;
  }

  /**
   * Initialize the migration process
   */
  async initialize() {
    try {
      console.log('🚀 Starting file migration to S3...');
      
      // Initialize database
      await DatabaseManager.initialize();
      console.log('✅ Database connected');

      // Initialize S3 service
      await S3Service.initialize();
      if (!S3Service.isAvailable()) {
        throw new Error('S3 service is not available. Please check your AWS configuration.');
      }
      console.log('✅ S3 service initialized');

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize migration:', error.message);
      return false;
    }
  }

  /**
   * Find all chat messages with local attachments
   */
  async findLocalAttachments() {
    try {
      const messages = await ChatMessage.findAll({
        where: {
          attachmentPath: { [Op.ne]: null },
          attachmentStorageType: ['local', null] // Include null for legacy records
        },
        attributes: ['id', 'attachmentPath', 'attachmentName', 'attachmentType', 'attachmentSize']
      });

      console.log(`📁 Found ${messages.length} messages with local attachments`);
      return messages;
    } catch (error) {
      console.error('❌ Error finding local attachments:', error);
      throw error;
    }
  }

  /**
   * Migrate a single file to S3
   */
  async migrateFile(message) {
    try {
      const localPath = path.join(process.cwd(), message.attachmentPath);
      
      // Check if local file exists
      try {
        await fs.access(localPath);
      } catch (error) {
        console.log(`⚠️  Local file not found: ${localPath} (Message ID: ${message.id})`);
        this.skippedCount++;
        return false;
      }

      // Read the file
      const fileBuffer = await fs.readFile(localPath);
      
      // Upload to S3
      const s3Result = await S3Service.uploadFile(
        fileBuffer,
        message.attachmentName || path.basename(localPath),
        message.attachmentType || 'application/octet-stream',
        'migrated-attachments'
      );

      // Update database record
      await message.update({
        attachmentS3Url: s3Result.s3Url,
        attachmentS3Key: s3Result.s3Key,
        attachmentStorageType: 's3'
      });

      console.log(`✅ Migrated: ${message.attachmentName} -> ${s3Result.s3Key}`);
      this.migratedCount++;
      
      return {
        localPath,
        s3Result
      };
    } catch (error) {
      console.error(`❌ Failed to migrate file for message ${message.id}:`, error.message);
      this.errorCount++;
      return false;
    }
  }

  /**
   * Remove local file after successful migration
   */
  async removeLocalFile(localPath) {
    try {
      await fs.unlink(localPath);
      console.log(`🗑️  Removed local file: ${localPath}`);
    } catch (error) {
      console.warn(`⚠️  Could not remove local file ${localPath}:`, error.message);
    }
  }

  /**
   * Run the complete migration process
   */
  async migrate(options = {}) {
    const { removeLocalFiles = false, dryRun = false } = options;

    try {
      if (dryRun) {
        console.log('🔍 Running in DRY RUN mode - no changes will be made');
      }

      const messages = await this.findLocalAttachments();
      
      if (messages.length === 0) {
        console.log('✅ No local attachments found to migrate');
        return;
      }

      console.log(`📤 Starting migration of ${messages.length} files...`);
      
      for (const message of messages) {
        if (dryRun) {
          console.log(`[DRY RUN] Would migrate: ${message.attachmentPath}`);
          continue;
        }

        const result = await this.migrateFile(message);
        
        if (result && removeLocalFiles) {
          await this.removeLocalFile(result.localPath);
        }

        // Add a small delay to avoid overwhelming S3
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log('\n📊 Migration Summary:');
      console.log(`✅ Successfully migrated: ${this.migratedCount}`);
      console.log(`❌ Errors: ${this.errorCount}`);
      console.log(`⚠️  Skipped: ${this.skippedCount}`);
      
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Cleanup and close connections
   */
  async cleanup() {
    try {
      await DatabaseManager.close();
      console.log('✅ Database connections closed');
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  const removeLocalFiles = args.includes('--remove-local');
  const help = args.includes('--help') || args.includes('-h');

  if (help) {
    console.log(`
📁 File to S3 Migration Tool

Usage: node scripts/migrate-files-to-s3.js [options]

Options:
  --dry-run        Run without making any changes (preview mode)
  --remove-local   Remove local files after successful migration
  --help, -h       Show this help message

Examples:
  node scripts/migrate-files-to-s3.js --dry-run
  node scripts/migrate-files-to-s3.js --remove-local
  node scripts/migrate-files-to-s3.js
    `);
    return;
  }

  const migrator = new FileToS3Migrator();

  try {
    const initialized = await migrator.initialize();
    if (!initialized) {
      process.exit(1);
    }

    await migrator.migrate({ dryRun, removeLocalFiles });
    console.log('\n🎉 Migration completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error);
    process.exit(1);
  } finally {
    await migrator.cleanup();
  }
}

// Import Op for database queries
import { Op } from 'sequelize';

// Run the migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { FileToS3Migrator };
