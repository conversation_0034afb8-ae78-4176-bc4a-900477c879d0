# LLM Models Production Migration Guide

This guide explains how to migrate your local LLM models data to production server.

## Overview

Your local database contains **22 LLM models** across 5 providers:
- **OPENAI**: 4 models (GPT-4o, GPT-4o Mini, GPT-4 Turbo, GPT-3.5 Turbo)
- **ANTHROPIC**: 5 models (<PERSON> 4 Sonnet, Claude 4 Opus, Claude 3.7 Son<PERSON>, Claude 3.5 Sonnet, <PERSON> 3.5 <PERSON><PERSON>)
- **GOOGLE**: 5 models (Gemini 2.5 Pro, Gemini 2.5 Flash, Gemini 2.0 Flash, Gemini 1.5 Pro, Gemini 1.5 Flash)
- **DEEPSEEK**: 2 models (DeepSeek R1, DeepSeek V3)
- **META**: 6 models (Llama 3.3 70B, Llama 3.2 90B Vision, Llama 3.2 11B Vision, Llama 3.1 405B, Llama 3.1 70B, Llama 3.1 8B)

## Migration Options

### Option 1: Direct Database Migration (Recommended)

Use the Node.js script to directly migrate data between databases.

#### Prerequisites
1. Ensure production database has the `llm_models` table created
2. Configure production database credentials

#### Steps

1. **Configure Production Database**
   ```bash
   cp scripts/.env.production.template scripts/.env.production
   # Edit .env.production with your production database details
   ```

2. **Run Migration Script**
   ```bash
   node scripts/migrate-llm-models-to-production.js
   ```

#### Features
- ✅ Handles existing models (updates them)
- ✅ Creates new models with fresh UUIDs
- ✅ Preserves all model data and configurations
- ✅ Provides detailed logging and verification
- ✅ Safe rollback (doesn't delete existing data)

### Option 2: SQL File Export/Import

Generate SQL file and manually import on production.

#### Steps

1. **Export to SQL File**
   ```bash
   node scripts/export-llm-models-sql.js
   ```

2. **Transfer and Import**
   ```bash
   # Copy the generated file to production server
   scp scripts/llm-models-production.sql user@production-server:/path/

   # Import on production server
   mysql -u username -p infini_ai_user_chat_recs < llm-models-production.sql
   ```

#### Features
- ✅ Creates portable SQL file
- ✅ Uses `ON DUPLICATE KEY UPDATE` for safety
- ✅ Can be reviewed before execution
- ✅ Works with any MySQL-compatible database

## Important Notes

### API Keys
Your local models contain API keys that will be migrated:
- **OpenAI**: `********************************************************`
- **Google**: `AIzaSyBCnrcbXi-kd938oJrNwHz8ve4zcAt1Cw0`
- **Anthropic**: `your_anthropic_api_key_here`
- **DeepSeek**: `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`
- **Meta**: No API keys (likely using third-party service)

⚠️ **Security Recommendation**: Update API keys in production to use production-specific keys.

### Logo URLs
Current logo URLs point to localhost:
- `http://localhost:5529/assets/logos/...`

🔧 **Action Required**: Update logo URLs to production domain after migration.

### Model Capabilities
All models include detailed capability configurations:
- Text generation, image understanding, code generation
- Function calling, JSON mode, streaming support
- Vision, audio, and code execution capabilities
- Pricing information per provider

## Verification

After migration, verify the data:

```sql
-- Check total models
SELECT COUNT(*) as total_models FROM llm_models;

-- Check by provider
SELECT provider, COUNT(*) as count 
FROM llm_models 
GROUP BY provider 
ORDER BY provider;

-- Check active models
SELECT provider, COUNT(*) as active_count 
FROM llm_models 
WHERE is_active = 1 
GROUP BY provider;
```

Expected results:
- Total models: 22
- ANTHROPIC: 5, DEEPSEEK: 2, GOOGLE: 5, META: 6, OPENAI: 4

## Troubleshooting

### Database Connection Issues
- Verify production database credentials
- Ensure database server is accessible
- Check firewall settings

### Table Not Found
- Ensure `llm_models` table exists in production
- Run table creation migration if needed

### Duplicate Key Errors
- Scripts handle duplicates safely with UPDATE
- Check for unique constraint violations

### API Key Issues
- Update API keys after migration
- Test model functionality with new keys

## Post-Migration Tasks

1. **Update Logo URLs**
   ```sql
   UPDATE llm_models 
   SET logo_url = REPLACE(logo_url, 'http://localhost:5529', 'https://your-production-domain.com');
   ```

2. **Update API Keys**
   ```sql
   UPDATE llm_models 
   SET api_key = 'new-production-api-key' 
   WHERE provider = 'OPENAI';
   ```

3. **Test Model Functionality**
   - Verify each provider's models work
   - Test API integrations
   - Validate model responses

4. **Monitor Performance**
   - Check model response times
   - Monitor API usage and costs
   - Verify capability flags work correctly

## Support

If you encounter issues during migration:
1. Check the logs for detailed error messages
2. Verify database connectivity and permissions
3. Ensure all required environment variables are set
4. Test with a single model first before full migration
