import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { LLMModel } from '../src/models/chat/LLMModel.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config({ path: '.env' });

/**
 * Migrate API Keys from Environment Variables to Database Script
 * This script updates existing LLM models with API keys from environment variables
 */

// API key mapping from environment variables
const API_KEY_MAPPING = {
  OPENAI: process.env.OPENAI_API_KEY,
  ANTHROPIC: process.env.ANTHROPIC_API_KEY,
  GOOGLE: process.env.GOOGLE_API_KEY,
  DEEPSEEK: process.env.DEEPSEEK_API_KEY,
  META: process.env.META_API_KEY || process.env.TOGETHER_API_KEY
};

/**
 * Update models with API keys from environment variables
 * @param {string} provider - Provider name
 * @param {string} apiKey - API key
 */
async function updateProviderModels(provider, apiKey) {
  try {
    if (!apiKey) {
      logger.warn(`No API key found for provider: ${provider}`);
      return;
    }

    const models = await LLMModel.findAll({
      where: { provider }
    });

    if (models.length === 0) {
      logger.warn(`No models found for provider: ${provider}`);
      return;
    }

    let updatedCount = 0;
    for (const model of models) {
      // Only update if api_key is not already set
      if (!model.apiKey) {
        await model.update({ apiKey });
        updatedCount++;
        logger.info(`Updated model ${model.modelId} with API key`);
      } else {
        logger.info(`Model ${model.modelId} already has API key, skipping`);
      }
    }

    logger.info(`✅ Updated ${updatedCount} models for provider ${provider}`);
  } catch (error) {
    logger.error(`❌ Failed to update models for provider ${provider}:`, error);
    throw error;
  }
}

/**
 * Migrate all API keys from environment variables to database
 */
async function migrateApiKeys() {
  try {
    logger.info('Starting API key migration from environment variables to database...');

    // Connect to database
    await DatabaseManager.connectChatDatabase();
    logger.info('Connected to chat database');

    // Check if any API keys are available
    const availableKeys = Object.entries(API_KEY_MAPPING).filter(([provider, key]) => key);
    
    if (availableKeys.length === 0) {
      logger.warn('No API keys found in environment variables');
      process.exit(0);
    }

    logger.info(`Found API keys for providers: ${availableKeys.map(([provider]) => provider).join(', ')}`);

    // Process each provider
    for (const [provider, apiKey] of Object.entries(API_KEY_MAPPING)) {
      if (apiKey) {
        logger.info(`Processing ${provider}...`);
        await updateProviderModels(provider, apiKey);
      }
    }

    logger.info('🎉 API key migration completed successfully!');
    
    // Show summary
    const { Op } = await import('sequelize');
    const modelsWithKeys = await LLMModel.findAll({
      where: {
        apiKey: { [Op.ne]: null }
      }
    });
    
    logger.info(`📊 Summary: ${modelsWithKeys.length} models now have API keys stored in database`);
    
    // Group by provider for summary
    const providerCounts = {};
    modelsWithKeys.forEach(model => {
      providerCounts[model.provider] = (providerCounts[model.provider] || 0) + 1;
    });

    Object.entries(providerCounts).forEach(([provider, count]) => {
      logger.info(`  ${provider}: ${count} models`);
    });

    logger.info('\n📝 Next steps:');
    logger.info('1. Test the application to ensure LLM models work with database API keys');
    logger.info('2. Consider removing API keys from environment variables for security');
    logger.info('3. Use the admin interface to manage API keys going forward');
    
    process.exit(0);
  } catch (error) {
    logger.error('Error in API key migration:', error);
    process.exit(1);
  }
}

// Run the migration
migrateApiKeys();
