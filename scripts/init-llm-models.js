import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { LLMModelDataService } from '../src/services/LLMModelDataService.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config({ path: '.env' });

/**
 * Initialize LLM Models Database Script
 * This script sets up the LLM models database with all available models
 */
async function initializeLLMModels() {
  try {
    logger.info('Starting LLM models database initialization...');

    // Connect to databases
    await DatabaseManager.connectChatDatabase();
    logger.info('Connected to chat database');

    // Initialize LLM models
    await LLMModelDataService.initializeModels();
    logger.info('LLM models database initialized successfully');

    // Get summary of initialized models
    const { LLMService } = await import('../src/services/LLMService.js');
    const models = await LLMService.getAvailableModels();
    
    logger.info(`Initialized ${models.length} LLM models:`);
    
    // Group by provider
    const providerCounts = {};
    models.forEach(model => {
      providerCounts[model.provider] = (providerCounts[model.provider] || 0) + 1;
    });

    Object.entries(providerCounts).forEach(([provider, count]) => {
      logger.info(`  ${provider}: ${count} models`);
    });

    logger.info('LLM models initialization completed successfully!');
    process.exit(0);
  } catch (error) {
    logger.error('Error initializing LLM models:', error);
    process.exit(1);
  }
}

// Run the initialization
initializeLLMModels();
