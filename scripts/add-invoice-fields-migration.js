import { DatabaseManager } from '../src/config/database.js';
import logger from '../src/config/logger.js';

/**
 * Migration script to add invoice fields to PaymentTransaction table
 */
async function addInvoiceFields() {
  try {
    logger.info('Starting invoice fields migration...');

    // Connect to database
    await DatabaseManager.connectAllDatabases();

    // Get the user database connection
    const { userDatabase } = await import('../src/config/database.js');

    // Check if columns already exist and add them if they don't
    const checkColumnQuery = `
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'payment_transactions'
      AND COLUMN_NAME IN ('invoice_id', 'invoice_number', 'invoice_url')
    `;

    const [existingColumns] = await userDatabase.query(checkColumnQuery);
    const existingColumnNames = existingColumns.map(col => col.COLUMN_NAME);

    // Add invoice_id column if it doesn't exist
    if (!existingColumnNames.includes('invoice_id')) {
      const addInvoiceIdQuery = `
        ALTER TABLE payment_transactions
        ADD COLUMN invoice_id VARCHAR(36) NULL
        COMMENT 'Reference to generated invoice'
      `;
      await userDatabase.query(addInvoiceIdQuery);
      logger.info('Added invoice_id column');
    } else {
      logger.info('invoice_id column already exists');
    }

    // Add invoice_number column if it doesn't exist
    if (!existingColumnNames.includes('invoice_number')) {
      const addInvoiceNumberQuery = `
        ALTER TABLE payment_transactions
        ADD COLUMN invoice_number VARCHAR(100) NULL
        COMMENT 'Invoice number for reference'
      `;
      await userDatabase.query(addInvoiceNumberQuery);
      logger.info('Added invoice_number column');
    } else {
      logger.info('invoice_number column already exists');
    }

    // Add invoice_url column if it doesn't exist
    if (!existingColumnNames.includes('invoice_url')) {
      const addInvoiceUrlQuery = `
        ALTER TABLE payment_transactions
        ADD COLUMN invoice_url VARCHAR(500) NULL
        COMMENT 'URL to download invoice'
      `;
      await userDatabase.query(addInvoiceUrlQuery);
      logger.info('Added invoice_url column');
    } else {
      logger.info('invoice_url column already exists');
    }

    logger.info('Invoice fields migration completed successfully!');
  } catch (error) {
    logger.error('Error during invoice fields migration:', error);
    throw error;
  } finally {
    // Close database connections
    try {
      const { userDatabase, chatDatabase } = await import('../src/config/database.js');
      await userDatabase.close();
      await chatDatabase.close();
      logger.info('Database connections closed');
    } catch (closeError) {
      logger.warn('Error closing database connections:', closeError);
    }
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addInvoiceFields()
    .then(() => {
      logger.info('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration failed:', error);
      process.exit(1);
    });
}

export { addInvoiceFields };
