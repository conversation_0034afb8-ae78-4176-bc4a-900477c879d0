import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { DriveUtilityApp } from '../src/services/DriveUtilityApp.js';
import { GoogleAuthService } from '../src/services/GoogleAuthService.js';
import { logger } from '../src/utils/logger.js';

// Load environment variables
dotenv.config();

/**
 * Test script for DriveUtilityApp class
 * Tests all Google Sheets operations with mock data
 */
class DriveUtilityAppTester {
  constructor() {
    this.testResults = [];
    this.testUserId = 'test-user-drive-utility-123';
    this.testSpreadsheetId = '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms'; // Example Google Sheets ID
  }

  /**
   * Initialize the test environment
   */
  async initialize() {
    try {
      console.log('🚀 Initializing DriveUtilityApp test environment...\n');

      // Connect to database
      await DatabaseManager.connectAllDatabases();
      await DatabaseManager.syncAllDatabases(false);

      console.log('✅ Test environment initialized successfully\n');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize test environment:', error.message);
      return false;
    }
  }

  /**
   * Test validation methods
   */
  async testValidationMethods() {
    try {
      console.log('🔍 Testing validation methods...');

      // Test spreadsheet ID validation
      try {
        DriveUtilityApp._validateSpreadsheetId('valid-spreadsheet-id-123');
        console.log('✅ Valid spreadsheet ID accepted');
      } catch (error) {
        console.log('❌ Valid spreadsheet ID rejected:', error.message);
      }

      try {
        DriveUtilityApp._validateSpreadsheetId('');
        console.log('❌ Empty spreadsheet ID should be rejected');
      } catch (error) {
        console.log('✅ Empty spreadsheet ID correctly rejected');
      }

      // Test range validation
      try {
        DriveUtilityApp._validateRange('A1:C3');
        DriveUtilityApp._validateRange('Sheet1!A1:C3');
        DriveUtilityApp._validateRange('A:C');
        DriveUtilityApp._validateRange('1:3');
        DriveUtilityApp._validateRange('Sheet1');
        console.log('✅ Valid ranges accepted');
      } catch (error) {
        console.log('❌ Valid ranges rejected:', error.message);
      }

      try {
        DriveUtilityApp._validateRange('invalid-range');
        console.log('❌ Invalid range should be rejected');
      } catch (error) {
        console.log('✅ Invalid range correctly rejected');
      }

      // Test values validation
      try {
        DriveUtilityApp._validateValues([['A1', 'B1'], ['A2', 'B2']]);
        console.log('✅ Valid values array accepted');
      } catch (error) {
        console.log('❌ Valid values array rejected:', error.message);
      }

      try {
        DriveUtilityApp._validateValues('not-an-array');
        console.log('❌ Invalid values should be rejected');
      } catch (error) {
        console.log('✅ Invalid values correctly rejected');
      }

      this.testResults.push({
        test: 'Validation Methods',
        status: 'PASSED',
        details: 'All validation methods working correctly'
      });

      return true;
    } catch (error) {
      console.error('❌ Validation methods test failed:', error.message);
      this.testResults.push({
        test: 'Validation Methods',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test error handling methods
   */
  async testErrorHandling() {
    try {
      console.log('\n🛡️  Testing error handling...');

      // Test Google API error handling
      const testErrors = [
        { code: 400, message: 'Bad Request' },
        { code: 401, message: 'Unauthorized' },
        { code: 403, message: 'Forbidden' },
        { code: 404, message: 'Not Found' },
        { code: 429, message: 'Too Many Requests' },
        { code: 500, message: 'Internal Server Error' }
      ];

      testErrors.forEach(error => {
        const handledError = DriveUtilityApp._handleGoogleApiError(error, 'test operation');
        console.log(`✅ Error ${error.code} handled: ${handledError.message}`);
      });

      // Test network error handling
      const networkError = new Error('ENOTFOUND googleapis.com');
      const handledNetworkError = DriveUtilityApp._handleGoogleApiError(networkError, 'test operation');
      console.log(`✅ Network error handled: ${handledNetworkError.message}`);

      this.testResults.push({
        test: 'Error Handling',
        status: 'PASSED',
        details: 'All error types handled correctly'
      });

      return true;
    } catch (error) {
      console.error('❌ Error handling test failed:', error.message);
      this.testResults.push({
        test: 'Error Handling',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test method signatures and structure
   */
  async testMethodStructure() {
    try {
      console.log('\n🏗️  Testing method structure...');

      // Check if all required methods exist
      const requiredMethods = [
        'createSpreadsheet',
        'updateSpreadsheetContent',
        'readSpreadsheetContent',
        'getAvailableSheets',
        'getSheetData'
      ];

      requiredMethods.forEach(methodName => {
        if (typeof DriveUtilityApp[methodName] === 'function') {
          console.log(`✅ Method ${methodName} exists`);
        } else {
          throw new Error(`Method ${methodName} is missing`);
        }
      });

      // Check private methods
      const privateMethods = [
        '_validateSheetsPermissions',
        '_getSheetsClient',
        '_validateSpreadsheetId',
        '_validateRange',
        '_handleGoogleApiError',
        '_validateValues'
      ];

      privateMethods.forEach(methodName => {
        if (typeof DriveUtilityApp[methodName] === 'function') {
          console.log(`✅ Private method ${methodName} exists`);
        } else {
          throw new Error(`Private method ${methodName} is missing`);
        }
      });

      this.testResults.push({
        test: 'Method Structure',
        status: 'PASSED',
        details: 'All required methods exist'
      });

      return true;
    } catch (error) {
      console.error('❌ Method structure test failed:', error.message);
      this.testResults.push({
        test: 'Method Structure',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test authentication requirements (without actual Google auth)
   */
  async testAuthenticationRequirements() {
    try {
      console.log('\n🔐 Testing authentication requirements...');

      // Test that methods require authentication
      const methodsToTest = [
        'createSpreadsheet',
        'updateSpreadsheetContent',
        'readSpreadsheetContent',
        'getAvailableSheets',
        'getSheetData'
      ];

      for (const methodName of methodsToTest) {
        try {
          await DriveUtilityApp[methodName]('invalid-user-id', 'test-param');
          console.log(`❌ Method ${methodName} should require valid authentication`);
        } catch (error) {
          if (error.message.includes('authenticated') || error.message.includes('User ID')) {
            console.log(`✅ Method ${methodName} correctly requires authentication`);
          } else {
            console.log(`⚠️  Method ${methodName} failed with: ${error.message}`);
          }
        }
      }

      this.testResults.push({
        test: 'Authentication Requirements',
        status: 'PASSED',
        details: 'All methods require proper authentication'
      });

      return true;
    } catch (error) {
      console.error('❌ Authentication requirements test failed:', error.message);
      this.testResults.push({
        test: 'Authentication Requirements',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test parameter validation
   */
  async testParameterValidation() {
    try {
      console.log('\n📋 Testing parameter validation...');

      // Test createSpreadsheet parameter validation
      try {
        await DriveUtilityApp.createSpreadsheet('test-user', '');
        console.log('❌ Empty title should be rejected');
      } catch (error) {
        if (error.message.includes('title')) {
          console.log('✅ Empty title correctly rejected');
        }
      }

      // Test updateSpreadsheetContent parameter validation
      try {
        await DriveUtilityApp.updateSpreadsheetContent('test-user', '', 'Sheet1', 'A1:B2', []);
        console.log('❌ Empty spreadsheet ID should be rejected');
      } catch (error) {
        if (error.message.includes('Spreadsheet ID')) {
          console.log('✅ Empty spreadsheet ID correctly rejected');
        }
      }

      this.testResults.push({
        test: 'Parameter Validation',
        status: 'PASSED',
        details: 'Parameter validation working correctly'
      });

      return true;
    } catch (error) {
      console.error('❌ Parameter validation test failed:', error.message);
      this.testResults.push({
        test: 'Parameter Validation',
        status: 'FAILED',
        details: error.message
      });
      return false;
    }
  }

  /**
   * Print test results
   */
  printTestResults() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(50));

    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;

    this.testResults.forEach(result => {
      const icon = result.status === 'PASSED' ? '✅' : '❌';
      console.log(`${icon} ${result.test}: ${result.status}`);
      if (result.details) {
        console.log(`   Details: ${result.details}`);
      }
    });

    console.log('\n' + '='.repeat(50));
    console.log(`Total Tests: ${this.testResults.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / this.testResults.length) * 100).toFixed(1)}%`);
    console.log('='.repeat(50));
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting DriveUtilityApp Tests\n');
    console.log('=' .repeat(50));

    const tests = [
      () => this.testValidationMethods(),
      () => this.testErrorHandling(),
      () => this.testMethodStructure(),
      () => this.testAuthenticationRequirements(),
      () => this.testParameterValidation()
    ];

    for (const test of tests) {
      await test();
    }

    this.printTestResults();
  }

  /**
   * Cleanup test environment
   */
  async cleanup() {
    try {
      await DatabaseManager.closeAllConnections();
      console.log('\n🧹 Test environment cleaned up');
    } catch (error) {
      console.error('Error during cleanup:', error.message);
    }
  }
}

/**
 * Main function
 */
async function main() {
  const tester = new DriveUtilityAppTester();

  try {
    const initialized = await tester.initialize();
    if (!initialized) {
      process.exit(1);
    }

    await tester.runAllTests();
    
  } catch (error) {
    console.error('\n💥 Test script failed:', error);
    process.exit(1);
  } finally {
    await tester.cleanup();
  }
}

// Run the tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { DriveUtilityAppTester };
