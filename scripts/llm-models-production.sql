-- LLM Models Migration Script
-- Generated on: 2025-08-05T12:09:54.308Z
-- Total models: 22

USE infini_ai_user_chat_recs;

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Clear existing models (optional - uncomment if you want to start fresh)
-- DELETE FROM llm_models;

-- OPENAI: GPT-3.5 Turbo
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '2487b366-a236-41ae-a002-253645f7b810',
  'OPENAI',
  'GPT-3.5-Turbo',
  'gpt-3.5-turbo',
  'GPT-3.5 Turbo',
  'Fast and efficient model for most conversational and text generation tasks',
  '{"textGeneration":true,"imageUnderstanding":false,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":false}',
  'http://localhost:5529/assets/logos/gpt-35.webp',
  NULL,
  '********************************************************',
  '{"inputTokens":0.5,"outputTokens":1.5,"currency":"USD"}',
  16385,
  4096,
  0,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- OPENAI: GPT-4 Turbo
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '06041e9f-4c1e-49da-88de-69be02e04e2a',
  'OPENAI',
  'GPT-4-Turbo',
  'gpt-4-turbo',
  'GPT-4 Turbo',
  'High-performance GPT-4 model optimized for speed and efficiency',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/gpt-35.webp',
  NULL,
  '********************************************************',
  '{"inputTokens":10,"outputTokens":30,"currency":"USD"}',
  128000,
  4096,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- OPENAI: GPT-4o
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '94679376-57b6-40ec-9e07-1186c64cc674',
  'OPENAI',
  'GPT-4o',
  'gpt-4o',
  'GPT-4o',
  'Most advanced GPT-4 model with improved reasoning, coding, and multimodal capabilities',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/gpt-35.webp',
  NULL,
  '********************************************************',
  '{"inputTokens":2.5,"outputTokens":10,"currency":"USD"}',
  128000,
  16384,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- OPENAI: GPT-4o Mini
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '25067756-afe0-4ff9-8f4e-22b4ad72a65a',
  'OPENAI',
  'GPT-4o-mini',
  'gpt-4o-mini',
  'GPT-4o Mini',
  'Smaller, faster, and more affordable version of GPT-4o',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/gpt-35.webp',
  NULL,
  '********************************************************',
  '{"inputTokens":0.15,"outputTokens":0.6,"currency":"USD"}',
  128000,
  16384,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- ANTHROPIC: Claude 3.5 Haiku
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  'b3c77fbd-be71-4fb2-804e-a3ba0616932e',
  'ANTHROPIC',
  'Claude-3.5-Haiku',
  'claude-3-5-haiku-20241022',
  'Claude 3.5 Haiku',
  'Fastest Claude model with intelligence at blazing speeds',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":false}',
  'http://localhost:5529/assets/logos/claude.webp',
  NULL,
  'your_anthropic_api_key_here',
  '{"inputTokens":0.8,"outputTokens":4,"currency":"USD"}',
  200000,
  8192,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- ANTHROPIC: Claude 3.5 Sonnet
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '918a83f7-9586-4e1b-866a-419f314ba6c6',
  'ANTHROPIC',
  'Claude-3.5-Sonnet',
  'claude-3-5-sonnet-20241022',
  'Claude 3.5 Sonnet',
  'Previous intelligent model with high level of capability',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/claude.webp',
  NULL,
  'your_anthropic_api_key_here',
  '{"inputTokens":3,"outputTokens":15,"currency":"USD"}',
  200000,
  8192,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- ANTHROPIC: Claude 3.7 Sonnet
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  'd351e38b-ddd7-4a01-9cd0-c15d7532a39a',
  'ANTHROPIC',
  'Claude-3.7-Sonnet',
  'claude-3-7-sonnet-20250219',
  'Claude 3.7 Sonnet',
  'High-performance model with early extended thinking capabilities',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"extendedThinking":true}',
  'http://localhost:5529/assets/logos/claude.webp',
  NULL,
  'your_anthropic_api_key_here',
  '{"inputTokens":3,"outputTokens":15,"currency":"USD"}',
  200000,
  64000,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- ANTHROPIC: Claude 4 Opus
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  'b3ab6dbc-c766-4c04-98ca-f3513c6e66b6',
  'ANTHROPIC',
  'Claude-4-Opus',
  'claude-opus-4-20250514',
  'Claude 4 Opus',
  'Most powerful and capable Claude model with superior reasoning capabilities',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"extendedThinking":true}',
  'http://localhost:5529/assets/logos/claude.webp',
  NULL,
  'your_anthropic_api_key_here',
  '{"inputTokens":15,"outputTokens":75,"currency":"USD"}',
  200000,
  32000,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- ANTHROPIC: Claude 4 Sonnet
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  'a75e0f62-9499-4ecd-80f6-086d24dc8290',
  'ANTHROPIC',
  'Claude-4-Sonnet',
  'claude-sonnet-4-20250514',
  'Claude 4 Sonnet',
  'High-performance model with exceptional reasoning capabilities',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"extendedThinking":true}',
  'http://localhost:5529/assets/logos/claude.webp',
  NULL,
  'your_anthropic_api_key_here',
  '{"inputTokens":3,"outputTokens":15,"currency":"USD"}',
  200000,
  64000,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- GOOGLE: Gemini 1.5 Flash
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '76bbbf51-5850-4a1f-8358-c4075bd9e9b7',
  'GOOGLE',
  'Gemini-1.5-Flash',
  'gemini-1.5-flash',
  'Gemini 1.5 Flash',
  'Fast and versatile multimodal model for scaling across diverse tasks',
  '{"textGeneration":true,"imageUnderstanding":true,"videoUnderstanding":true,"audioUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"codeExecution":true}',
  'http://localhost:5529/assets/logos/gemini.png',
  NULL,
  'AIzaSyBCnrcbXi-kd938oJrNwHz8ve4zcAt1Cw0',
  '{"inputTokens":0.075,"outputTokens":0.3,"currency":"USD"}',
  1048576,
  8192,
  1,
  1,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- GOOGLE: Gemini 1.5 Pro
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  'b735e812-abbd-43b5-b1ac-05c7ba5dda2f',
  'GOOGLE',
  'Gemini-1.5-Pro',
  'gemini-1.5-pro',
  'Gemini 1.5 Pro',
  'Medium-sized multimodal model optimized for reasoning tasks with long context',
  '{"textGeneration":true,"imageUnderstanding":true,"videoUnderstanding":true,"audioUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"codeExecution":true}',
  'http://localhost:5529/assets/logos/gemini.png',
  NULL,
  'AIzaSyBCnrcbXi-kd938oJrNwHz8ve4zcAt1Cw0',
  '{"inputTokens":1.25,"outputTokens":5,"currency":"USD"}',
  2097152,
  8192,
  1,
  1,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- GOOGLE: Gemini 2.0 Flash
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '2e78dba1-9557-4461-8acb-8c975eb3d53c',
  'GOOGLE',
  'Gemini-2.0-Flash',
  'gemini-2.0-flash',
  'Gemini 2.0 Flash',
  'Next-generation features with enhanced capabilities and speed',
  '{"textGeneration":true,"imageUnderstanding":true,"videoUnderstanding":true,"audioUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"codeExecution":true}',
  'http://localhost:5529/assets/logos/gemini.png',
  NULL,
  'AIzaSyBCnrcbXi-kd938oJrNwHz8ve4zcAt1Cw0',
  '{"inputTokens":0.075,"outputTokens":0.3,"currency":"USD"}',
  1048576,
  8192,
  1,
  1,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- GOOGLE: Gemini 2.5 Flash
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '89aa17e6-e348-4c0d-8af6-2013f546cd89',
  'GOOGLE',
  'Gemini-2.5-Flash',
  'gemini-2.5-flash',
  'Gemini 2.5 Flash',
  'Best price-performance model with versatile capabilities and adaptive thinking',
  '{"textGeneration":true,"imageUnderstanding":true,"videoUnderstanding":true,"audioUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"thinking":true,"codeExecution":true}',
  'http://localhost:5529/assets/logos/gemini.png',
  NULL,
  'AIzaSyBCnrcbXi-kd938oJrNwHz8ve4zcAt1Cw0',
  '{"inputTokens":0.075,"outputTokens":0.3,"currency":"USD"}',
  1048576,
  65536,
  1,
  1,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- GOOGLE: Gemini 2.5 Pro
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '22f03c3c-a2ed-4888-aa4d-32dcee72b70a',
  'GOOGLE',
  'Gemini-2.5-Pro',
  'gemini-2.5-pro',
  'Gemini 2.5 Pro',
  'Most advanced thinking model with maximum accuracy and cutting-edge performance',
  '{"textGeneration":true,"imageUnderstanding":true,"videoUnderstanding":true,"audioUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"thinking":true,"codeExecution":true}',
  'http://localhost:5529/assets/logos/gemini.png',
  NULL,
  'AIzaSyBCnrcbXi-kd938oJrNwHz8ve4zcAt1Cw0',
  '{"inputTokens":1.25,"outputTokens":5,"currency":"USD"}',
  1048576,
  65536,
  1,
  1,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- DEEPSEEK: DeepSeek R1
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  'a3a9203e-20e1-42c2-9b1a-27315eac9435',
  'DEEPSEEK',
  'DeepSeek-R1',
  'deepseek-reasoner',
  'DeepSeek R1',
  'Advanced reasoning model with performance on par with OpenAI o1',
  '{"textGeneration":true,"imageUnderstanding":false,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true,"thinking":true,"chainOfThought":true}',
  'http://localhost:5529/assets/logos/deepseek.png',
  NULL,
  '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',
  '{"inputTokens":0.55,"outputTokens":2.19,"currency":"USD"}',
  64000,
  64000,
  0,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- DEEPSEEK: DeepSeek V3
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '469b07a2-5f6a-4a66-ac9e-73c2bc2f1c01',
  'DEEPSEEK',
  'DeepSeek-V3',
  'deepseek-chat',
  'DeepSeek V3',
  'High-performance general-purpose model for various tasks',
  '{"textGeneration":true,"imageUnderstanding":false,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/deepseek.png',
  NULL,
  '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',
  '{"inputTokens":0.27,"outputTokens":1.1,"currency":"USD"}',
  64000,
  8000,
  0,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- META: Llama 3.1 405B
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '847847bb-e962-4800-b774-7b204a3b84e9',
  'META',
  'Llama-3.1-405B',
  'llama-3.1-405b-instruct',
  'Llama 3.1 405B',
  'Largest and most capable Llama model for complex reasoning tasks',
  '{"textGeneration":true,"imageUnderstanding":false,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/llama.png',
  NULL,
  NULL,
  '{"inputTokens":2.7,"outputTokens":2.7,"currency":"USD"}',
  131072,
  4096,
  0,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- META: Llama 3.1 70B
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '4a39cc60-d84b-41cd-8f69-c56bd6427a23',
  'META',
  'Llama-3.1-70B',
  'llama-3.1-70b-instruct',
  'Llama 3.1 70B',
  'High-performance model balancing capability and efficiency',
  '{"textGeneration":true,"imageUnderstanding":false,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/llama.png',
  NULL,
  NULL,
  '{"inputTokens":0.9,"outputTokens":0.9,"currency":"USD"}',
  131072,
  4096,
  0,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- META: Llama 3.1 8B
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  'b23b1412-4c11-4352-9d85-9abfc7c25258',
  'META',
  'Llama-3.1-8B',
  'llama-3.1-8b-instruct',
  'Llama 3.1 8B',
  'Compact and efficient model for lightweight applications',
  '{"textGeneration":true,"imageUnderstanding":false,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/llama.png',
  NULL,
  NULL,
  '{"inputTokens":0.2,"outputTokens":0.2,"currency":"USD"}',
  131072,
  4096,
  0,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- META: Llama 3.2 11B Vision
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  'c3c3b3e4-5c1d-4d73-9c71-11c04efadd27',
  'META',
  'Llama-3.2-11B-Vision',
  'llama-3.2-11b-vision-instruct',
  'Llama 3.2 11B Vision',
  'Efficient multimodal model for vision and text tasks',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/llama.png',
  NULL,
  NULL,
  '{"inputTokens":0.35,"outputTokens":0.35,"currency":"USD"}',
  131072,
  4096,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- META: Llama 3.2 90B Vision
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '695a6964-07df-4bd7-a360-c8d6f97d84fa',
  'META',
  'Llama-3.2-90B-Vision',
  'llama-3.2-90b-vision-instruct',
  'Llama 3.2 90B Vision',
  'Large multimodal model with advanced text and visual intelligence',
  '{"textGeneration":true,"imageUnderstanding":true,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/llama.png',
  NULL,
  NULL,
  '{"inputTokens":1.2,"outputTokens":1.2,"currency":"USD"}',
  131072,
  4096,
  1,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

-- META: Llama 3.3 70B
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  '88f962f5-f35f-4eba-9723-8fc01a60891b',
  'META',
  'Llama-3.3-70B',
  'llama-3.3-70b-instruct',
  'Llama 3.3 70B',
  'Latest Llama model with improved performance and efficiency',
  '{"textGeneration":true,"imageUnderstanding":false,"codeGeneration":true,"functionCalling":true,"jsonMode":true,"streaming":true,"reasoning":true}',
  'http://localhost:5529/assets/logos/llama.png',
  NULL,
  NULL,
  '{"inputTokens":0.6,"outputTokens":0.6,"currency":"USD"}',
  131072,
  4096,
  0,
  0,
  1,
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();


-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify the import
SELECT 
  provider,
  COUNT(*) as model_count
FROM llm_models 
GROUP BY provider 
ORDER BY provider;

SELECT COUNT(*) as total_models FROM llm_models;
