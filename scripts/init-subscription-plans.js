#!/usr/bin/env node

/**
 * Initialize subscription plans
 * This script creates the default subscription plans in the database
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '..', '.env') });

import { DatabaseManager } from '../src/config/database.js';
import { SubscriptionService } from '../src/services/SubscriptionService.js';
import logger from '../src/config/logger.js';

/**
 * Initialize subscription plans
 */
async function initializePlans() {
  try {
    console.log('🚀 Initializing subscription plans...\n');

    // Load models
    await import('../src/models/user/index.js');
    await import('../src/models/subscription/index.js');

    // Connect to database
    await DatabaseManager.connectAllDatabases();
    
    // Sync database (create tables if they don't exist)
    await DatabaseManager.syncAllDatabases(false);

    // Initialize default plans
    await SubscriptionService.initializeDefaultPlans();

    // Verify plans were created
    const plans = await SubscriptionService.getAvailablePlans();
    
    console.log('✅ Subscription plans initialized successfully!\n');
    console.log('📋 Available Plans:');
    
    plans.forEach(plan => {
      const price = plan.price > 0 ? `₹${plan.price}` : 'Free';
      const cycle = plan.billingCycle.toLowerCase();
      const credits = plan.isUnlimitedCredits ? 'Unlimited' : plan.credits;
      
      console.log(`  • ${plan.name}`);
      console.log(`    Type: ${plan.planType}`);
      console.log(`    Price: ${price}/${cycle}`);
      console.log(`    Credits: ${credits}`);
      console.log(`    Features: ${JSON.stringify(plan.getPlanFeatures())}`);
      console.log(`    Limits: ${JSON.stringify(plan.getPlanLimits())}`);
      console.log('');
    });

    console.log('🎉 Subscription system is ready!');
    
  } catch (error) {
    console.error('💥 Failed to initialize subscription plans:', error);
    process.exit(1);
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  initializePlans().catch(console.error);
}

export { initializePlans };
