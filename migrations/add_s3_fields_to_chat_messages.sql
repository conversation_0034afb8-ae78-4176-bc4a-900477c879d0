-- Migration: Add S3-related fields to chat_messages table
-- Date: 2025-01-28
-- Description: Add columns to support AWS S3 storage for chat attachments

USE infini_ai_user_chat_recs;

-- Add new columns for S3 support
ALTER TABLE chat_messages 
ADD COLUMN attachment_s3_url VARCHAR(500) NULL AFTER attachment_size,
ADD COLUMN attachment_s3_key VARCHAR(500) NULL AFTER attachment_s3_url,
ADD COLUMN attachment_storage_type ENUM('local', 's3') NULL AFTER attachment_s3_key;

-- Add indexes for the new columns
CREATE INDEX idx_chat_messages_attachment_s3_url ON chat_messages(attachment_s3_url);
CREATE INDEX idx_chat_messages_attachment_s3_key ON chat_messages(attachment_s3_key);
CREATE INDEX idx_chat_messages_attachment_storage_type ON chat_messages(attachment_storage_type);

-- Update existing records to mark them as local storage
UPDATE chat_messages 
SET attachment_storage_type = 'local' 
WHERE attachment_path IS NOT NULL;

-- Verify the changes
DESCRIBE chat_messages;

-- Show sample data
SELECT id, attachment_path, attachment_s3_url, attachment_s3_key, attachment_storage_type 
FROM chat_messages 
WHERE attachment_path IS NOT NULL 
LIMIT 5;
