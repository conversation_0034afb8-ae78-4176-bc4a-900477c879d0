-- Migration: Add api_key and logo_attachment_secure_id fields to llm_models table
-- Date: 2025-07-29
-- Description: Add new fields for storing API keys and logo attachment secure IDs

USE infini_ai_user_chat_recs;

-- Add api_key field if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'infini_ai_user_chat_recs' 
     AND TABLE_NAME = 'llm_models' 
     AND COLUMN_NAME = 'api_key') = 0,
    'ALTER TABLE llm_models ADD COLUMN api_key VARCHAR(500) NULL AFTER logo_url;',
    'SELECT "api_key column already exists" AS message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add logo_attachment_secure_id field if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'infini_ai_user_chat_recs' 
     AND TABLE_NAME = 'llm_models' 
     AND COLUMN_NAME = 'logo_attachment_secure_id') = 0,
    'ALTER TABLE llm_models ADD COLUMN logo_attachment_secure_id VARCHAR(255) NULL AFTER logo_url;',
    'SELECT "logo_attachment_secure_id column already exists" AS message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show the updated table structure
DESCRIBE llm_models;
