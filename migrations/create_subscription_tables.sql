-- Migration: Create subscription system tables
-- Date: 2025-01-28
-- Description: Create tables for subscription plans, user subscriptions, payment transactions, and credit resets

USE infini_ai_users;

-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id VARCHAR(36) PRIMARY KEY,
    plan_type ENUM('EXPLORER', 'CREATOR', 'PRO', 'ADDON') NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'INR',
    billing_cycle ENUM('WEEKLY', 'MONTHLY', 'YEARLY', 'ONE_TIME') NOT NULL DEFAULT 'MONTHLY',
    credits INT NOT NULL DEFAULT 0,
    is_unlimited_credits BOOLEAN NOT NULL DEFAULT FALSE,
    features JSON COMMENT 'JSON object containing plan features',
    limits JSON COMMENT 'JSON object containing plan limits (projects, files, etc.)',
    razorpay_plan_id VARCHAR(255) UNIQUE COMMENT 'Razorpay plan ID for recurring payments',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    sort_order INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_plan_type (plan_type),
    INDEX idx_razorpay_plan_id (razorpay_plan_id),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    plan_id VARCHAR(36) NOT NULL,
    razorpay_subscription_id VARCHAR(255) UNIQUE,
    razorpay_customer_id VARCHAR(255),
    status ENUM('ACTIVE', 'SUSPENDED', 'CANCELLED', 'EXPIRED') NOT NULL DEFAULT 'ACTIVE',
    start_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP NULL,
    next_billing_date TIMESTAMP NULL,
    grace_period_end TIMESTAMP NULL,
    cancelled_at TIMESTAMP NULL,
    cancellation_reason VARCHAR(500),
    metadata JSON COMMENT 'Additional subscription metadata',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT,
    
    INDEX idx_user_id (user_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_razorpay_subscription_id (razorpay_subscription_id),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date),
    INDEX idx_next_billing_date (next_billing_date)
);

-- Create payment_transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    subscription_id VARCHAR(36) NULL,
    plan_id VARCHAR(36) NULL,
    transaction_type ENUM('SUBSCRIPTION', 'ADDON', 'RENEWAL', 'UPGRADE', 'DOWNGRADE') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'INR',
    status ENUM('PENDING', 'SUCCESS', 'FAILED', 'REFUNDED') NOT NULL DEFAULT 'PENDING',
    razorpay_order_id VARCHAR(255) UNIQUE,
    razorpay_payment_id VARCHAR(255) UNIQUE,
    razorpay_signature VARCHAR(500),
    payment_method VARCHAR(50),
    paid_at TIMESTAMP NULL,
    failure_reason VARCHAR(500),
    payment_details JSON COMMENT 'Additional payment details from Razorpay',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE SET NULL,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_razorpay_order_id (razorpay_order_id),
    INDEX idx_razorpay_payment_id (razorpay_payment_id),
    INDEX idx_status (status),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at)
);

-- Create credit_resets table
CREATE TABLE IF NOT EXISTS credit_resets (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL UNIQUE,
    reset_cycle ENUM('WEEKLY', 'MONTHLY') NOT NULL,
    credit_amount INT NOT NULL COMMENT 'Amount of credits to reset to',
    last_reset_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    next_reset_date TIMESTAMP NOT NULL,
    reset_count INT NOT NULL DEFAULT 0 COMMENT 'Number of times credits have been reset',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_reset_cycle (reset_cycle),
    INDEX idx_next_reset_date (next_reset_date),
    INDEX idx_is_active (is_active)
);

-- Insert default subscription plans
INSERT INTO subscription_plans (
    id, plan_type, name, description, price, currency, billing_cycle, 
    credits, is_unlimited_credits, features, limits, sort_order
) VALUES 
(
    UUID(), 'EXPLORER', 'Explorer Plan', 
    'Explorer Plan - 30 credits per week', 
    0.00, 'INR', 'WEEKLY', 30, FALSE,
    JSON_OBJECT('models', JSON_ARRAY('gpt-3.5-turbo', 'claude-3-haiku-20240307'), 'support', 'standard'),
    JSON_OBJECT('projects', 3, 'filesPerDay', 3, 'models', JSON_ARRAY('gpt-3.5-turbo', 'claude-3-haiku-20240307')),
    1
),
(
    UUID(), 'CREATOR', 'Creator Plan', 
    'Creator Plan - 1500 credits per month', 
    1299.00, 'INR', 'MONTHLY', 1500, FALSE,
    JSON_OBJECT('models', JSON_ARRAY('gpt-4o', 'gpt-4o-mini', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229'), 'support', 'standard'),
    JSON_OBJECT('projects', 20, 'filesPerDay', 20, 'models', JSON_ARRAY('gpt-4o', 'gpt-4o-mini', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229')),
    2
),
(
    UUID(), 'PRO', 'Pro Plan', 
    'Pro Plan - Unlimited credits', 
    1899.00, 'INR', 'MONTHLY', 0, TRUE,
    JSON_OBJECT('models', JSON_ARRAY('all'), 'support', 'priority'),
    JSON_OBJECT('projects', 100, 'filesPerDay', 50, 'models', JSON_ARRAY('all')),
    3
),
(
    UUID(), 'ADDON', 'Infini Add Pack', 
    'Infini Add Pack - 100 credits (no expiry)', 
    120.00, 'INR', 'ONE_TIME', 100, FALSE,
    JSON_OBJECT('type', 'addon'),
    JSON_OBJECT('projects', 2, 'filesPerDay', 10),
    4
);

-- Update existing user profiles to use new plan types
UPDATE user_profiles SET plan = 'EXPLORER' WHERE plan = 'FREE';
UPDATE user_profiles SET plan = 'CREATOR' WHERE plan = 'PREMIUM';
UPDATE user_profiles SET plan = 'PRO' WHERE plan = 'ENTERPRISE';

-- Verify the changes
SELECT 'Subscription Plans Created:' as info;
SELECT plan_type, name, price, billing_cycle FROM subscription_plans ORDER BY sort_order;

SELECT 'User Profiles Updated:' as info;
SELECT plan, COUNT(*) as count FROM user_profiles GROUP BY plan;

COMMIT;
