-- Migration: Create user_addons table
-- Date: 2025-07-05
-- Description: Create table to track user addon purchases and their remaining limits

USE infini_ai_subscription;

START TRANSACTION;

-- Create user_addons table
CREATE TABLE IF NOT EXISTS user_addons (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    plan_id VARCHAR(36) NOT NULL,
    transaction_id VARCHAR(36) NOT NULL UNIQUE,
    total_credits INT NOT NULL DEFAULT 0,
    remaining_credits INT NOT NULL DEFAULT 0,
    total_projects INT NOT NULL DEFAULT 0,
    remaining_projects INT NOT NULL DEFAULT 0,
    total_files_per_day INT NOT NULL DEFAULT 0,
    remaining_files_per_day INT NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES infini_ai_user_management.users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT,
    FOREIGN KEY (transaction_id) REFERENCES payment_transactions(id) ON DELETE RESTRICT,
    
    -- Indexes for performance
    INDEX idx_user_addons_user_id (user_id),
    INDEX idx_user_addons_plan_id (plan_id),
    INDEX idx_user_addons_transaction_id (transaction_id),
    INDEX idx_user_addons_is_active (is_active),
    INDEX idx_user_addons_remaining_credits (remaining_credits),
    INDEX idx_user_addons_remaining_projects (remaining_projects),
    INDEX idx_user_addons_remaining_files_per_day (remaining_files_per_day),
    INDEX idx_user_addons_created_at (created_at)
);

-- Verify the table creation
SELECT 'User Addons Table Created:' as info;
DESCRIBE user_addons;

COMMIT;
