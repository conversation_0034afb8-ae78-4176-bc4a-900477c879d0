-- Migration: Create LLM models table
-- Date: 2025-07-29
-- Description: Create table to store LLM model information with capabilities

USE infini_ai_user_chat_recs;

-- Create LLM models table
CREATE TABLE IF NOT EXISTS llm_models (
  id VARCHAR(36) PRIMARY KEY,
  provider ENUM('O<PERSON><PERSON><PERSON><PERSON>', 'ANTHROPIC', 'GOOGLE', 'DEEPSEEK', 'META') NOT NULL,
  model_name VARCHAR(100) NOT NULL,
  model_id VARCHAR(100) NOT NULL UNIQUE,
  display_name VARCHAR(150) NOT NULL,
  description TEXT NOT NULL,
  capabilities JSON NOT NULL,
  logo_url VARCHAR(500) NULL,
  logo_attachment_secure_id VARCHAR(255) NULL,
  api_key VARCHAR(500) NULL,
  pricing JSON NULL,
  context_window INT NULL,
  max_output_tokens INT NULL,
  supports_vision BOOLEAN NOT NULL DEFAULT FALSE,
  supports_audio BOOLEAN NOT NULL DEFAULT FALSE,
  supports_code_execution BOOLEAN NOT NULL DEFAULT FALSE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_llm_models_provider ON llm_models(provider);
CREATE UNIQUE INDEX idx_llm_models_model_id ON llm_models(model_id);
CREATE INDEX idx_llm_models_is_active ON llm_models(is_active);
CREATE INDEX idx_llm_models_supports_vision ON llm_models(supports_vision);
CREATE INDEX idx_llm_models_supports_audio ON llm_models(supports_audio);
CREATE INDEX idx_llm_models_supports_code_execution ON llm_models(supports_code_execution);
CREATE INDEX idx_llm_models_created_at ON llm_models(created_at);
CREATE INDEX idx_llm_models_updated_at ON llm_models(updated_at);

-- Insert initial data (this will be handled by the service)
-- The LLMModelDataService will populate this table with all model data
