-- Migration: Add secure file ID field to chat_messages table
-- Date: 2025-01-28
-- Description: Add column to support secure file identifiers for chat attachments

USE infini_ai_user_chat_recs;

-- Add new column for secure file identifier
ALTER TABLE chat_messages 
ADD COLUMN attachment_secure_id VARCHAR(255) NULL AFTER attachment_storage_type;

-- Add index for secure file ID lookups
CREATE INDEX idx_chat_messages_attachment_secure_id ON chat_messages(attachment_secure_id);

-- Update existing records to generate secure file IDs for S3 files
-- This will be done by a separate script to avoid blocking the migration
