-- Migration: Add S3 fields to user_tickets table
-- This migration adds S3-related fields to support proper S3 file storage for support ticket attachments

-- Add S3 URL field
ALTER TABLE user_tickets 
ADD COLUMN attachment_s3_url VARCHAR(500) NULL 
AFTER attachment_secure_id;

-- Add S3 key field
ALTER TABLE user_tickets 
ADD COLUMN attachment_s3_key VARCHAR(500) NULL 
AFTER attachment_s3_url;

-- Add storage type field
ALTER TABLE user_tickets 
ADD COLUMN attachment_storage_type ENUM('local', 's3') NULL 
AFTER attachment_s3_key;

-- Add indexes for the new fields
CREATE INDEX idx_user_tickets_attachment_s3_key ON user_tickets(attachment_s3_key);
CREATE INDEX idx_user_tickets_attachment_storage_type ON user_tickets(attachment_storage_type);

-- Update existing records to set storage type based on attachment URL
UPDATE user_tickets 
SET attachment_storage_type = 's3' 
WHERE attachment_url IS NOT NULL 
  AND attachment_url LIKE '%s3%';

UPDATE user_tickets 
SET attachment_storage_type = 'local' 
WHERE attachment_url IS NOT NULL 
  AND attachment_url NOT LIKE '%s3%';
