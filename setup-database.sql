-- TheInfini AI Backend Database Setup Script
-- Run this script as MySQL root user to set up the databases and users

-- Create databases
CREATE DATABASE IF NOT EXISTS infini_ai_users;
CREATE DATABASE IF NOT EXISTS infini_ai_user_chat_recs;

-- Create users
CREATE USER IF NOT EXISTS 'inf_ai_user'@'localhost' IDENTIFIED BY 'S1nR1j@inf*';
CREATE USER IF NOT EXISTS 'inf_ai_chat_recs'@'localhost' IDENTIFIED BY 'S1nR1j@inf*';

-- Grant privileges
GRANT ALL PRIVILEGES ON infini_ai_users.* TO 'inf_ai_user'@'localhost';
GRANT ALL PRIVILEGES ON infini_ai_user_chat_recs.* TO 'inf_ai_chat_recs'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Show created databases
SHOW DATABASES;

-- Show users
SELECT User, Host FROM mysql.user WHERE User IN ('inf_ai_user', 'inf_ai_chat_recs');
